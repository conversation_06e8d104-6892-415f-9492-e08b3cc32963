package com.birdeye.messenger.service.impl;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.external.service.SpamDetectionService;
import com.birdeye.messenger.util.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.WriteRequest.RefreshPolicy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.constant.MessengerConstants;
import com.birdeye.messenger.dao.entity.Email;
import com.birdeye.messenger.dao.entity.FacebookMessage;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.ResponseTimeOutBox;
import com.birdeye.messenger.dao.repository.FacebookMessageRepository;
import com.birdeye.messenger.dao.repository.MessengerContactRepository;
import com.birdeye.messenger.dao.repository.ResponseTimeOutBoxRepository;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.ContactDocument.Appointment;
import com.birdeye.messenger.dto.elastic.ContactDocument.Payment;
import com.birdeye.messenger.dto.elastic.ContactDocument.Review;
import com.birdeye.messenger.dto.elastic.ContactDocument.SurveyResponse;
import com.birdeye.messenger.dto.elastic.ConversationIds;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.dto.elastic.MessageDocument.MediaFile;
import com.birdeye.messenger.dto.elastic.MessageDocumentTemp;
import com.birdeye.messenger.dto.facebook.GetUserDetailsMessage;
import com.birdeye.messenger.dto.facebook.UserDetailsMessage;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.LeadSource;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.enums.MessengerTagEnum;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.enums.WebhookEventEnum;
import com.birdeye.messenger.es.sro.ESFindByIdRequest;
import com.birdeye.messenger.es.sro.ESQueryBuilderRequest;
import com.birdeye.messenger.es.sro.ESUpdateByQueryRequest;
import com.birdeye.messenger.es.sro.ESUpsertRequest;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.external.service.SocialService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.ConversationActivityService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.MessengerMediaFileService;
import com.birdeye.messenger.service.RedisHandler;
import com.birdeye.messenger.service.WebhookService;
import com.birdeye.messenger.sro.ReviewEvent;
import com.birdeye.messenger.sro.SurveyEvent;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class MessengerContactServiceImpl implements MessengerContactService {

    private final MessengerContactRepository messengerContactRepository;
    private final RedisHandler redisHandler;
    private final FacebookMessageRepository facebookMessageRepository;
    private final SocialService socialService;
    private final MessengerMediaFileService mediaFileService;
    private final ContactService contactService;
    // TODO: USED only for userDTO population, refactoring required.
    private final CommunicationHelperService communicationHelperService;
    private final WebhookService webhookService;

    private final MessengerMessageService messengerMessageService;

    private final ResponseTimeOutBoxRepository responseTimeOutBoxRepository;

    private final ElasticSearchExternalService elasticSearchService;
    @Autowired
    @Lazy
    private  SpamDetectionService spamDetectionService;

    @Autowired
    @Lazy
    private ConversationActivityService conversationActivityService;

    @Autowired
    @Lazy
    private CommonService commonService;
    
    public static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * TODO: Revisit - Webchat specific handling, Refactor for generic use.
     * 
     * 1. FindOrCreate Messenger Contact 2. Encrypt last message and update
     * messenger contact. 3. ES push to Conversation & Messages indices.
     * 
     **/
    @Override
    public MessengerContact findOrCreate(BusinessDTO business, Integer tag, CustomerDTO customer) {
        MessengerContact messengerContact = messengerContactRepository.findByCustomerId(customer.getId())
                .orElseGet(() -> {
                    MessengerContact newContact = new MessengerContact();
                    newContact.setBusinessId(business.getBusinessId());
                    newContact.setCustomerId(customer.getId());
                    newContact.setTag(tag);
                    if (tag != null && tag.equals(5)) {
                        newContact.setIsRead(true);
                    }
                    newContact.setCreatedAt(new Date());
                    // Default date to avoid current date set as default by DB.
                    newContact.setLastResponseAt(new Date(5000));
                    newContact.setIsNew(true);
                    return newContact;
                });
        return messengerContact;
    }

    private void updateContactWithSMS(MessengerContact messengerContact, BusinessDTO business,
            CustomerDTO customer, SmsDTO sms) {
        Integer encrypted = sms.getEncrypted();
        // Update Last message metadata.
        if (StringUtils.isEmpty(sms.getMessageBodyUnencrypted()) && StringUtils.isNotEmpty(sms.getMediaURL())) {
            messengerContact.setLastMessage(ATTCH_MESSAGE);
        } else {
            messengerContact.setLastMessage(sms.getMessageBodyUnencrypted());
        }
        if (messengerContact.isCampaignOnly()) {
            messengerContact.setLastMessage("");
        }
        // TODO: Required fields: customer -> phone , business -> businessNumber
        boolean isLastMsgEncrypted = encryptLastMessage(business, sms, messengerContact, customer);
        encrypted = isLastMsgEncrypted ? 1 : 0;
        messengerContact.setEncrypted(encrypted);
        // TODO: Last Message Meta data
        messengerContact.setLastMsgOn(sms.getSentOn());
        messengerContact.setUpdatedAt(sms.getSentOn());
        // Save Messenger Contact.
        messengerContact = messengerContactRepository.saveAndFlush(messengerContact);

    }

    @Transactional
    @Override
    public MessengerData processMessengerContactForCampaignSms(BusinessDTO business, UserDTO campaignUser,
            CustomerDTO customer, SmsDTO sms) {
        Integer tagCode = MessageTag.CAMPAIGN.getCode();
        MessengerContact messengerContact = findOrCreate(business, tagCode, customer);
        // For campaign path we dont want to change Conversation tag.
        // Last message handling.
        if (messengerContact.isCampaignOnly()) {
            messengerContact.setIsRead(true);
            updateLastMessageMetaForCampaign(messengerContact, campaignUser, Source.SMS.name());
            updateContactWithSMS(messengerContact, business, customer, sms);
        } else {
            messengerContact.setUpdatedAt(new Date());
            messengerContactRepository.save(messengerContact);
        }
        messengerContact.setLead(customer.isLead());
        messengerContact.setLeadSource(customer.getLeadSource());
        messengerContact.setContactState(customer.getContactState());
        sms.setMessengerContactId(messengerContact.getId());
        // TODO: for applicable cases, get MessengerMediaFile for sms_id
        MessengerMediaFileDTO messengerMediaFileDTO = mediaFileService.saveMedia(sms);

        // ES update for Conversation. Ideally should be done separately in a
        // separate task.
        ContactDocument conversation = updateContactOnESWithRefresh(messengerContact, customer, business,
                MessageTag.getMessageTagById(messengerContact.getTag()),
                messengerContact.isCampaignOnly() ? campaignUser : null, false);

        // ES update for new message.
        MessageDocumentDTO messageDocumentDTO = new MessageDocumentDTO(sms, conversation.getM_c_id());
        // Adding stats for Campaign sms and Event sync time difference
        Long eventProcessingTime = new Date().getTime();
        messageDocumentDTO.setEventProcessingTimeStamp(eventProcessingTime);
        // convert millisec to min
        Long elapsedTimeInMin = (eventProcessingTime - sms.getSentOn().getTime()) / 60000;
        messageDocumentDTO.setTimeElapsedInProcessing(elapsedTimeInMin);

        MessageDocument messageDocument = addNewMessageOnEsWithRefresh(messageDocumentDTO,
                messengerMediaFileDTO, campaignUser, business, MessengerEvent.SMS_SEND, false);

        log.debug("[Campaign SMS] ES data push for Messages with m_c_id#{} and m_id#{} is done.",
                new Object[] { messageDocument.getC_id(), messageDocument.getM_id() });
        MessengerData data = new MessengerData();
        data.setContactDocument(conversation);
        data.setMessageDocument(messageDocument);
        data.setMessengerContact(messengerContact);
        return data;
    }

    private void updateLastMessageMetaForCampaign(MessengerContact messengerContact, UserDTO campaignUser,
            String channel) {
        LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
        lastMessageMetadataPOJO.setLastMessageType("SEND");
        lastMessageMetadataPOJO.setLastMessageUserId(campaignUser.getId());
        if (Source.SMS.name().equals(channel)) {
            lastMessageMetadataPOJO.setLastMessageSource(Source.SMS.getSourceId());
        } else if (Source.EMAIL.name().equals(channel)) {
            lastMessageMetadataPOJO.setLastMessageSource(Source.EMAIL.getSourceId());
        }

        lastMessageMetadataPOJO.setLastMessageUserName(MessengerUtil.buildUserName(campaignUser));
        messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));
    }

    @Override
    @Transactional
    public MessengerData processMessengerContactForVoiceCall(BusinessDTO business, CustomerDTO customer, SmsDTO sms) {
        MessengerContact messengerContact = findOrCreate(business, MessageTag.INBOX.getCode(), customer);

        // Last message meta data
        LastMessageMetaData lastMessageMetaData = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
        lastMessageMetaData.setLastMessageType(MessageDocument.CommunicationDirection.SEND.toString());
        messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetaData));
        // setting isRead false and viewedBy to null as a new message is received
        messengerContact.setIsRead(false);
        messengerContact.setViewedBy(null);
        updateContactWithSMS(messengerContact, business, customer, sms);
        sms.setMessengerContactId(messengerContact.getId());

        // TODO: Auto reply case.
        UserDTO userDTO = new UserDTO();
        userDTO.setId(-1);

        // ES update for Conversation. Ideally should be done separately in a separate
        // task.
        ContactDocument conversation = updateContactOnES(messengerContact, customer, business,
                MessageTag.getMessageTagById(messengerContact.getTag()), userDTO);

        // TODO: for applicable cases, get MessengerMediaFile for sms_id . This will be
        // available for recording.
        MessengerMediaFileDTO messengerMediaFileDTO = null;

        // Message doc is using 0 to identify
        userDTO.setId(0);
        // ES update for new message.
        MessageDocument messageDocument = andNewMessageOnEs(new MessageDocumentDTO(sms, conversation.getM_c_id()),
                messengerMediaFileDTO, userDTO, business, MessengerEvent.SMS_SEND);

        log.info("[Voicemail callback] ES data push for Messages with m_c_id#{} and m_id#{} is done.",
                new Object[] { messageDocument.getC_id(), messageDocument.getM_id() });

        MessengerData data = new MessengerData();
        data.setContactDocument(conversation);
        data.setMessageDocument(messageDocument);

        return data;
    }

    @Override
    @Transactional
    public MessengerData processMessengerContactForWebChat(BusinessDTO business, CustomerDTO customer,
                                                           LiveChatMessageObject liveChatMessageObject, WebchatMessageDTO request) {

        MessengerContact messengerContact = findOrCreate(business, MessageTag.INBOX.getCode(), customer);

        // IMP: WEBCHAT messenger contact should be with UNREAD tag
        messengerContact.setTag(MessageTag.UNREAD.getCode());
        // setting isRead false and viewedBy to null as a new message is received from
        // webchat
        messengerContact.setIsRead(false);
        messengerContact.setViewedBy(null);
        LastMessageMetaData lastMessageMetaData = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
        lastMessageMetaData.setLastMessageType(MessageDocument.CommunicationDirection.RECEIVE.toString());
        lastMessageMetaData.setLastReceivedMessageSource(liveChatMessageObject.getSource());
        messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetaData));
        messengerContact.setLead(customer.isLead());
        messengerContact.setLeadSource(customer.getLeadSource());
        messengerContact.setContactState(customer.getContactState());
        updateContactWithLiveChat(messengerContact, business, customer, liveChatMessageObject);

        if (request.getMessage() != null) {
            request.setBusinessDTO(business);
            request.setCustomerDTO(customer);
            request.setMessengerContact(messengerContact);
            request.setSource(request.getSource());
            spamDetectionService.spamDetectionAllChannels(request, messengerContact, customer, MessageTag.UNREAD);
        }

        liveChatMessageObject.setMessengerContactId(messengerContact.getId());
        if(StringUtils.isNotBlank(liveChatMessageObject.getDevice())) {
            messengerContact.setDevice(liveChatMessageObject.getDevice());
        }
        // ES update for Conversation. Ideally should be done separately in a separate
        // task.
        ContactDocument conversation = updateContactOnES(messengerContact, customer, business,
                MessageTag.getMessageTagById(messengerContact.getTag()), null);

        // TODO: System user can be used.
        UserDTO userDTO = null;

        // TODO: for applicable cases, get MessengerMediaFile for sms_id
        MessengerMediaFileDTO messengerMediaFileDTO = null;

        if (customer.getBlocked()) {
            liveChatMessageObject.setSpam(true);
        }

        // ES update for new message.
        MessageDocumentDTO messageDocumentDTO = new MessageDocumentDTO(liveChatMessageObject, conversation.getM_c_id());
        commonService.updateContactFiltersInMessage(conversation, messageDocumentDTO);
        MessageDocument messageDocument = andNewMessageOnEs(messageDocumentDTO,
                messengerMediaFileDTO, userDTO, business, MessengerEvent.SMS_RECEIVE);

        log.info("[Webchat SMS] ES data push for Messages with m_c_id#{} and m_id#{} is done.",
                new Object[] { messageDocument.getC_id(), messageDocument.getM_id() });

        MessengerData data = new MessengerData();
        data.setContactDocument(conversation);
        data.setMessageDocument(messageDocument);
        data.setMessengerContact(messengerContact);
        return data;

    }

    private void updateContactWithLiveChat(MessengerContact messengerContact, BusinessDTO business,
            CustomerDTO customer, LiveChatMessageObject liveChatMessageObject) {

        Integer encrypted = liveChatMessageObject.getEncrypted();
        // Update Last message metadata.
        if (StringUtils.isEmpty(liveChatMessageObject.getMessageBodyUnencrypted())
                && StringUtils.isNotEmpty(liveChatMessageObject.getMediaURL())) {
            messengerContact.setLastMessage(ATTCH_MESSAGE);
        } else {
            messengerContact.setLastMessage(liveChatMessageObject.getMessageBodyUnencrypted());
        }
        if (messengerContact.isCampaignOnly()) {
            messengerContact.setLastMessage("");
        }
        // TODO: Required fields: customer -> phone , business -> businessNumber
        boolean isLastMsgEncrypted = encryptLastMessage(business, liveChatMessageObject, messengerContact,
                customer.getPhone());
        encrypted = isLastMsgEncrypted ? 1 : 0;
        messengerContact.setEncrypted(encrypted);
        // TODO: Last Message Meta data
        messengerContact.setLastMsgOn(liveChatMessageObject.getCreateDate());
        messengerContact.setUpdatedAt(liveChatMessageObject.getCreateDate());
        // Save Messenger Contact.
        messengerContact = messengerContactRepository.saveAndFlush(messengerContact);
        messengerContact.setLastIncomingMessageTime(liveChatMessageObject.getCreateDate().getTime());

    }

    /**
     * For messenger contact, encryption needs to be done with Customer Phone number
     * & Busines Long number This is different from Messages where we can use
     * existing SMS data.
     * 
     * @param messengerContact
     *                              - MessengerContact to be updated
     * @param liveChatMessageObject
     *                              - Current SMS message
     * @return boolean - if encryption is successful.
     */
    boolean encryptLastMessage(BusinessDTO businessDTO, SmsDTO sms, MessengerContact messengerContact,
            String customerPhoneNo) {
        boolean isEncrypted = false;
        boolean smsEncrypted = sms.getEncrypted() != null && sms.getEncrypted() == 1;
        if (MessengerUtil.isEncryptionEnabled() && smsEncrypted
                && StringUtils.isNotEmpty(messengerContact.getLastMessage())) {
            try {
                Long businessNumber = businessDTO.getBusinessNumber();
                String encryptedMessage = EncryptionUtil.encrypt(messengerContact.getLastMessage(),
                        StringUtils.join(businessNumber, customerPhoneNo),
                        StringUtils.join(customerPhoneNo, businessNumber));
                messengerContact.setLastMessage(encryptedMessage);
                messengerContact.setEncrypted(1);
                isEncrypted = true;
            } catch (Exception e) {
                log.error("[encryptLastMessage] Failed to encrypt the message, saving as unencrypted ", e);
                messengerContact.setEncrypted(0);
            }
        }
        return isEncrypted;
    }

    @Override
    public boolean encryptLastMessage(BusinessDTO businessDTO, SmsDTO sms, MessengerContact messengerContact,
            CustomerDTO customer) {
        return encryptLastMessage(businessDTO, sms, messengerContact, customer.getPhone());
    }

    @Override
    public MessengerContact findById(Integer id) {
        long startTime = System.currentTimeMillis();
        MessengerContact messengerContact = messengerContactRepository.findById(id).orElse(null);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("findMessengerContact", startTime, endTime);
        if (Objects.isNull(messengerContact)) {
            log.error("[ MESSENGER_CONTACT_ERROR ] ES contact not found in DB {} ", id);
        }
        return messengerContact;
    }

    @Override
    @Transactional
    public MessengerContact getOrCreateContactForExistingCustomer(Integer businessId, Integer customerId,
            Integer accountId) {
        return messengerContactRepository.findByCustomerId(customerId).orElseGet(() -> {
            MessengerContact contact = createContact(customerId, businessId);
            //should we add check for anonymous here ?
            publishConversationAddedEvent(accountId, contact);
            return contact;
        });
    }

    private void publishConversationAddedEvent(Integer accountId, MessengerContact savedMessengerContact) {
        ConversationWebhookEventDTO conversationWebhookDTO = webhookService
                .mapMessengerContactToConversationWebhookDTO(savedMessengerContact);
        webhookService.publishMessengerWebhookEvent(conversationWebhookDTO, accountId,
                WebhookEventEnum.CONVERSATION_CREATED.getName(), savedMessengerContact.getBusinessId());
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public MessengerContact getOrCreateContact(Integer businessId, Integer contactId, Integer accountId) {
        return messengerContactRepository.findById(contactId).orElseGet(() -> {
            MessengerContact newContact = new MessengerContact();
            newContact.setBusinessId(businessId);
            newContact.setTag(MessageTag.INBOX.getCode());
            newContact.setCreatedAt(new Date());
            log.info("getOrCreateContact : new messengerContact created");
            MessengerContact savedMessengerContact = messengerContactRepository.saveAndFlush(newContact);
            publishConversationAddedEvent(accountId, savedMessengerContact);
            return savedMessengerContact;
        });
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public MessengerContact saveOrUpdateMessengerContact(MessengerContact contact) {
        return messengerContactRepository.saveAndFlush(contact);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public MessengerData saveOrUpdateMessengerContact(MessengerContact contact, ResponseTimeOutBox responseTimeOutBox) {
        if (Objects.nonNull(responseTimeOutBox)) {
            responseTimeOutBox = responseTimeOutBoxRepository.save(responseTimeOutBox);
        }
        MessengerContact messengerContact = messengerContactRepository.saveAndFlush(contact);
        MessengerData messengerData = new MessengerData();
        messengerData.setMessengerContact(messengerContact);
        messengerData.setResponseTimeOutBox(responseTimeOutBox);
        return messengerData;
    }

    @Override
    public List<ContactDocument> getContactFromES(MessangerBaseFilter messengerQueryFilter) {
        Map<String, Object> dataModel = new HashMap<>();
        dataModel.put("contactFilter", messengerQueryFilter);
        ESRequest esRequest = new ESRequest.Builder(new ESRequest())
                .addIndex(Constants.Elastic.CONTACT_INDEX)
                .addRoutingId(messengerQueryFilter.getAccountId())
                .addSize(messengerQueryFilter.getCount())
                .addTemplateAndDataModel(Constants.Elastic.GET_CONTACT, dataModel).build();
        return elasticSearchService.searchByQuery(esRequest, ContactDocument.class);
    }

    @Override
    public List<ContactDocument> getConversationFromES(MessangerBaseFilter messengerQueryFilter) {
        Map<String, Object> dataModel = new HashMap<>();
        dataModel.put("contactFilter", messengerQueryFilter);
        ESRequest esRequest = new ESRequest.Builder(new ESRequest())
                .addIndex(Constants.Elastic.CONTACT_INDEX)
                .addRoutingId(messengerQueryFilter.getAccountId())
                .addSize(messengerQueryFilter.getCount())
                .addTemplateAndDataModel(messengerQueryFilter.getQueryFile(), dataModel).build();
        return elasticSearchService.searchByQuery(esRequest, ContactDocument.class);
    }

    @Override
    public List<MessageDocument> getMessagesFromES(MessangerBaseFilter messengerQueryFilter) {
        ESRequest esRequest = buildESRequestForMessage(messengerQueryFilter);
        return elasticSearchService.searchByQuery(esRequest, MessageDocument.class);
    }

    /**
     * This method has a DB fallback to get contact info from DB in case its not yet
     * available on ES. Why not available ? ES takes some time to replicate data
     * across cluster and newly created data may not be instantly available
     */
    @Override
    public ContactDocument getContact(MessengerGlobalFilter filter) {
        log.info("MessengerGlobalFilter Object {}:", filter);
        List<ContactDocument> contactFromES = getContactFromES(filter);
        // contact info coming null sometimes because of replication delay in ES hence
        // this fallback
        if (CollectionUtils.isEmpty(contactFromES)) {
            log.info("ES Contact is null, pulling details for Messenger Contact from DB {}",
                    filter.getConversationId());
            ContactDocument document = new ContactDocument();
            if (filter.getCustomerId() != null) {

                CustomerDTO customerDto = contactService.findById(filter.getCustomerId());
                if (customerDto != null) {
                    document.setC_email(customerDto.getEmailId());
                    document.setC_phone(customerDto.getPhone());
                    document.setC_name(customerDto.getPhone());

                    if (customerDto.getFirstName() != null)
                        document.setC_name(customerDto.getFirstName());
                }
            } else {
                long startTime = System.currentTimeMillis();
                List<FacebookMessage> facebookMessages = facebookMessageRepository
                        .getMessageByCustomerId(filter.getConversationId());
                long endTime = System.currentTimeMillis();
                LogUtil.logExecutionTime("getFacebookMessage", startTime, endTime);
                if (CollectionUtils.isNotEmpty(facebookMessages)) {
                    FacebookMessage facebookMessage = facebookMessages.get(0);
                    UserDetailsMessage userDetailsMessage = socialService.getFacebookUserInfo(new GetUserDetailsMessage(
                            facebookMessage.getRecipientFacebookId(), facebookMessage.getSenderFacebookId()));
                    log.info("FB user details from social {}:", userDetailsMessage);
                    document.setC_name(userDetailsMessage.getFirst_name());
                }
            }
            return document;
        }
        ContactDocument document = contactFromES.get(0);
        if (StringUtils.isEmpty(document.getC_name())) {
            document.setC_name(document.getC_phone());
        }
        return document;
    }

    @Override
    public ContactDocument updateContactOnES(MessengerContact messengerContact, CustomerDTO customerDTO,
            BusinessDTO businessDTO, MessageTag messageTag, UserDTO userDTO) {
        return updateContactOnESWithRefresh(messengerContact, customerDTO, businessDTO, messageTag, userDTO, true);
    }

    @Override
    public MessageDocument andNewMessageOnEs(MessageDocumentDTO messageDocumentDTO,
            MessengerMediaFileDTO messengerMediaFileDTO, UserDTO userDTO, BusinessDTO businessDTO,
            MessengerEvent event, boolean isSpam) {
        return addNewMessageOnEsWithRefresh(messageDocumentDTO, messengerMediaFileDTO, userDTO, businessDTO, event,
                isSpam, true);
    }

    @Override
    public MessageDocument andNewMessageOnEs(MessageDocumentDTO messageDocumentDTO,
            MessengerMediaFileDTO messengerMediaFileDTO, UserDTO userDTO, BusinessDTO businessDTO,
            MessengerEvent event) {
        return addNewMessageOnEsWithRefresh(messageDocumentDTO, messengerMediaFileDTO, userDTO, businessDTO, event,
                true);
    }

    @Override
    public Integer getLastReceivedMessageSource(Integer contactId, Integer routeId) {
        MessengerFilter messengerFilter = new MessengerFilter();
        messengerFilter.setStartIndex(0);
        messengerFilter.setCount(1);
        messengerFilter.setConversationId(contactId);
        messengerFilter.setAccountId(routeId);
        List<String> typeList = new ArrayList<>();
        typeList.add(MessengerEvent.SMS_RECEIVE.name());
        typeList.add(MessengerEvent.MMS_RECEIVE.name());
        typeList.add(MessengerEvent.LIVECHAT_RECEIVE.name());
        typeList.add(MessengerEvent.LIVECHAT_MEDIA_RECEIVE.name());
        typeList.add(MessengerEvent.EMAIL_RECEIVE.name());
        Map<String, Object> params = new HashMap<>();
        params.put("msg_type", ControllerUtil.getJsonTextFromObject(typeList));
        messengerFilter.setParams(params);
        List<MessageDocument> messsages = getMessagesFromES(messengerFilter);
        log.info("#### getLastReceivedMessageSource documents from es: {}", messsages);

        if (CollectionUtils.isNotEmpty(messsages)) {
            return messsages.get(0).getSource();
        }
        return 0;
    }

    @Override
    public Integer getLastReceivedMessageSource(MessengerContact messengerContact, Integer routeId) {
        // Getting last message metadata
        LastMessageMetaData lastMessageMetaData = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
        if (lastMessageMetaData == null || lastMessageMetaData.getLastReceivedMessageSource() == null) {
            // fallback get from es
            lastMessageMetaData
                    .setLastReceivedMessageSource(getLastReceivedMessageSource(messengerContact.getId(), routeId));
            // TODO: remove this comment once all SEND and RECEIVE api migrated to
            // messenger-service
            // messengerContact.setLastMessageMetaData(ControllerUtil.getJsonTextFromObject(lastMessageMetaData));
            messengerContactRepository.save(messengerContact);
            log.info("#### findSource lastReceivedSourceId from es: {}",
                    lastMessageMetaData.getLastReceivedMessageSource());
        }
        log.info("Messenger Contact {} LastReceivedMessageSource is:{} ", messengerContact.getId(),
                lastMessageMetaData.getLastReceivedMessageSource());
        return lastMessageMetaData.getLastReceivedMessageSource();
    }

    @Transactional(readOnly = true)
    @Override
    public List<MessengerContact> findAllByUpdatedAtAfter(Date updatedAt) {
        return messengerContactRepository.findAllByUpdatedAtAfter(updatedAt);
    }

    @Override
    public MessengerContact findByCustomerId(Integer customerId) {
        return messengerContactRepository.findByCustomerId(customerId).orElse(null);
    }

    @Override
    @Transactional
    public boolean deleteMessengerContact(Integer id) {
        try {
            messengerContactRepository.deleteById(id);
            return true;
        } catch (Exception e) {
            log.error("error : {} occurred in deleteMessengerContact", e.getMessage());
            return false;
        }
    }

    @Override
    public boolean updateContactOnES(Integer messengerContactId, ContactDocument contactFromES, Integer routingId) {
        contactFromES.setLastUpdateDate((new Date()).getTime());
        return upsertContactDocumentOnESWithRefresh(contactFromES, String.valueOf(messengerContactId), routingId, true);
    }

    @Override
    public void updateMessageOnES(MessageDocument messageDocument, Integer routingId) {
        messageDocument.setLastUpdateDate((new Date()).getTime());
        ESRequest.Upsert<MessageDocument> documentForUpsert = new ESRequest.Upsert<>(messageDocument,
                messageDocument.getId().toString());
        ESRequest.Builder esRequestBuilder = new ESRequest.Builder(new ESRequest());
        ESRequest esRequest = esRequestBuilder.addIndex(Constants.Elastic.MESSAGE_INDEX)
                .addRoutingId(routingId)
                .addTemplateAndDataModel(null, null).addPayloadForUpsert(documentForUpsert).build();
        elasticSearchService.updateDocument(esRequest, true);
    }

    @Override
    public List<MessengerContact> getContactIdsForCustomerIds(List<Integer> customerIds) {
        if (CollectionUtils.isNotEmpty(customerIds)) {
            return messengerContactRepository.findContactsByCustomerIds(customerIds);
        }
        log.info("getContactIdsForCustomerIds: method argument empty {} ", customerIds);
        return new ArrayList<>();
    }

    @Override
    public ElasticData getMessageData(MessangerBaseFilter messengerQueryFilter) {
        ESRequest esRequest = buildESRequestForMessage(messengerQueryFilter);
        return elasticSearchService.getDataFromElastic(esRequest, MessageDocument.class);
    }

    private ESRequest buildESRequestForMessage(MessangerBaseFilter messengerQueryFilter) {
        Map<String, Object> dataModel = new HashMap<>();
        dataModel.put("messageFilter", messengerQueryFilter);
        return new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
                .addRoutingId(messengerQueryFilter.getAccountId())
                .addSize(messengerQueryFilter.getCount())
                .addTemplateAndDataModel(messengerQueryFilter.getQueryFile(), dataModel).build();
    }

    @Override
    public boolean updateContactDocumentOnES(ContactDocument contactDocument, String documentId, Integer routingId) {
        return upsertContactDocumentOnESWithRefresh(contactDocument, documentId, routingId, true);
    }

    @Override
    public boolean upsertContactDocumentOnES(ContactDocument contactDocument, String documentId, Integer routingId) {
        return upsertContactDocumentOnESWithRefresh(contactDocument, documentId, routingId, true);
    }

    @Override
    public ElasticData getConversationData(ContactFreeMarkerData data, Integer accountId) {
        ESRequest esRequest = buildESRequestForConversations(data, accountId);
        return elasticSearchService.getDataFromElastic(esRequest, ContactDocument.class);
    }

    @Override
    public ElasticData<ContactDocument> getConversationDataWithInnerHits(ContactFreeMarkerData data,
            Integer accountId) {
        // TODO - We need to generalize this method more for reusability
        ESRequest esRequest = buildESRequestForConversations(data, accountId);
        Map<String, Class<?>> nestedObjectMap = new HashMap<>();
        nestedObjectMap.put("reviews", Review.class);
        nestedObjectMap.put("surveyResponses", SurveyResponse.class);
        nestedObjectMap.put("payments", Payment.class);
        nestedObjectMap.put("appointments", Appointment.class);
        return elasticSearchService.getDataFromElasticWithInnerHits(esRequest, ContactDocument.class);
    }

    private ESRequest buildESRequestForConversations(ContactFreeMarkerData data, Integer accountId) {
        Map<String, Object> dataModel = new HashMap<>();
        dataModel.put("data", data);
        return new ESRequest.Builder(new ESRequest())
                .addIndex(Constants.Elastic.CONTACT_INDEX)
                .addRoutingId(accountId)
                .addSize(data.getSize())
                .addTemplateAndDataModel(data.getFtlFile(), dataModel).build();
    }

    @Override
    public MessageDocument addNewMessageDocOnES(MessageDocument document, String documentId) {
        ESRequest.Upsert<MessageDocument> documentForUpsert = new ESRequest.Upsert<>(document, documentId);
        ESRequest.Builder esRequestBuilder = new ESRequest.Builder(new ESRequest());
        ESRequest esRequest = esRequestBuilder.addIndex(Constants.Elastic.MESSAGE_INDEX)
                .addRoutingId(document.getE_id())
                .addTemplateAndDataModel(null, null).addPayloadForUpsert(documentForUpsert).build();
        elasticSearchService.updateDocument(esRequest, true);
        redisHandler.updateLastEventCache(String.valueOf(document.getE_id()));
        return document;
    }

    @Override
    @Transactional
    public MessengerData upsertMessengerContact(BusinessDTO business, Integer tag, CustomerDTO customer,String device) {

        MessengerContact messengerContact = messengerContactRepository.findByCustomerId(customer.getId())
                .orElseGet(() -> {
                    MessengerContact newMessengerContact = new MessengerContact();
                    newMessengerContact.setBusinessId(business.getBusinessId());
                    newMessengerContact.setCustomerId(customer.getId());
                    newMessengerContact.setTag(tag);
                    newMessengerContact.setUpdatedAt(new Date());
                    newMessengerContact.setCreatedAt(new Date()); // Default date to avoid current date set as default
                                                                  // by DB.
                    newMessengerContact.setLastResponseAt(new Date(5000)); // To take back into time, so that it gets
                                                                           // picked for an Email notification
                    LastMessageMetaData lastMessageMetaData = new LastMessageMetaData();
                    lastMessageMetaData.setLastMessageSource(Source.LIVE_CHAT_RECEIVE.getSourceId());
                    newMessengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetaData));
                    MessengerContact savedMessengerContact = messengerContactRepository.save(newMessengerContact);
                    savedMessengerContact.setIsNew(true);
                    publishConversationAddedEvent(business.getAccountId(), savedMessengerContact);
                    return savedMessengerContact;
                });
        // push contact document on ES
        if(StringUtils.isNotBlank(device)){
            messengerContact.setDevice(device);
        }
        messengerContact.setLastIncomingMessageTime(new Date().getTime());
        ContactDocument contactDocument = updateContactOnES(messengerContact, customer, business,
                MessageTag.getMessageTagById(messengerContact.getTag()),
                null);
        MessengerData messengerData = new MessengerData();
        messengerData.setMessengerContact(messengerContact);
        messengerData.setContactDocument(contactDocument);
        return messengerData;
    }

    @SuppressWarnings("unchecked")
    @Override
    public Entry<MessageDocument, ContactDocument> postLiveChatMessageReceiveActivity(BusinessDTO business,
            Integer mcId, LiveChatMessageObject liveChatMessageObject, MessengerEvent messengerEvent, String cPhone,
            MessageTag messageTag,
            Integer userId, CustomerDTO customerDTO, Boolean updateLastRespondedAt) {

        Map<String, Object> result = updateAndFetchMessengerContactAndMessageDocument(business, liveChatMessageObject,
                mcId,
                messengerEvent, cPhone, messageTag, userId, updateLastRespondedAt);

        Optional<MessengerContact> messengerContactOptional = (Optional<MessengerContact>) result.get("msgContactOpt");
        MessageDocumentDTO msgDoc = (MessageDocumentDTO) result.get("messageDocumentDTO");
        UserDTO userDto = (UserDTO) result.get("userDto");

        // This needs to be updated as per chatbot reply !!
        // Update messenger_contact table
        // messengerContact.setTag(MessageTag.UNREAD.getCode());
        MessengerContact messengerContact = messengerContactOptional.get();

        liveChatMessageObject.setMessengerContactId(mcId);
        if(StringUtils.isNotBlank(liveChatMessageObject.getDevice())) {
            messengerContact.setDevice(liveChatMessageObject.getDevice());
        }
        // ES update for Conversation. Ideally should be done separately in a separate
        // Not updating any customer information
        ContactDocument contactDocument = updateContactOnES(messengerContact, customerDTO, business,
                MessageTag.getMessageTagById(messengerContact.getTag()), userDto);
        MessageDocument messageDocument = updateMessageDocumentOnES(business, msgDoc, messengerEvent, userDto,
                contactDocument);

        return new AbstractMap.SimpleEntry<>(messageDocument, contactDocument);
    }

    private Map<String, Object> updateAndFetchMessengerContactAndMessageDocument(BusinessDTO business,
            LiveChatMessageObject liveChatMessageObject,
            Integer mcId, MessengerEvent messengerEvent, String cPhone, MessageTag messageTag, Integer userId,
            Boolean updateLastRepondedAt) {
        Integer encrypted = liveChatMessageObject.getEncrypted();

        // To return multiple values from a method.
        Map<String, Object> result = new HashMap<>();

        // This would upsert the messenger_contact on both ES and DB.
        Optional<MessengerContact> messengerContactOptional = messengerContactRepository.findById(mcId);
        if (messengerContactOptional.isPresent()) {

            MessengerContact messengerContact = messengerContactOptional.get();
            if (Objects.nonNull(userId) && BooleanUtils.isTrue(updateLastRepondedAt)) {
                messengerContact.setLastResponseAt(liveChatMessageObject.getSentOn());
            }
            if (StringUtils.isEmpty(liveChatMessageObject.getMessageBodyUnencrypted())
                    && StringUtils.isNotEmpty(liveChatMessageObject.getMediaURL())) {
                messengerContact.setLastMessage(ATTCH_MESSAGE);
            } else {
                messengerContact.setLastMessage(liveChatMessageObject.getMessageBodyUnencrypted());
            }
            // TODO: Required fields: customer -> phone , business -> businessNumber
            boolean isLastMsgEncrypted = encryptLastMessage(business, liveChatMessageObject, messengerContact, cPhone);
            encrypted = isLastMsgEncrypted ? 1 : 0;

            messengerContact.setEncrypted(encrypted);
            // TODO: Last Message Meta data
            messengerContact.setLastMsgOn(liveChatMessageObject.getSentOn());
            messengerContact.setUpdatedAt(liveChatMessageObject.getSentOn());
            messengerContact.setTag(messageTag.getCode());
            // setting isRead false and viewedBy to null as a new message is sent from
            // customer or chatbot
            if (CommunicationDirection.RECEIVE.equals(liveChatMessageObject.getCommunicationDirection())) {
                messengerContact.setIsRead(false);
                messengerContact.setViewedBy(null);
                messengerContact.setLastIncomingMessageTime(liveChatMessageObject.getSentOn().getTime());
            }
            UserDTO userDTO = null;
            if (Objects.nonNull(userId)) {
                userDTO = communicationHelperService.getUserDTO(userId);
            }

            LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
            if (Objects.nonNull(userDTO)) {
                if (Objects.nonNull(lastMessageMetadataPOJO)) {
                    lastMessageMetadataPOJO.setLastMessageType("SEND");
                    lastMessageMetadataPOJO.setLastMessageUserId(userDTO.getId());
                    lastMessageMetadataPOJO.setLastMessageUserName(MessengerUtil.buildUserName(userDTO));
                    if (userDTO.getId() != null && userDTO.getId() != -10) {
                        lastMessageMetadataPOJO.setLastMessageSource(liveChatMessageObject.getSource());
                    }
                }
                messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));
                result.put("userDto", userDTO);
            } else {
                if (Objects.nonNull(lastMessageMetadataPOJO)) {
                    lastMessageMetadataPOJO.setLastMessageType("RECEIVE");
                    lastMessageMetadataPOJO.setLastReceivedMessageSource(liveChatMessageObject.getSource());
                    lastMessageMetadataPOJO.setLastMessageSource(liveChatMessageObject.getSource());
                }
                messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));
            }
            // Save Messenger Contact.
            Long lastIncomingMessageTime = null;
            if(Objects.nonNull(messengerContact.getLastIncomingMessageTime())) {
                lastIncomingMessageTime = messengerContact.getLastIncomingMessageTime();
            }
            messengerContact = messengerContactRepository.saveAndFlush(messengerContact);
            if(Objects.nonNull(lastIncomingMessageTime)) {
                messengerContact.setLastIncomingMessageTime(lastIncomingMessageTime);
            }
            MessageDocumentDTO messageDocumentDTO = new MessageDocumentDTO(liveChatMessageObject, mcId);
            result.put("msgContactOpt", messengerContactOptional);
            result.put("messageDocumentDTO", messageDocumentDTO);
        }
        return result;
    }

    private boolean encryptLastMessage(BusinessDTO businessDTO, LiveChatMessageObject liveChatMessageObject,
            MessengerContact messengerContact, String customerPhoneNo) {

        boolean isEncrypted = false;
        boolean smsEncrypted = liveChatMessageObject.getEncrypted() != null
                && liveChatMessageObject.getEncrypted() == 1;
        if (MessengerUtil.isEncryptionEnabled() && smsEncrypted
                && StringUtils.isNotEmpty(messengerContact.getLastMessage())) {
            try {
                Long businessNumber = businessDTO.getBusinessNumber();
                String encryptedMessage = "";
                if (BooleanUtils.isTrue(liveChatMessageObject.getEmailMandatory())) {
                    encryptedMessage = EncryptionUtil.encrypt(messengerContact.getLastMessage(),
                            StringUtils.join(businessNumber, messengerContact.getId()),
                            StringUtils.join(messengerContact.getId(), businessNumber));
                }
                else if(StringUtils.isBlank(customerPhoneNo)){
                    encryptedMessage = EncryptionUtil.encrypt(messengerContact.getLastMessage(),
                            StringUtils.join(businessNumber, messengerContact.getId()),
                            StringUtils.join(messengerContact.getId(), businessNumber));
                } 
                else {
                    encryptedMessage = EncryptionUtil.encrypt(messengerContact.getLastMessage(),
                            StringUtils.join(businessNumber, customerPhoneNo),
                            StringUtils.join(customerPhoneNo, businessNumber));
                }
                messengerContact.setLastMessage(encryptedMessage);
                isEncrypted = true;
            } catch (Exception e) {
                log.error("[encryptLastMessage] Failed to encrypt the message, saving as unencrypted ", e);
                // TODO: For encryption failure case, consider having normal message instaed of
                // empty.
            }
        }
        return isEncrypted;

    }

    /**
     * Update Message Document on ES
     * 
     * @param business
     * @param liveChatMessageObject
     * @param mcId
     * @param messengerEvent
     */
    private MessageDocument updateMessageDocumentOnES(BusinessDTO business, MessageDocumentDTO messageDocumentDTO,
            MessengerEvent messengerEvent, UserDTO userDTO, ContactDocument contactDocument) {
        Integer routeId = business.getRoutingId();
        commonService.updateContactFiltersInMessage(contactDocument, messageDocumentDTO);
        // Update Last message metadata.
        MessageDocument document = new MessageDocument.Builder(new MessageDocument()).addMessageInfo(messageDocumentDTO)
                .addNoteInfo(messageDocumentDTO).addMessengerMediaFileInfo(null).addEventType(messengerEvent)
                .addSenderName(userDTO).addEId(business.getRoutingId()).addBusinessId(business.getBusinessId())
                .addClientIp(messageDocumentDTO).addSpam(messageDocumentDTO.getSpam()).addAnswerFlag(messageDocumentDTO.getAnswerFlag()).build();

        ESRequest.Upsert<MessageDocument> documentForUpsert = new ESRequest.Upsert<>(document,
                document.getM_id() + messageDocumentDTO.getSuffix());

        ESRequest.Builder esRequestBuilder = new ESRequest.Builder(new ESRequest());
        ESRequest esRequest = esRequestBuilder.addIndex(Constants.Elastic.MESSAGE_INDEX)
                .addRoutingId(routeId).addTemplateAndDataModel(null, null)
                .addPayloadForUpsert(documentForUpsert).build();

        elasticSearchService.updateDocument(esRequest, true);
        // TODO Why ??
        redisHandler.updateLastEventCache(routeId.toString());

        publishMessageAddedWebhookEvent(business, document);
        return document;
    }

    /**
     * @param business
     * @param document
     */
    private void publishMessageAddedWebhookEvent(BusinessDTO business, MessageDocument document) {
        Integer sourceId = document.getSource();
        if (Source.MISSED_CALL_AUTOREPLY.getSourceId() != sourceId
                && Source.VOICE_CALL_AUTOREPLY.getSourceId() != sourceId) {
            if (!(MessageDocument.MessageType.ACTIVITY.equals(document.getMessageType())
                    || MessageDocument.MessageType.EVENTS.equals(document.getMessageType()))) {
                MessageWebhookEventDTO messageWebhookEventDTO = webhookService
                        .mapMessageDocumentToMessageWebhookDTO(document);
                webhookService.publishMessengerWebhookEvent(messageWebhookEventDTO, business.getAccountId(),
                        WebhookEventEnum.MESSAGE_ADDED.getName(), business.getBusinessId());
            }
        }
    }

    /**
     * For all messenger events, We need to 1. Create/Update messenger contact for
     * specified customerId
     * 
     * 2. Apply tag.
     * 
     * 3. Last message, Encrypt if required.
     * 
     * 4. Add last message meta details
     * 
     */
    @Override
    @Transactional
    public MessengerContact updateLastMessage(MessengerContactDto messengerContactDto) {
        Integer businessId = messengerContactDto.getBusinessId();
        Integer customerId = messengerContactDto.getCustomerId();
        CustomerDTO customerDto = messengerContactDto.getCustomerDTO();
        // Find or create.
        MessengerContact messengerContact = getOrCreateMessengerContact(messengerContactDto);
        String existingContactLastMessage = messengerContact.getLastMessage();
        messengerContact.setLastMessage(messengerContactDto.getLastMessage());
        // Tag setup.
        messengerContact.setTag(messengerContactDto.getTag());

        boolean encrypted = false;
        String lastMessage = messengerContactDto.getLastMessage();
        if (messengerContactDto.isEncrypt() && StringUtils.isNotEmpty(lastMessage)) {
            // Encrypt & Store
            if (MessengerUtil.isEncryptionEnabled() && StringUtils.isNotEmpty(messengerContact.getLastMessage())) {
                try {
                    String customerPhone = messengerContactDto.getCustomerDTO().getPhone();
                    Long businessNumber = messengerContactDto.getBusinessDTO().getBusinessNumber();
                    String encryptedMessage = EncryptionUtil.encrypt(lastMessage,
                            StringUtils.join(businessNumber, customerPhone),
                            StringUtils.join(customerPhone, businessNumber));
                    messengerContact.setLastMessage(encryptedMessage);
                    messengerContact.setEncrypted(1);
                    encrypted = true;
                } catch (Exception e) {
                    log.error("Failed to encrypt the message for BID{} , CID{} saving as unencrypted {}", businessId,
                            customerId, lastMessage);
                }
            }
        }
        // If encryption is not required or result in failure, set unencrypted text
        if (!encrypted) {
            messengerContact.setEncrypted(0);
            messengerContact.setLastMessage(lastMessage);
        }

        // Last message meta data
        LastMessageMetaData lastMessageMetaData = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
        lastMessageMetaData.setLastMessageType(messengerContactDto.getLastMessageMetaData().getLastMessageType());
        if (messengerContactDto.getLastMessageMetaData().getLastMessageSource() != null) {
            lastMessageMetaData
                    .setLastMessageSource(messengerContactDto.getLastMessageMetaData().getLastMessageSource());
        }
        if (messengerContactDto.getLastMessageMetaData().getLastReceivedMessageSource() != null)
            lastMessageMetaData.setLastReceivedMessageSource(
                    messengerContactDto.getLastMessageMetaData().getLastReceivedMessageSource());
        messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetaData));
        messengerContact.setUpdatedAt(messengerContactDto.getLastUpatedAt());
        // setting isRead false and viewedBy to null as a new message is received from
        // voicemail
        if (lastMessageMetaData != null && MessageDocument.CommunicationDirection.RECEIVE.toString()
                .equals(lastMessageMetaData.getLastMessageType())) {
            messengerContact.setIsRead(false);
            messengerContact.setViewedBy(null);
        }

        // BLOCK SPAM CALLS
        // 1. RecordingDuration < 5s
        // 2. Customer name is not available
        // 3. lastMessage in messengerContact not null, already a valid conversation
        // 4. Do Not change ordering for existing conversations, if RecordingDuration <
        // 5s, Change for Missed Call
        // 5. RecordingDuration < 16s + No Contact Name + No Transcription
        
		// Check if the account is in the list of accounts where we don't want to hide
		// missed calls
		boolean showMissedCalls = false;
		Integer accountId = messengerContactDto.getBusinessDTO() != null
				? messengerContactDto.getBusinessDTO().getAccountId()
				: null;
		if (accountId != null) {
			String showMissedCallsAccounts = CacheManager.getInstance().getCache(SystemPropertiesCache.class)
					.getProperty(Constants.SHOW_ALL_VOICE_CALLS_ACCOUNTS, "261968");
			Set<Integer> showMissedCallsAccountsList = ControllerUtil.getTokensListIntegerFromString(showMissedCallsAccounts);
			showMissedCalls = showMissedCallsAccountsList != null
					&& showMissedCallsAccountsList.contains(accountId);
		}
        if (messengerContactDto.getLastMessageMetaData().getLastMessageSource() == Source.MISSED_CALL_AUTOREPLY
                .getSourceId()) {
            if (isValidMissedCall(messengerContactDto) || showMissedCalls) {
                messengerContact.setLastMsgOn(messengerContactDto.getLastActivity());
                messengerContact.setLastIncomingMessageTime(messengerContactDto.getLastActivity().getTime());
            }
            if (!isValidMissedCall(messengerContactDto) && StringUtils.isEmpty(existingContactLastMessage)&& !showMissedCalls) {
                messengerContact.setLastMessage("");
                messengerContact.setIsRead(true);
            }
        } else {
            if (isInvalidVoiceCall(messengerContactDto) && StringUtils.isEmpty(existingContactLastMessage)&& !showMissedCalls) {
                messengerContact.setLastMessage("");
                messengerContact.setIsRead(true);
            }
            if (!isInvalidVoiceCall(messengerContactDto) || showMissedCalls) {
                messengerContact.setLastMsgOn(messengerContactDto.getLastActivity());
                messengerContact.setLastIncomingMessageTime(messengerContactDto.getLastActivity().getTime());
            }
        }

        Long lastIncomingMessageTime = messengerContact.getLastIncomingMessageTime();

        // Save Messenger Contact.
        messengerContact = messengerContactRepository.saveAndFlush(messengerContact);
        if ((Constants.INBOX_DEFENDER_USER.equals(messengerContact.getSpamMarkedBy())
                || messengerContact.getSpamMarkedBy() == null)
                && (messengerContactDto.getCustomerDTO().isNewContact()
                        || messengerContactDto.getCustomerDTO().getBlocked())
                && messengerContactDto.getLastMessageMetaData().getLastMessageSource() != Source.MISSED_CALL_AUTOREPLY
                        .getSourceId()) {
            updateSpamAndBlock(messengerContact, messengerContactDto.getSpam(), messengerContactDto);
            messengerContact = messengerContactRepository.saveAndFlush(messengerContact);
        }
        messengerContact.setLastIncomingMessageTime(lastIncomingMessageTime);
        return messengerContact;
    }

    private boolean isValidMissedCall(MessengerContactDto messengerContactDto) {
        CustomerDTO customerDto = messengerContactDto.getCustomerDTO();
        return customerDto.getFirstName() != null;
    }

    private boolean isInvalidVoiceCall(MessengerContactDto messengerContactDto) {
        CustomerDTO customerDto = messengerContactDto.getCustomerDTO();
        Integer callSuppressDuration = Integer.valueOf(CacheManager.getInstance().getCache(SystemPropertiesCache.class)
                .getIntegerProperty("voicecall_suppress_duration_seconds", 16));
        if (messengerContactDto.getRecordingDuration() != null
                && messengerContactDto.getRecordingDuration().intValue() <= 5
                && customerDto.getFirstName() == null) {
            return true;
        } else if (messengerContactDto.getRecordingDuration() != null
                && (messengerContactDto.getRecordingDuration().intValue() > 5
                        && messengerContactDto.getRecordingDuration().intValue() < callSuppressDuration)
                && messengerContactDto.getLastMessage() == MessengerConstants.NO_TRANSCRIPTION_MESSAGE
                && customerDto.getFirstName() == null) {
            return true;
        }
        return false;
    }

    @Override
    public MessengerContact getOrCreateMessengerContact(MessengerContactDto messengerContactDto) {
        Integer businessId = messengerContactDto.getBusinessId();
        Integer customerId = messengerContactDto.getCustomerId();
        MessengerContact messengerContact = messengerContactRepository.findByCustomerId(customerId).orElseGet(() -> {
            MessengerContact newContact = new MessengerContact();
            newContact.setBusinessId(businessId);
            newContact.setCustomerId(customerId);
            newContact.setCreatedAt(new Date());
            // Default date to avoid current date set as default by DB.
            newContact.setLastResponseAt(new Date(5000));
            return newContact;
        });

        return messengerContact;
    }

    @Override
    public boolean updateMessageDocument(MessageDocument messageDocument, String documentId, Integer accountId) {
        ESRequest.Upsert<MessageDocument> upsert = new ESRequest.Upsert<>(messageDocument, documentId);
        ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
                .addRoutingId(accountId).addPayloadForUpsert(upsert)
                .build();
        return elasticSearchService.updateDocument(esRequest, false);
    }

    @Override
    public ContactDocument contactDocumentBuilder(MessengerContact messengerContact, CustomerDTO customerDTO,
            BusinessDTO businessDTO, MessageTag messageTag, UserDTO userDTO) {
        LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
        ContactDocument.Builder contactBuilder = new ContactDocument.Builder(new ContactDocument());
        ContactDocument document = contactBuilder.addBusinessInfo(businessDTO)
                .addCustomerInfo(customerDTO)
                .addMessageTag(messageTag)
                .addMessengerContactInfo(messengerContact)
                .addLastMessageSenderDetail(userDTO, lastMessageMetadataPOJO.getLastMessageType())
                .addViewedStatus(messengerContact)
                .addPulseSurveyStatus(customerDTO).build();
        return document;
    }

    @Override
    public ElasticData getConversationDataForExport(MessengerFilterForExportInbox messengerQueryFilter,
            boolean isFilterCampaign) {
        Map<String, Object> dataModel = new HashMap<>();
        dataModel.put("contactFilter", messengerQueryFilter);
        dataModel.put("isFilterCampaign", isFilterCampaign);

        ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.CONTACT_INDEX)
                .addTemplateAndDataModel(messengerQueryFilter.getQueryFile(), dataModel).build();
        if (messengerQueryFilter.getAccountId() != null) {
            esRequest.setRoutingId(messengerQueryFilter.getAccountId());
        }
        return elasticSearchService.getDataFromElastic(esRequest, ContactDocument.class);
    }

    @Override
    public ElasticData getMessageDataForExport(MessengerFilterForExportInbox messengerFilter) {
        ESRequest esRequest = buildESRequestForMessage(messengerFilter);
        return elasticSearchService.getDataFromElastic(esRequest, MessageDocument.class);
    }

    private ESRequest buildESRequestForMessage(MessengerFilterForExportInbox messengerFilter) {
        Map<String, Object> dataModel = new HashMap<>();
        dataModel.put("messageFilter", messengerFilter);
        return new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
                .addRoutingId(messengerFilter.getAccountId())
                .addSize(messengerFilter.getSize()).addTemplateAndDataModel(messengerFilter.getQueryFile(), dataModel)
                .build();
    }

    @Override
    public MessageDocument getMessageDocument(MessageDocumentDTO messageDocumentDTO,
            MessengerMediaFileDTO messengerMediaFileDTO, UserDTO userDTO, BusinessDTO businessDTO,
            MessengerEvent event) {
        Integer routeId = businessDTO.getEnterpriseId() != null ? businessDTO.getEnterpriseId()
                : businessDTO.getBusinessId();
        MessageDocument.Builder messageBuilder = new MessageDocument.Builder(new MessageDocument());
        MessageDocument document = messageBuilder.addMessageInfo(messageDocumentDTO)
                .addNoteInfo(messageDocumentDTO)
                .addMessengerMediaFileInfo(messengerMediaFileDTO)
                .addEventType(event)
                .addSenderName(userDTO)
                .addEId(routeId)
                .addBusinessId(businessDTO.getBusinessId())
                .build();

        return document;
    }

    @Override
    public MessageDocument addMessageOnES(MessageDocumentDTO messageDocumentDTO,
            MessengerMediaFileDTO messengerMediaFileDTO, UserDTO userDTO, BusinessDTO businessDTO,
            MessengerEvent event, List<MediaFile> mediaFiles) {
        Integer routeId = businessDTO.getEnterpriseId() != null ? businessDTO.getEnterpriseId()
                : businessDTO.getBusinessId();
        MessageDocument.Builder messageBuilder = new MessageDocument.Builder(new MessageDocument());
        Boolean isSpam = messageDocumentDTO.getSpam() != null ? messageDocumentDTO.getSpam() : false;
        MessageDocument document = messageBuilder.addMessageInfo(messageDocumentDTO).addNoteInfo(messageDocumentDTO)
                .addMessengerMediaFileInfoList(messengerMediaFileDTO, mediaFiles)
                .addEventType(event).addSpam(isSpam)
                .addSenderName(userDTO).addEId(routeId).addBusinessId(businessDTO.getBusinessId())
                .addSecureFaq(messageDocumentDTO.isSecureFaq()).build();
        ESRequest.Upsert<MessageDocument> documentForUpsert = new ESRequest.Upsert<>(document,
                document.getM_id() + messageDocumentDTO.getSuffix());

        ESRequest.Builder esRequestBuilder = new ESRequest.Builder(new ESRequest());
        ESRequest esRequest = esRequestBuilder.addIndex(Constants.Elastic.MESSAGE_INDEX)
                .addRoutingId(routeId).addTemplateAndDataModel(null, null)
                .addPayloadForUpsert(documentForUpsert).build();

        elasticSearchService.updateDocument(esRequest, true);
        redisHandler
                .updateLastEventCache(businessDTO.getEnterpriseId() != null ? businessDTO.getEnterpriseId().toString()
                        : businessDTO.getBusinessId().toString());
        return document;
    }

    @Override
    public MessengerData processMessengerContactForCampaignEmail(BusinessDTO business, UserDTO campaignUser,
            CustomerDTO customer, Email email, ConversationDTO conversationDTO) {

        Integer tagCode = MessageTag.CAMPAIGN.getCode();
        MessengerContact messengerContact = findOrCreate(business, tagCode, customer);
        // For campaign path we dont want to change Conversation tag.
        // Last message handling.
        if (messengerContact.isCampaignOnly()) {
            messengerContact.setIsRead(true);
            updateLastMessageMetaForCampaign(messengerContact, campaignUser, Source.EMAIL.name());
            updateContactWithEmail(messengerContact, business, customer, email);
        } else {
            messengerContact.setUpdatedAt(new Date());
            messengerContactRepository.save(messengerContact);
        }
        messengerContact.setLead(customer.isLead());
        messengerContact.setLeadSource(customer.getLeadSource());
        messengerContact.setContactState(customer.getContactState());
        messengerContact.setTemplateId(email.getTemplateId());
        conversationDTO.setMessengerContactId(messengerContact.getId());
        // ES update for Conversation. Ideally should be done separately in a
        // separate task.
        ContactDocument conversation = updateContactOnESWithRefresh(messengerContact, customer, business,
                MessageTag.getMessageTagById(messengerContact.getTag()),
                messengerContact.isCampaignOnly() ? campaignUser : null, false);

        // ES update for new message.
        MessageDocumentDTO messageDocumentDTO = new MessageDocumentDTO(email, conversation.getM_c_id());
        // Adding stats for Campaign email and Event sync time difference
        Long eventProcessingTime = new Date().getTime();
        messageDocumentDTO.setEventProcessingTimeStamp(eventProcessingTime);
        // convert millisec to min
        Long elapsedTime = email.getSentOn()!=null ? email.getSentOn().getTime() : new Date().getTime();
        Long elapsedTimeInMin = (eventProcessingTime - elapsedTime) / 60000;
        messageDocumentDTO.setTimeElapsedInProcessing(elapsedTimeInMin);
        messageDocumentDTO.setChannel(conversationDTO.getChannel());
        messageDocumentDTO.setSentThrough(conversationDTO.getSentThrough());
        messageDocumentDTO.setMessageType(conversationDTO.getMessageType());
        messageDocumentDTO.setCommunicationDirection(conversationDTO.getCommunicationDirection());
        messageDocumentDTO.setSuffix(
                MessengerUtil.getMessageTypeSuffix(conversationDTO.getMessageType(), conversationDTO.getSource()));
        messageDocumentDTO.setTemplateId(email.getTemplateId());

        MessageDocument messageDocument = addNewMessageOnEsWithRefresh(messageDocumentDTO,
                new MessengerMediaFileDTO(), campaignUser, business, MessengerEvent.EMAIL_SEND, false);

        log.info("[Campaign EMAIL] ES data push for Messages with m_c_id#{} and m_id#{} is done.",
                new Object[] { messageDocument.getC_id(), messageDocument.getM_id() });
        MessengerData data = new MessengerData();
        data.setContactDocument(conversation);
        data.setMessageDocument(messageDocument);
        data.setMessengerContact(messengerContact);
        return data;
    }

    private void updateContactWithEmail(MessengerContact messengerContact, BusinessDTO business, CustomerDTO customer,
            Email email) {
        // Update Last message metadata.
        messengerContact.setEncrypted(0);
        // TODO: Last Message Meta data
        messengerContact.setLastMsgOn(email.getCreateDate());
        messengerContact.setUpdatedAt(email.getCreateDate());
        // Save Messenger Contact.
        messengerContact = messengerContactRepository.saveAndFlush(messengerContact);
    }

    @Override
    public Integer getLastMessageSource(Integer contactId, Integer routeId) {
        Integer lastMessageSource = null;
        MessangerBaseFilter messengerBaseFilter = new MessangerBaseFilter();
        messengerBaseFilter.setAccountId(routeId);
        messengerBaseFilter.setConversationId(contactId);
        messengerBaseFilter.setCount(1);
        List<ContactDocument> contactDocuments = getContactFromES(messengerBaseFilter);
        if (CollectionUtils.isNotEmpty(contactDocuments)) {
            ContactDocument contactDocument = contactDocuments.get(0);
            lastMessageSource = getLastMessageSource(contactDocument);
        }
        return lastMessageSource;
    }

    @Override
    public Integer getLastMessageSource(ContactDocument conversation) {
        Assert.notNull(conversation, "Conversation can't be null");
        Integer lastMessageSource = null;
        LastMessageMetaData lastMessageMetaData = conversation.getLastMessageMetaData();
        if (lastMessageMetaData != null && Source.CONTACT_US.getSourceId().equals(lastMessageMetaData.getLastMessageSource())){
        	if (StringUtils.isNotBlank(conversation.getC_phone())) {
                lastMessageSource = Source.SMS.getSourceId();
            } else {
                lastMessageSource = Source.EMAIL.getSourceId();
            }
        }
        else if (lastMessageMetaData != null && lastMessageMetaData.getLastMessageSource() != null) {
            lastMessageSource = lastMessageMetaData.getLastMessageSource();
        } else if (StringUtils.isNotBlank(conversation.getC_phone())) {
            lastMessageSource = Source.SMS.getSourceId();
        } else {
            lastMessageSource = Source.EMAIL.getSourceId();
        }
        return lastMessageSource;
    }

    @Override
    public ElasticData getConversationIdsFromES(ContactFreeMarkerData data, Integer accountId) {
        ESRequest esRequest = buildESRequestForConversations(data, accountId);
        return elasticSearchService.getDataFromElastic(esRequest, ConversationIds.class);
    }

    @Override
    public List<GetMessengerContactDTO> findByIdIn(List<Integer> ids) {
        List<GetMessengerContactDTO> messengerContacts = messengerContactRepository.findByIdIn(ids);
        if (CollectionUtils.isEmpty(messengerContacts)) {
            log.error("[ MESSENGER_CONTACT_ERROR ] ES contact not found in DB {} ", ids);
        }
        return messengerContacts;
    }

    @Override
    public void updateViewedByRead(Integer userId, List<Integer> conversationIds) {
        List<List<Integer>> conversationIdsOuterList = ListUtils.partition(conversationIds, 1000);
        conversationIdsOuterList.parallelStream().forEach(conversationIdInner -> {
            long startTime = System.currentTimeMillis();
            messengerContactRepository.updateViewedByRead(userId, conversationIdInner, new Date(),
                    MessageTag.INBOX.getCode());
            long endTime = System.currentTimeMillis();
            LogUtil.logExecutionTime("updateViewedByReadDB", startTime, endTime);
        });
    }

    @Override
    public void updateConversationTag(Integer tag, List<Integer> conversationIds) {
        messengerContactRepository.updateConversationTag(tag, conversationIds, new Date());

    }

    @Override
    public void updateViewedByUnread(Integer userId, List<Integer> conversationIds) {
        List<List<Integer>> conversationIdsOuterList = ListUtils.partition(conversationIds, 1000);
        conversationIdsOuterList.parallelStream().forEach(conversationIdInner -> {
            long startTime = System.currentTimeMillis();
            messengerContactRepository.updateViewedByUnread((~(userId - 1)), conversationIdInner, new Date());
            long endTime = System.currentTimeMillis();
            LogUtil.logExecutionTime("updateViewedByUnreadDB", startTime, endTime);
        });

    }

    @Override
    public void updateViewedByUnreadForBusiness(Integer businessId, String userId) {
        messengerContactRepository.updateViewedByUnreadForBusiness(businessId, userId, new Date());
    }

    public List<MessengerContact> findMessengerContactByBusinessId(Integer businessId) {
        return messengerContactRepository.findMessengerContactByBusinessId(businessId);
    }

    @Override
    public List<GetMessengerContactDTO> findViewedByByIdIn(List<Integer> conversationIds) {
        List<GetMessengerContactDTO> messengerContacts = messengerContactRepository.findViewedByByIdIn(conversationIds);
        if (CollectionUtils.isEmpty(messengerContacts)) {
            log.error("[ MESSENGER_CONTACT_ERROR ] ES contact not found in DB {} ", conversationIds);
        }
        return messengerContacts;
    }

    @Override
    public List<MessageDocumentTemp> getMessagesFromESForBidMigration(MessangerBaseFilter messengerQueryFilter) {
        ESRequest esRequest = buildESRequestForMessage(messengerQueryFilter);
        return elasticSearchService.searchByQueryForBidMigration(esRequest, MessageDocumentTemp.class);
    }

    public List<Integer> getByDateWhereContactStateIsNull(Date startDate) {
        return messengerContactRepository.findByNullContactStateAndLastActivityDate(startDate);
    }

    @Override
    public List<Object[]> findFacebookIdsByMcIds(List<Integer> mcIds) {
        return messengerContactRepository.findFacebookIdsByMcIds(mcIds);
    }

    @Override
    public ContactDocument getContact(Integer accountId, Integer mcId) {
        ESFindByIdRequest<ContactDocument> esFindByIdRequest = ESFindByIdRequest.<ContactDocument>builder()
                .documentType(ContactDocument.class).routingId(String.valueOf(accountId))
                .index(Constants.Elastic.CONTACT_INDEX).id(String.valueOf(mcId)).build();
        ContactDocument contactDocument = elasticSearchService.findDocumentById(esFindByIdRequest);
        if (Objects.isNull(contactDocument)) {
            log.error("coversation doen't exists in ES with id : {} and accountId : {}", mcId, accountId);
            throw new NotFoundException(ErrorCode.INVALID_CONVERSATION_ID);
        }
        return contactDocument;
    }

    @Override
    public List<MessengerContact> findByCustomerIdAndBusinessId(Integer customerId, Integer businessId) {
        return messengerContactRepository.findByCustomerIdAndBusinessId(customerId, businessId);
    }

    @Override
    public MessageDocument addReviewToMessages(ReviewEvent reviewEvent, Integer routingId, Integer businessId,
            Integer mcId) {
        Integer routeId = routingId;
        MessageDocument.Builder messageBuilder = new MessageDocument.Builder(new MessageDocument());
        MessageDocument document = messageBuilder.addReviewInfo(reviewEvent, mcId)
                .addEId(routeId).addBusinessId(businessId).build();
        ESUpsertRequest<MessageDocument> esUpsertRequest = ESUpsertRequest.<MessageDocument>builder().document(document)
                .id(document.getM_id() + MessengerUtil.getMessageTypeSuffix(MessageDocument.MessageType.REVIEW, null))
                .index(Constants.Elastic.MESSAGE_INDEX).refreshPolicy(RefreshPolicy.NONE).upsert(true).routingId(String.valueOf(routingId)).build();
        elasticSearchService.upsertESDocument(esUpsertRequest);
        redisHandler.updateLastEventCache(routeId.toString());
        // TODO - do we need to publish this in case of review
        // publishMessageAddedWebhookEvent(businessDTO, document);
        return document;
    }

    @Override
    public MessengerContact findByReviewerIdAndBusinessId(Integer reviewerId, Integer businessId) {
        return messengerContactRepository.findByReviewerIdAndBusinessId(reviewerId, businessId);
    }

    @Override
    public MessengerContact createMessengerContact(ReviewEvent reviewEvent) {
        MessengerContact messengerContact = new MessengerContact();
        if (reviewEvent.getReviewDetail().getCustomerId() != null) {
            CustomerDTO customerDTO = contactService.findById(reviewEvent.getReviewDetail().getCustomerId());
            if (Objects.nonNull(customerDTO)) {
                messengerContact.setContactState(customerDTO.getContactState());
                messengerContact.setLead(customerDTO.isLead());
                messengerContact.setLeadSource(customerDTO.getLeadSource());
            }
            messengerContact.setCustomerId(reviewEvent.getReviewDetail().getCustomerId());
        } else if (reviewEvent.getReviewDetail().getReviewerId() != null) {
            messengerContact.setReviewerId(reviewEvent.getReviewDetail().getReviewerId());
        }
        Date reviewDate = new Date(reviewEvent.getReviewDetail().getReviewDate());
        messengerContact.setLastReviewDate(reviewDate);
        messengerContact.setUpdatedAt(new Date());
        messengerContact.setBusinessId(reviewEvent.getReviewDetail().getBusinessId());
        messengerContact.setCreatedAt(new Date());
        messengerContact.setTag(MessengerTagEnum.UNREAD.getId());
        messengerContact.setViewedBy(null);
        messengerContact.setIsRead(false);
        MessengerContact messengerContact1 = messengerContactRepository.saveAndFlush(messengerContact);
        return messengerContact1;
    }

    @Override
    public MessengerContact createMessengerContact(MessengerContact oldMessengerContact, Review review) {
        MessengerContact newMessengerContact = new MessengerContact();
        newMessengerContact.setAssignee(oldMessengerContact.getAssignee());
        newMessengerContact.setAssignmentType(oldMessengerContact.getAssignmentType());
        newMessengerContact.setBusinessId(oldMessengerContact.getBusinessId());
        newMessengerContact.setCreatedAt(new Date());
        newMessengerContact.setCurrentAssignee(oldMessengerContact.getCurrentAssignee());
        newMessengerContact.setCurrentAssigneeEmailId(oldMessengerContact.getCurrentAssigneeEmailId());
        newMessengerContact.setCurrentAssigneeName(oldMessengerContact.getCurrentAssigneeName());
        newMessengerContact.setCurrentAssigner(oldMessengerContact.getCurrentAssigner());
        newMessengerContact.setEncrypted(0);
        newMessengerContact.setIsRead(oldMessengerContact.getIsRead());
        newMessengerContact.setLastMessageMetaData(null);
        newMessengerContact.setLastReviewDate(new Date(review.getRdate()));
        newMessengerContact.setReviewerId(review.getRvr_id());
        newMessengerContact.setTag(oldMessengerContact.getTag());
        newMessengerContact.setTeamId(oldMessengerContact.getTeamId());
        newMessengerContact.setTemplateId(oldMessengerContact.getTemplateId());
        newMessengerContact.setUpdatedAt(new Date());
        newMessengerContact.setViewedBy(oldMessengerContact.getViewedBy());
        newMessengerContact = messengerContactRepository.saveAndFlush(newMessengerContact);
        return newMessengerContact;
    }

    @Override
    public MessengerContact createMessengerContactAndMessengerMessage(ReviewEvent reviewEvent) {
        MessengerContact mc = createMessengerContact(reviewEvent);
        // messengerMessageService.saveMessengerMessageReviewDetails(reviewEvent,mc.getId());
        return mc;
    }

    @Override
    public Map<Integer, MessengerContact> findByIdsIn(List<Integer> ids) {
        Assert.notNull("ids", "ids shouldn't be null");
        List<MessengerContact> messengerContacts = messengerContactRepository.findByIdsIn(ids);
        return messengerContacts.stream().collect(Collectors.toMap(MessengerContact::getId, mc -> mc));
    }

    @Override
    public MessengerContact saveOrUpdateMessengerContactWithExistingTransaction(MessengerContact contact) {
        return messengerContactRepository.saveAndFlush(contact);
    }

    public Optional<ContactDocument> getContactDocument(Integer accountId, Integer mcId) {
        MessangerBaseFilter messangerBaseFilter = new MessangerBaseFilter();
        messangerBaseFilter.setAccountId(accountId);
        messangerBaseFilter.setConversationId(mcId);
        messangerBaseFilter.setCount(1);
        List<ContactDocument> contactDocuments = getContactFromES(messangerBaseFilter);
        if (CollectionUtils.isNotEmpty(contactDocuments)) {
            return Optional.of(contactDocuments.get(0));
        }
        return Optional.empty();
    }

    @Override
    public MessageDocument addSurveyResponseToMessages(SurveyEvent.After surveyEvent, Integer routingId,
            Integer businessId, Integer mcId, String surveyName) {
        Integer routeId = routingId;
        MessageDocument.Builder messageBuilder = new MessageDocument.Builder(new MessageDocument());
        MessageDocument document = messageBuilder.addSurveyDetails(surveyEvent, mcId, surveyName)
                .addEId(routeId).addBusinessId(businessId).build();

        String id = document.getM_id()
                + MessengerUtil.getMessageTypeSuffix(MessageDocument.MessageType.SURVEY_RESPONSE, null);
        ESUpsertRequest<MessageDocument> esUpsertRequest = ESUpsertRequest.<MessageDocument>builder()
                .document(document).id(id).refreshPolicy(RefreshPolicy.NONE).index(Constants.Elastic.MESSAGE_INDEX)
                .routingId(String.valueOf(routeId)).upsert(true).build();
        elasticSearchService.upsertESDocument(esUpsertRequest);
        redisHandler
                .updateLastEventCache(routeId.toString());
        // TODO - do we need to publish this in case of review
        // publishMessageAddedWebhookEvent(businessDTO, document);
        return document;
    }

    @Override
    public MessengerContact createMessengerContactAndMessengerMessage(SurveyEvent.After surveyEvent, Integer accountId,
            CustomerDTO customerDTO) {
        MessengerContact mc = createMessengerContact(surveyEvent, customerDTO);
        messengerMessageService.saveMessengerMessageSurveyDetails(surveyEvent, mc.getId(), accountId);
        return mc;
    }

    public MessengerContact createMessengerContact(SurveyEvent.After surveyEvent, CustomerDTO customerDTO) {
        MessengerContact messengerContact = new MessengerContact();
        messengerContact.setContactState(customerDTO.getContactState());
        messengerContact.setLead(customerDTO.isLead());
        messengerContact.setLeadSource(customerDTO.getLeadSource());
        messengerContact.setCustomerId(surveyEvent.getCustomerId());
        messengerContact.setLastReviewDate(surveyEvent.getResponseDate());
        messengerContact.setUpdatedAt(new Date());
        messengerContact.setBusinessId(surveyEvent.getBusinessId());
        messengerContact.setCreatedAt(new Date());
        messengerContact.setTag(MessengerTagEnum.UNREAD.getId());
        messengerContact.setViewedBy(null);
        messengerContact.setIsRead(false);
        MessengerContact messengerContact1 = messengerContactRepository.saveAndFlush(messengerContact);
        return messengerContact1;
    }

    @Override
    @Transactional
    public MessengerContact createContact(Integer customerId, Integer businessId) {
        MessengerContact newContact = new MessengerContact();
        newContact.setBusinessId(businessId);
        newContact.setCustomerId(customerId);
        newContact.setTag(MessageTag.INBOX.getCode());
        newContact.setIsRead(true);
        Date createdAt = new Date();
        newContact.setCreatedAt(createdAt);
        newContact.setUpdatedAt(createdAt);
        newContact.setLastMsgOn(createdAt);
        newContact.setIsNew(true);
        newContact.setLastResponseAt(new Date(5000));
        log.debug("createContact : new messengerContact created for customer [ {} ]", customerId);
        return messengerContactRepository.saveAndFlush(newContact);
    }

    public MessengerContact getOrCreateMessengerContactForAppleContact(String appleConversationId, Integer businessId,
            Integer customerId) {
        Optional<MessengerContact> messengerContact = messengerContactRepository
                .findByAppleConversation(appleConversationId, businessId, customerId);
        if (messengerContact.isPresent())
            return messengerContact.get();
        MessengerContact mc = new MessengerContact();
        mc.setCustomerId(customerId);
        mc.setBusinessId(businessId);
        mc.setAppleConversationId(appleConversationId);
        mc.setIsNew(true);
        mc.setCreatedAt(new Date());
        mc.setLastResponseAt(new Date(5000));
        mc.setCurrentStateForResTimeCalc(ConvStateForResTimeCalc.getInitialState());
        mc.setTag(MessengerTagEnum.UNREAD.getId());
        mc.setLeadSource(LeadSource.APPLE);
        return messengerContactRepository.saveAndFlush(mc);
    }

    public MessengerContact getOrCreateMessengerContactForGoogleContact(String googleConversationId, Integer businessId,
            Integer customerId) {
        Optional<MessengerContact> messengerContact = messengerContactRepository
                .findByGoogleConversation(googleConversationId, businessId, customerId);
        if (messengerContact.isPresent())
            return messengerContact.get();
        MessengerContact mc = new MessengerContact();
        mc.setCustomerId(customerId);
        mc.setBusinessId(businessId);
        mc.setGoogleConversationId(googleConversationId);
        mc.setIsNew(true);
        mc.setCreatedAt(new Date());
        mc.setLastResponseAt(new Date(5000));
        mc.setCurrentStateForResTimeCalc(ConvStateForResTimeCalc.getInitialState());
        mc.setTag(MessengerTagEnum.UNREAD.getId());
        mc.setLeadSource(LeadSource.GOOGLE);
        return messengerContactRepository.save(mc);
    }

    @Override
    public Optional<MessengerContact> getByGoogleConversationIdAndSubaccountId(String gcId, Integer businessId) {
        return messengerContactRepository.findByGoogleConversationAndSubaccount(gcId, businessId);
    }

    @Override
    public SearchResponse getAllMessageDataForExport(MessengerFilterForExportInbox messengerQueryFilter) {
        Map<String, Object> dataModel = new HashMap<>();
        dataModel.put("messageFilter", messengerQueryFilter);
        ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
                .addScroll(messengerQueryFilter.getSessionTime())
                .addTemplateAndDataModel(messengerQueryFilter.getQueryFile(), dataModel).build();
        if (messengerQueryFilter.getAccountId() != null) {
            esRequest.setRoutingId(messengerQueryFilter.getAccountId());
        }
        return elasticSearchService.getSearchResult(esRequest);
    }

    @Override
    public SearchResponse getAllContactsDataForExport(MessengerFilterForExportInbox messengerQueryFilter) {
        Map<String, Object> dataModel = new HashMap<>();
        dataModel.put("contactFilter", messengerQueryFilter);
        ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.CONTACT_INDEX)
                .addScroll(messengerQueryFilter.getSessionTime())
                .addTemplateAndDataModel(messengerQueryFilter.getQueryFile(), dataModel).build();
        if (messengerQueryFilter.getAccountId() != null) {
            esRequest.setRoutingId(messengerQueryFilter.getAccountId());
        }
        return elasticSearchService.getSearchResult(esRequest);
    }

    @Override
    public Optional<MessengerContact> getByAppleConversationIdAndSubaccountId(String acId, Integer businessId) {
        return messengerContactRepository.findByAppleConversationAndSubaccount(acId, businessId);
    }

    // Created new Blank conversation
    @Override
    public MessengerContact createMessengerConversation(Integer businessId) {
        MessengerContact messengerContact = new MessengerContact();
        messengerContact.setBusinessId(businessId);
        Date date = new Date();
        messengerContact.setUpdatedAt(date);
        messengerContact.setCreatedAt(date);
        messengerContact.setTag(MessengerTagEnum.UNREAD.getId());
        messengerContact.setViewedBy(null);
        messengerContact.setIsRead(false);

        return messengerContactRepository.saveAndFlush(messengerContact);
    }

    @Override
    public MessengerContact findByGoogleConversationId(String googleConvId) {
        List<MessengerContact> messengerContacts = messengerContactRepository.findByGoogleConversationId(googleConvId);
        if (messengerContacts.isEmpty()) {
            log.error("No conversation found with the following google conversationId : {}", googleConvId);
            return null;
        }
        return messengerContacts.get(0);
    }

    @Override
    public boolean updateContactOnES(Integer messageContactId, ContactDocument contactDocument, Integer routingId,
            Integer enterpriseId, Integer businessId) {
        boolean isUpdated = updateContactOnES(messageContactId, contactDocument, routingId);
        redisHandler
                .updateLastEventCache(enterpriseId != null ? enterpriseId.toString()
                        : businessId.toString());
        return isUpdated;
    }

    @Override
    public void markInActivePreviousAppointmentCards(Integer accountId, Integer messengerContactId, Long appointmentId,
            Long parentId) {
        Map<String, Object> messageFilter = new HashMap<>();
        messageFilter.put("accountId", accountId);
        messageFilter.put("m_c_id", messengerContactId);
        if (parentId != null) {
            messageFilter.put("appointmentId", parentId);
        } else {
            messageFilter.put("appointmentId", appointmentId);
        }
        Map<String, Object> data = new HashMap<>();
        data.put("messageFilter", messageFilter);
        ESUpdateByQueryRequest.Builder builder = new ESUpdateByQueryRequest.Builder(new ESUpdateByQueryRequest());
        builder.index(Constants.Elastic.MESSAGE_INDEX)
                .queryTemplateFile(Constants.Elastic.UPDATE_BY_INACTIVE_APPOINTMENT).freeMarkerDataModel(data);
        Map<String, Object> scriptData = new HashMap<>();
        DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
        String updatedAt = df.format(new Date());
        scriptData.put("inline", "ctx._source.appointmentInfo.isActive = false;ctx._source.updatedAt='" + updatedAt
                + "';ctx._source.lastUpdateDate='" + (new Date()).getTime() + "'");
        builder.scriptParam(scriptData);
        boolean updateByQueryResponse = elasticSearchService.updateByQuery(builder.build());
        if (!updateByQueryResponse) {
            log.error("error in updating mcid {} to messages document for appointmentId:{}", messengerContactId,
                    appointmentId);
            return;

        }
        log.info("Mcid {} updation success for appointmentId:{}", messengerContactId, appointmentId);
    }

    @Override
    public List<MessengerContact> findMessengerContactsByMcIds(List<Integer> messengerContactIds) {
        return messengerContactRepository.findByIdsIn(messengerContactIds);
    }

    @Override
    public void saveBulkMessengerContact(List<MessengerContact> messengerContact) {
        messengerContactRepository.saveAll(messengerContact);
    }

    @Override
    public MessengerContactResponse getFacebookIdsByCustomerId(Integer customerId) {
        List<MessengerContactResponse> response = messengerContactRepository.findFacebookIdsByCustomerIds(customerId);
        if (CollectionUtils.isNotEmpty(response)) {
            return response.get(0);
        }
        return null;
    }

    public MessengerContactResponse findMessengerContactResponseByCustomerId(Integer customerId) {
        List<MessengerContactResponse> response = messengerContactRepository
                .findMessengerContactResponseByCustomerId(customerId);
        if (CollectionUtils.isNotEmpty(response)) {
            return response.get(0);
        }
        return null;
    }

    private void updateSpamAndBlock(MessengerContact messengerContact, Boolean spamStatus,
            MessengerContactDto messengerContactDto) {
        try {
            if (!isBlockAndSpamMarked(messengerContact)) {
                log.info("update contact service for block status for messenger contact {}", messengerContact);
                MarkBlockAndSpamRequestDto markBlockAndSpamRequestDto = new MarkBlockAndSpamRequestDto(
                        messengerContactDto.getBusinessDTO().getAccountId(), spamStatus,
                        messengerContact.getCustomerId(),
                        null, "inbox", Constants.INBOX_DEFENDER_USER);
                if (!contactService.markBlockAndSpam(markBlockAndSpamRequestDto))
                    return;
                if (messengerContact.getSpam().equals(null)) {
                    // check = true;
                    messengerContact.setIsNew(true);
                } else {
                    messengerContact.setIsNew(false);
                }
                messengerContact.setSpam(spamStatus);
                messengerContact.setSpamMarkedBy(Constants.INBOX_DEFENDER_USER);
                messengerContact.setRtmPauseTagging(spamStatus);
                messengerContact.setBlocked(spamStatus);
                messengerContactDto.getCustomerDTO().setBlocked(spamStatus);
                messengerContactDto.getCustomerDTO().setUserId(Constants.INBOX_DEFENDER_USER);
                createActivity(messengerContact, messengerContactDto, spamStatus);
            }
        } catch (Exception e) {
            log.error("Exception while updating a contact spam blocked", e);
            return;
        }

    }

    private boolean isBlockAndSpamMarked(MessengerContact messengerContact) {
    	return (messengerContact.getBlocked() && messengerContact.getSpam());
    }

    private void createActivity(MessengerContact messengerContact, MessengerContactDto messengerContactDto,
            Boolean spamStatus) {
        log.info("Create Block and unblock activity for spam number through automated system for mcid : {}",
                messengerContact.getId());
        MessageTag messageTag = MessageTag.getMessageTagById(messengerContactDto.getTag()); // can be remove it ?
        CustomerDTO customerDTO = messengerContactDto.getCustomerDTO();
        BusinessDTO businessDTO = messengerContactDto.getBusinessDTO();
        UserDTO userDTO = new UserDTO();
        ContactDocument contactDocument = contactDocumentBuilder(messengerContact, customerDTO, businessDTO, messageTag,
                userDTO);
        Date created = new Date(messengerContactDto.getCreateDate().getTime() - 1000);
        if (spamStatus) {
            conversationActivityService.createBlockUnblockActivitySpamPath(ActivityType.CONTACT_BLOCKED, customerDTO,
                    contactDocument, businessDTO, created);
        } else if (Boolean.FALSE.equals(messengerContact.getIsNew())) {
            conversationActivityService.createBlockUnblockActivitySpamPath(ActivityType.CONTACT_UNBLOCKED, customerDTO,
                    contactDocument, businessDTO, created);
        }
    }

    @Override
    public MessengerContact handleMissedCall(MessengerContactDto messengerContactDto) {
        log.info("Check and mark spam misscall as block and spam {}", messengerContactDto);
        MessengerContact messengerContact = getOrCreateMessengerContact(messengerContactDto);
        String existingLastMessage = messengerContact.getLastMessage();
        messengerContact.setTag(messengerContactDto.getTag());
        // Save Messenger Contact.
        messengerContact.setLastMessage(messengerContactDto.getLastMessage());
        messengerContact.setLastMsgOn(messengerContactDto.getLastActivity());
        messengerContact = messengerContactRepository.saveAndFlush(messengerContact);
        // why customerId is coming null
        messengerContact.setCustomerId(messengerContactDto.getCustomerId());
        if ((Constants.INBOX_DEFENDER_USER.equals(messengerContact.getSpamMarkedBy())
                || messengerContact.getSpamMarkedBy() == null)
                && (messengerContactDto.getCustomerDTO().isNewContact()
                        || messengerContactDto.getCustomerDTO().getBlocked())) {
            updateSpamAndBlock(messengerContact, messengerContactDto.getSpam(), messengerContactDto);
        }
        if (StringUtils.isEmpty(existingLastMessage)) {
            messengerContactDto.setHide(true);
        }
        return messengerContact;
    }

    @Override
    public MessengerContact findMessengerContactByMcIdAndCustomerId(Integer id, Integer customerId) {
        long startTime = System.currentTimeMillis();
        MessengerContact messengerContact = messengerContactRepository.findByIdAndCustomerId(id, customerId)
                .orElse(null);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("findMessengerContact", startTime, endTime);
        if (Objects.isNull(messengerContact)) {
            log.error("[ MESSENGER_CONTACT_ERROR ] ES contact not found in DB {} ", id);
        }
        return messengerContact;
    }

    @Override
    @Transactional
    public MessengerContact createContact(Integer customerId,ConversationBySocialId request) {
        MessengerContact newContact = new MessengerContact();
        String feedDate = Objects.nonNull(request.getFeedDate()) ? String.valueOf(new Date(request.getFeedDate())) : (new Date()).toString();
        newContact.setBusinessId(request.getBusinessId());
        newContact.setCustomerId(customerId);
        newContact.setTag(MessageTag.INBOX.getCode());
        newContact.setIsRead(true);
        Date createdAt = new Date();
        newContact.setCreatedAt(createdAt);
        newContact.setUpdatedAt(createdAt);
        newContact.setLastMsgOn(createdAt);
        newContact.setIsNew(true);
        newContact.setLastResponseAt(new Date(5000));
        if(Objects.nonNull(request.getSocialUserId())){
            LastMessageMetaData lastMessageMetadataPOJO = new LastMessageMetaData();
            switch(request.getChannel()){
                case "FACEBOOK":
                    newContact.setFacebookId(request.getSocialUserId());
                    lastMessageMetadataPOJO.setLastMessageSource(Source.FACEBOOK.getSourceId());
                    newContact.setLeadSource(LeadSource.FACEBOOK);
                    lastMessageMetadataPOJO.setLastFbSentAt(feedDate);
                    lastMessageMetadataPOJO.setLastFbReceivedAt(feedDate);
                    break;
                case "INSTAGRAM":
                    newContact.setInstagramConversationId(request.getSocialUserId());
                    lastMessageMetadataPOJO.setLastMessageSource(Source.INSTAGRAM.getSourceId());
                    newContact.setLeadSource(LeadSource.INSTAGRAM);
                    lastMessageMetadataPOJO.setLastIgSentAt(feedDate);
                    lastMessageMetadataPOJO.setLastIgReceivedAt(feedDate);
                    break;
                case "GOOGLE":
                    newContact.setGoogleConversationId(request.getSocialUserId());
                    lastMessageMetadataPOJO.setLastMessageSource(Source.GOOGLE.getSourceId());
                    newContact.setLeadSource(LeadSource.GOOGLE);
                    lastMessageMetadataPOJO.setLastFbSentAt(feedDate);
                    lastMessageMetadataPOJO.setLastFbReceivedAt(feedDate);
                    break;
                case "APPLE":
                    newContact.setAppleConversationId(request.getSocialUserId());
                    lastMessageMetadataPOJO.setLastMessageSource(Source.APPLE.getSourceId());
                    newContact.setLeadSource(LeadSource.APPLE);
                    lastMessageMetadataPOJO.setLastFbSentAt(feedDate);
                    lastMessageMetadataPOJO.setLastFbReceivedAt(feedDate);
                    break;
                case "TWITTER":
                    newContact.setTwitterConversationId(request.getSocialUserId());
                    lastMessageMetadataPOJO.setLastMessageSource(Source.TWITTER.getSourceId());
                    newContact.setLeadSource(LeadSource.TWITTER);
                    lastMessageMetadataPOJO.setLastFbSentAt(feedDate);
                    lastMessageMetadataPOJO.setLastFbReceivedAt(feedDate);
                    break;
                default:
                    throw new IllegalArgumentException("Invalid channel: " + request.getChannel());
            }
            newContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));
            newContact.setImageUrl(request.getProfile_pic());
        }
        newContact.setLastMessage("");
        log.debug("createContact : new messengerContact created for customer [ {} ]", customerId);
        return messengerContactRepository.saveAndFlush(newContact);
    }
    
    public ContactDocument updateContactOnESWithRefresh(MessengerContact messengerContact, CustomerDTO customerDTO,
            BusinessDTO businessDTO, MessageTag messageTag, UserDTO userDTO, boolean refresh) {
        ContactDocument document = contactDocumentBuilder(messengerContact, customerDTO, businessDTO, messageTag,
                userDTO);
        log.info("===============>Updating contact document : {} and refersh : {}", document.toString(), refresh);
        Integer routingId = businessDTO.getRoutingId();
        Integer mcId = messengerContact.getId();
        upsertContactDocumentOnESWithRefresh(document, String.valueOf(mcId), routingId, refresh);
        redisHandler
                .updateLastEventCache(businessDTO.getEnterpriseId() != null ? businessDTO.getEnterpriseId().toString()
                        : businessDTO.getBusinessId().toString());
        return document;
    }

    @Override
    public MessageDocument addNewMessageOnEsWithRefresh(MessageDocumentDTO messageDocumentDTO,
            MessengerMediaFileDTO messengerMediaFileDTO, UserDTO userDTO, BusinessDTO businessDTO, MessengerEvent event,
            boolean refresh) {
        Boolean spam = messageDocumentDTO.getSpam() != null ? messageDocumentDTO.getSpam() : false;
        return addNewMessageOnEsWithRefresh(messageDocumentDTO, messengerMediaFileDTO, userDTO, businessDTO, event,
                spam, refresh);
    }

    @Override
    public MessageDocument addNewMessageOnEsWithRefresh(MessageDocumentDTO messageDocumentDTO,
            MessengerMediaFileDTO messengerMediaFileDTO, UserDTO userDTO, BusinessDTO businessDTO, MessengerEvent event,
            boolean isSpam, boolean refresh) {
        log.info("addNewMessageOnEsWithRefresh called with refresh : {}", refresh);
        Integer routeId = businessDTO.getEnterpriseId() != null ? businessDTO.getEnterpriseId()
                : businessDTO.getBusinessId();
        MessageDocument.Builder messageBuilder = new MessageDocument.Builder(new MessageDocument());
        MessageDocument document = messageBuilder.addMessageInfo(messageDocumentDTO).addNoteInfo(messageDocumentDTO)
                .addMessengerMediaFileInfo(messengerMediaFileDTO).addEventType(event).addSenderName(userDTO)
                .addEId(routeId).addBusinessId(businessDTO.getBusinessId()).addSpam(isSpam)
                .addSecureFaq(messageDocumentDTO.isSecureFaq())
                .addClientIp(messageDocumentDTO).addEventProcessingTime(messageDocumentDTO).build();
        String id = document.getM_id() + messageDocumentDTO.getSuffix();
        if (refresh) {
            ESRequest.Upsert<MessageDocument> documentForUpsert = new ESRequest.Upsert<>(document,
                    id);

            ESRequest.Builder esRequestBuilder = new ESRequest.Builder(new ESRequest());
            ESRequest esRequest = esRequestBuilder.addIndex(Constants.Elastic.MESSAGE_INDEX)
                    .addRoutingId(routeId).addTemplateAndDataModel(null, null)
                    .addPayloadForUpsert(documentForUpsert).build();

            elasticSearchService.updateDocument(esRequest, true);
        } else {
            ESUpsertRequest<MessageDocument> esUpsertRequest = ESUpsertRequest.<MessageDocument>builder()
                    .document(document).id(id).refreshPolicy(RefreshPolicy.NONE).index(Constants.Elastic.MESSAGE_INDEX)
                    .routingId(String.valueOf(routeId)).upsert(true).build();
            elasticSearchService.upsertESDocument(esUpsertRequest);
        }

        redisHandler
                .updateLastEventCache(businessDTO.getEnterpriseId() != null ? businessDTO.getEnterpriseId().toString()
                        : businessDTO.getBusinessId().toString());
        publishMessageAddedWebhookEvent(businessDTO, document);
        return document;
    }

    @Override
    public boolean upsertContactDocumentOnESWithRefresh(ContactDocument contactDocument, String documentId,
            Integer routingId, boolean refresh) {
        log.info("upsertContactDocumentOnESWithRefresh called with id : {},routingId : {} and refresh : {}", documentId,
                routingId, refresh);
        if (refresh) {
            ESRequest.Upsert<ContactDocument> updateDoc = new ESRequest.Upsert<>(contactDocument, documentId);
            ESRequest esRequest = new ESRequest.Builder(new ESRequest())
                    .addIndex(Constants.Elastic.CONTACT_INDEX)
                    .addRoutingId(routingId)
                    .addPayloadForUpsert(updateDoc)
                    .addIncludesAndExcludesForUpsert(new String[] { "latestSurveyScore" }, null).build();
            return elasticSearchService.updateDocument(esRequest, true);
        }

        ESUpsertRequest<ContactDocument> esUpsertRequest = ESUpsertRequest.<ContactDocument>builder()
                .document(contactDocument)
                .id(documentId).index(Constants.Elastic.CONTACT_INDEX).refreshPolicy(RefreshPolicy.NONE)
                .routingId(String.valueOf(routingId)).includes(new String[] { "latestSurveyScore" }).upsert(true)
                .build();
        contactDocument = elasticSearchService.upsertESDocument(esUpsertRequest);
        return Objects.nonNull(contactDocument);
    }

    @Override
    public String getLastMsgCustomChannel(ContactDocument conversation) {
        if (conversation == null) {
            return null;
        }
        LastMessageMetaData lastMessageMetaData = conversation.getLastMessageMetaData();
        return lastMessageMetaData != null ? lastMessageMetaData.getLastMessageCustomChannel() : null;
    }
}