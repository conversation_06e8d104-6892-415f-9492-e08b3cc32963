package com.birdeye.messenger.service.impl;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

import com.birdeye.messenger.external.service.*;
import com.birdeye.messenger.service.leadgenagent.LeadGenAgentTriggerService;
import com.birdeye.messenger.service.leadgenagent.ResponseGeneratorToolService;
import com.birdeye.messenger.util.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.DocWriteRequest;
import org.elasticsearch.action.support.WriteRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.constant.MessengerConstants;
import com.birdeye.messenger.dao.entity.BusinessChatWidgetConfig;
import com.birdeye.messenger.dao.entity.LiveChatMessage;
import com.birdeye.messenger.dao.entity.LiveChatSessionToken;
import com.birdeye.messenger.dao.entity.LiveChatWidgetConfig;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.PulseSurveyContext;
import com.birdeye.messenger.dao.entity.ResponseTimeOutBox;
import com.birdeye.messenger.dao.entity.RobinActiveHours;
import com.birdeye.messenger.dao.entity.TeamConfigForRoundRobin;
import com.birdeye.messenger.dao.repository.LiveChatMessageConfigRepository;
import com.birdeye.messenger.dto.ActivityDto;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.BusinessLocationCustomFieldsTokensDto;
import com.birdeye.messenger.dto.BusinessProfileData;
import com.birdeye.messenger.dto.ConversationDTO;
import com.birdeye.messenger.dto.ConversationStateDTO;
import com.birdeye.messenger.dto.CustomerContactInfo;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.ElasticData;
import com.birdeye.messenger.dto.FeedbackRequest;
import com.birdeye.messenger.dto.FirebaseDto;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.LiveChatInitDTO;
import com.birdeye.messenger.dto.LiveChatInitResponseDTO;
import com.birdeye.messenger.dto.LiveChatMessageDTO;
import com.birdeye.messenger.dto.LiveChatMessageObject;
import com.birdeye.messenger.dto.LiveChatSessionRefreshResponseDTO;
import com.birdeye.messenger.dto.LiveChatSessionTimeoutResponse;
import com.birdeye.messenger.dto.LivechatAnonymousConversationDataDto;
import com.birdeye.messenger.dto.MergeCustomerRequest;
import com.birdeye.messenger.dto.MergeCustomerResponse;
import com.birdeye.messenger.dto.MessageRequest;
import com.birdeye.messenger.dto.MessangerBaseFilter;
import com.birdeye.messenger.dto.MessengerData;
import com.birdeye.messenger.dto.MessengerFilter;
import com.birdeye.messenger.dto.followup.CustomerFollowupRequest;
import com.birdeye.messenger.dto.PublicDataBusinessDTO;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.SmsDTO;
import com.birdeye.messenger.dto.SwitchConversationFirebaseEvent;
import com.birdeye.messenger.dto.TeamDto;
import com.birdeye.messenger.dto.UpdateWebchatContactDetailsRequest;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.UserDetailDTO;
import com.birdeye.messenger.dto.WebChatCustomFieldDataRequest;
import com.birdeye.messenger.dto.elastic.BulkUpsertPayload;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.Channel;
import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.dto.elastic.MessageDocument.MessageType;
import com.birdeye.messenger.dto.elastic.MessageDocument.RobinResponseType;
import com.birdeye.messenger.enums.ActivityMeantFor;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.ChatSessionStatus;
import com.birdeye.messenger.enums.ContactInfoSourceEnum;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.IntentType;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.MessageStatusEnum;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.enums.MessengerTagEnum;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.ComponentCodeEnum;
import com.birdeye.messenger.exception.ErrorMessageBuilder;
import com.birdeye.messenger.exception.InputValidationException;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.external.dto.ChatbotQueryResponse;
import com.birdeye.messenger.external.dto.ChatbotReplyDTO;
import com.birdeye.messenger.external.dto.Suggestion;
import com.birdeye.messenger.external.dto.Suggestion.Type;
import com.birdeye.messenger.external.dto.SuggestionHolder;
import com.birdeye.messenger.external.service.ChatbotService.QueryChannel;
import com.birdeye.messenger.external.service.KontactoRequest.LocationInfo;
import com.birdeye.messenger.service.BusinessChatWidgetConfigService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.ConversationActivityService;
import com.birdeye.messenger.service.ConversationService;
import com.birdeye.messenger.service.CustomerFollowupService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.FirebaseService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.LiveChatMessageService;
import com.birdeye.messenger.service.LiveChatService;
import com.birdeye.messenger.service.LiveChatSessionService;
import com.birdeye.messenger.service.MessageService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.MessengerDashboardService;
import com.birdeye.messenger.service.MessengerService;
import com.birdeye.messenger.service.NotificationService;
import com.birdeye.messenger.service.PulseSurveyService;
import com.birdeye.messenger.service.RedisHandler;
import com.birdeye.messenger.service.RedisLockService;
import com.birdeye.messenger.service.ResponseTimeCalculationService;
import com.birdeye.messenger.service.RobinActiveHoursService;
import com.birdeye.messenger.service.RoundRobinAssignmentService;
import com.birdeye.messenger.service.SmsService;
import com.birdeye.messenger.service.TeamConfigForRoundRobinRepositoryService;
import com.birdeye.messenger.service.WebchatService;
import com.birdeye.messenger.sro.BusinessTimingDTO;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;

import lombok.extern.slf4j.Slf4j;


/**
 * 
 * <AUTHOR>
 *
 */
@Service
@Slf4j
public class LiveChatServiceImpl implements LiveChatService {

	@Autowired
	private LiveChatSessionService sessionService;

	@Autowired
	private BusinessService businessService;

	@Autowired
	private ContactService contactService;

	@Autowired
	private SmsService smsService;

	@Autowired
	private MessengerContactService messengerContactService;

	@Autowired
	private ChatbotService chatbotService;

	@Autowired
	private NotificationService notificationService;

	@Autowired
	private MessengerMessageService messengerMessageService;

	@Autowired
	private FirebaseService fcmService;

	@Autowired
	private BusinessChatWidgetConfigService businessChatWidgetConfigService;

	@Autowired
	private WebchatService webchatService;

	@Autowired
	private RedisHandler redisHandler;

	@Value("${amazonSQS.user.typing.event.queue}")
	private String sqsQueueUrl;

	@Value("${amazonSQS.queue.delay.timer}")
	private String queueDelayTimer;

	@Autowired
	private ConversationActivityService conversationActivityService;

	@Autowired
	private AmazonSQSUtility amazonSQSUtility;

	@Autowired
	private ResponseTimeCalculationService responseTimeCalculationService;

	@Autowired
	private KafkaService kafkaService;

	@Autowired
	LiveChatMessageConfigRepository liveChatMessageConfigRepository;

	@Autowired
	private PulseSurveyService pulseSurveyService;

	@Autowired
	private SMSSendEventHandler smsSendEventHandler;

	@Autowired
	private EmailSendEventHandler emailSendEventHandler;

	@Autowired
	private CommunicationHelperService communicationHelperService;

	@Autowired
	private LiveChatMessageService liveChatMessageService;

	@Autowired
	private MessengerService messengerService;

	@Autowired
	@Lazy
	private AppleSendHandler appleSendHandler;

	@Autowired
	private NLPService nlpService;

	@Autowired
	private RoundRobinAssignmentService roundRobinAssignmentService;

	@Autowired
    private RedisLockService redisLockService;

	@Autowired
	private TeamConfigForRoundRobinRepositoryService teamConfigForRoundRobinRepositoryService;

	@Autowired
	private CommonService commonService;

	@Autowired
	private SpamDetectionService spamDetectionService;
	@Lazy
	private MessengerDashboardService messengerDashboardService;
	@Autowired
	private ElasticSearchExternalService elasticSearchService;

	@Autowired
	private MessageService messageService;

	@Autowired
	private ConversationService conversationService;

	@Autowired
	@Lazy
	private LiveChatSendEventHandler liveChatSendEventHandler;

	@Autowired
	private RobinActiveHoursService robinActiveHoursService;

	@Autowired
	private LeadGenAgentTriggerService leadGenAgentTriggerService;

	@Autowired
	private CustomerFollowupService customerFollowupService;

	@Autowired
	private ResponseGeneratorToolService responseGeneratorToolService;


	public enum TypingEventSource {
		CUSTOMER("customer"), USER("user"), ROBIN("robin");
		private String source;

		private TypingEventSource(String source) {
			this.source = source;
		}

		public String source() {
			return this.source;
		}

		public static TypingEventSource from(String from) {
			return Arrays.stream(TypingEventSource.values())
					.filter(s -> s.source().equals(from))
					.findFirst().orElseThrow(() -> new MessengerException("Invalid value of TypingEventSource"));
		}
	}

	@Override
	public LiveChatInitResponseDTO initialiseSession(LiveChatInitDTO liveChatInitDTO) {
		// Fetch business for given location in liveChatInitDTO
		BusinessDTO businessDto = businessService.getBusinessByBusinessNumber(liveChatInitDTO.getBusinessNumber());
		if (businessDto == null) {
			throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.INVALID_CHAT_SEND_SMS_BUSINESS_NO, HttpStatus.BAD_REQUEST));
		}
		try {
			commonService.validateWidgetApiKey(businessDto.getBusinessNumber(), liveChatInitDTO.getApiKey());
		} catch (Exception e) {
			log.info("Error validating widget api key : {}",e.getMessage());
			return null;
		}
		if(Boolean.TRUE.equals(liveChatInitDTO.getAnonymousConversation()) || (CollectionUtils.isNotEmpty(liveChatInitDTO.getWidgetAgent()) && StringUtils.isNotBlank(liveChatInitDTO.getWidgetAgent().get(0)) && StringUtils.isBlank(liveChatInitDTO.getEmail()) && StringUtils.isBlank(liveChatInitDTO.getMobileNumber()))){
			populateAnonymousCustomerData(liveChatInitDTO,businessDto);
		}
		if(Boolean.FALSE.equals(liveChatInitDTO.getShowPreChatForm()) && Boolean.FALSE.equals(liveChatInitDTO.getAnonymousConversation())){
			liveChatInitDTO.setName(null);
		}
		// 1. Validate the incoming liveChatInitDTO
		RequestDtoValidator.validateLiveChatRequest(liveChatInitDTO);
		Integer businessId = businessDto.getBusinessId();
		boolean isFreeAccount = BusinessDTO.isFreeAccount(businessDto);
		if (isFreeAccount) {
			throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.FREE_ACCOUNT_NOT_SUPPORTED, HttpStatus.BAD_REQUEST));
		}

		LiveChatInitResponseDTO liveChatInitResponseDTO = new LiveChatInitResponseDTO();

		// 2. Fetch/Create a customer as per the provided information
		// Auto user for webchat is -1, can be part of kontacto itself.
		CustomerDTO customer = null;
		List<Integer> accountIds = contactService.getAllContactUpgradeEnabledAccounts();
		if (CollectionUtils.isNotEmpty(accountIds) && businessDto != null
				&& accountIds.contains(businessDto.getAccountId())) {
			customer = commonService.getActiveCustomerBySource(liveChatInitDTO.getMobileNumber(),
					liveChatInitDTO.getEmail(), liveChatInitDTO.getName(), businessDto.getBusinessId(),
					businessDto.getAccountId(), Source.WEB_CHAT.name());
		}
		if(Objects.isNull(customer)){
			KontactoRequest kontactoRequest = create(liveChatInitDTO);
			kontactoRequest.setBusinessId(businessDto.getBusinessId());
			customer = contactService.getorCreateNewCustomer(kontactoRequest, businessDto.getRoutingId(), -1);
		}

		TeamDto teamDto = null;
		if (customer != null) {
			Optional<Lock> lockOpt = Optional.empty();
			try {
				String lockKey = Constants.CUSTOMER_ID_PREFIX+customer.getId();
				lockOpt = redisLockService.tryLock(lockKey, 1,TimeUnit.SECONDS);
				if (!lockOpt.isPresent()) {
					//Reject such events
					log.info("[Livechat init] Unable to acquire lock key:{}",lockKey);
					throw new MessengerException(ErrorCode.WEBCHAT_SEND_LOCK);
				}
				// 3. Upsert messenger_contact for the given customer with the tag as INBOX.
				// This is important to be done during initialisation only. Propagate this mc_id further.
				MessengerData messengerData = messengerContactService.upsertMessengerContact(businessDto, MessageTag.INBOX.getCode(), customer,liveChatInitDTO.getDevice());
				MessengerContact messengerContact = messengerData.getMessengerContact();
				liveChatInitResponseDTO.setMc_id(messengerContact.getId());

				TeamConfigForRoundRobin config = teamConfigForRoundRobinRepositoryService.findTeamConfigForRoundRobinByAccountId(businessDto.getAccountId());


				if (liveChatInitDTO.getTeamId() != null){
					teamDto = webchatService.validateTeamId(liveChatInitDTO.getTeamId());
					if (teamDto != null && (Objects.isNull(config) || !config.getTeamId().equals(liveChatInitDTO.getTeamId()))) {
						webchatService.assignConversationToTeam(teamDto, messengerContact.getId(), businessDto);
						ContactDocument contactDocument = messengerContactService.getContact(businessDto.getAccountId(),messengerData.getContactDocument().getM_c_id());
						if(contactDocument != null){
							log.info("Updated contact doc with team assignment team id:{},{}",contactDocument.getTeam_id(),contactDocument.getM_c_id());
							messengerData.setContactDocument(contactDocument);
						}
					}
				}

				Boolean isReceivedDuringBusinessHr = null;
				BusinessTimingDTO businessTimingDTO = businessService.getBusinessTimings(businessId,false);
				if (businessTimingDTO != null){
					isReceivedDuringBusinessHr=BusinessHoursUtility.isReceivedDuringBusinessHr(businessTimingDTO,new Date());
					liveChatInitResponseDTO.setIsReceivedDuringBusinessHours(isReceivedDuringBusinessHr);
				}

				// TODO 4. Fetch the chatbot_config and set the status accordingly to perform auto-reply on customer query.
				BusinessChatWidgetConfig businessChatWidgetConfig = null;
				if(liveChatInitDTO.getWidgetConfigId()!=null) {
					// this path should be used always
					businessChatWidgetConfig = businessChatWidgetConfigService.getWebChatConfigByWidgetId(liveChatInitDTO.getWidgetConfigId());
				} else {
					businessChatWidgetConfig = businessChatWidgetConfigService.getBusinessChatWidgetConfig(businessDto);
				}
				ChatSessionStatus chatSessionStatus = isChatBotEnabledForBusiness(businessDto, liveChatInitDTO.getWidgetConfigId(),isReceivedDuringBusinessHr,businessTimingDTO,businessChatWidgetConfig);

				LiveChatSessionToken liveChatSessionToken = sessionService.getSession(businessId, customer.getId(),
						businessDto.getRoutingId(), chatSessionStatus, messengerContact.getId(), liveChatInitDTO.getSource(),liveChatInitDTO.getEmailMandatory(),liveChatInitDTO.getWidgetConfigId());
				liveChatInitResponseDTO = convertToInitResponseDTO(liveChatSessionToken);
				liveChatInitResponseDTO.setCustomerPhone(customer.getPhone());
				log.info("[initialiseSession] - invoking persistActivityInStore for sessionId: {}", liveChatSessionToken.getSessionId());
				// adding Livechat-start activity at init call
				ActivityType activityType = teamDto != null && teamDto.getTeamId() != null && StringUtils.isNotBlank(teamDto.getName())
						&&( config == null || !liveChatInitDTO.getTeamId().equals(config.getTeamId()))?ActivityType.LIVECHAT_START_WITH_TEAM:ActivityType.LIVECHAT_START;
				logLiveChatActivity(liveChatSessionToken, liveChatInitDTO.getSource(), activityType,teamDto);
				removeLivechatEndKey(liveChatSessionToken.getMcid());
				liveChatInitResponseDTO.setCustomerEmail(liveChatInitDTO.getEmail());
				liveChatInitResponseDTO.setGreetingMessage(Constants.WEBCHAT_GREETING_MESSAGE);
				liveChatInitResponseDTO.setCustomerName(liveChatInitDTO.getName());
				try{
					//to add check here as well
					if((Boolean.FALSE.equals(liveChatInitDTO.getShowPreChatForm()) || (CollectionUtils.isNotEmpty(liveChatInitDTO.getWidgetAgent()) && StringUtils.isNotBlank(liveChatInitDTO.getWidgetAgent().get(0)) && StringUtils.isBlank(liveChatInitDTO.getEmail()) && StringUtils.isBlank(liveChatInitDTO.getMobileNumber()))) && Boolean.FALSE.equals(liveChatInitDTO.getExistingContact())){
						sendGreetingMessage(messengerContact,businessDto,false,liveChatInitDTO);
					}
					if(Boolean.TRUE.equals(liveChatInitDTO.getLocationSwitch())){
						switchAnonymousConversationLocation(liveChatInitDTO,messengerData.getContactDocument(),customer,messengerContact);
					}
				}catch(Exception e){
					log.info("error while sending greeting message and switching conversation, {}",e);
				}

			} finally {
				if (lockOpt.isPresent()){
					redisLockService.unlock(lockOpt.get());
				}
			}
		}
		return liveChatInitResponseDTO;
	}

	private LiveChatInitResponseDTO convertToInitResponseDTO(LiveChatSessionToken liveChatSessionToken) {
		LiveChatInitResponseDTO liveChatSessionRefreshResponseDTO = new LiveChatInitResponseDTO();
		liveChatSessionRefreshResponseDTO.setAccountId(liveChatSessionToken.getAccountId());
		liveChatSessionRefreshResponseDTO.setBusinessId(liveChatSessionToken.getBusinessId());
		liveChatSessionRefreshResponseDTO.setCustomerId(liveChatSessionToken.getCustomerId());
		liveChatSessionRefreshResponseDTO.setMc_id(liveChatSessionToken.getMcid());
		liveChatSessionRefreshResponseDTO.setSessionId(liveChatSessionToken.getSessionId());
		return liveChatSessionRefreshResponseDTO;
	}

	// Check BusinessChatWidgetConfig for chatbotEnabled business or not ? 
	private ChatSessionStatus isChatBotEnabledForBusiness(BusinessDTO business, Integer widgetId,Boolean isReceivedDuringBusinessHr,BusinessTimingDTO businessTimingDTO, BusinessChatWidgetConfig businessChatWidgetConfig) {
		List<RobinActiveHours> robinActiveHours = null;
		if(Objects.nonNull(businessChatWidgetConfig)) {
			robinActiveHours = robinActiveHoursService.getRobinHoursForWidgetIdAndBusinessID(businessChatWidgetConfig.getId(), business.getBusinessId());
		}
		if (businessChatWidgetConfig != null && businessChatWidgetConfig.getChatbotEnabled() != null
				&& businessChatWidgetConfig.getChatbotEnabled() == 1 && checkRobinActiveHours(robinActiveHours,isReceivedDuringBusinessHr,businessTimingDTO)) {
			log.info("[LiveChatServiceImpl] businessId:{}, isChatBotEnabledForBusiness: TRUE ", business.getBusinessId());
			return ChatSessionStatus.AUTO;
		}
		log.info("[LiveChatServiceImpl] businessId:{}, isChatBotEnabledForBusiness: FALSE ", business.getBusinessId());
		return ChatSessionStatus.MANUAL;
	}

	private KontactoRequest create(LiveChatInitDTO chatDTO) {
		KontactoRequest kontactoRequest = new KontactoRequest();
		String custName = chatDTO.getName();
		kontactoRequest.setName(custName);
		kontactoRequest.setEmailId(chatDTO.getEmail());
		kontactoRequest.setPhone(chatDTO.getMobileNumber());
		kontactoRequest.setSource(KontactoRequest.WEBCHAT);
		// TODO: For Web-chat sms enable by default, is it required for live chat ??
		kontactoRequest.setSmsEnabled(1);
		LocationInfo locationInfo = new LocationInfo();
		locationInfo.setCountryCode(chatDTO.getCountryCode());
		kontactoRequest.setLocation(locationInfo);
		Map<String,String> customField = new HashMap<>() ;
		if (CollectionUtils.isNotEmpty(chatDTO.getCustomFieldsData())) {
			for (WebChatCustomFieldDataRequest webChatCustomFieldDataRequest : chatDTO.getCustomFieldsData()) {
				customField.put(webChatCustomFieldDataRequest.getName(), webChatCustomFieldDataRequest.getValue());
			}
			kontactoRequest.setAdditionalParams(customField);
		}
		Map<String, String> urlParams = new HashMap<>();
		urlParams.put("originPageUrl", chatDTO.getPageUrlTrackedByGoogleAnalytics());
		urlParams.put("messageBody", chatDTO.getMessageBody());
		kontactoRequest.setUrlParams(urlParams);
		return kontactoRequest;
	}

	/**
	 * TODO : Receives the incoming messages and take appropriate action as per the business configurations
	 * It may respond automatically using the chatbot or just notify the business_users of an incoming query.
	 *
	 **/
	@Override
	public void receiveMessage(String sessionId, LiveChatMessageDTO liveChatMessageDTO) {
		// 1. Validate the incoming liveChatMessageDTO
		RequestDtoValidator.validateLiveChatCommunicationRequest(liveChatMessageDTO);

		// 2. Fetch an Active Session if exists.
		Optional<LiveChatSessionToken> sessionTokenOptional = sessionService.findActiveSessionBySessionId(sessionId);
		if (!sessionTokenOptional.isPresent()) {
			log.info("[LiveChatServiceImpl] communicateViaLiveChat invalid sessionId:{}", sessionId);
			throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.INVALID_LIVECHAT_SESSION_ID,
					ComponentCodeEnum.LIVECHAT, HttpStatus.BAD_REQUEST));
		}
		liveChatMessageDTO.setEmailMandatory(sessionTokenOptional.get().getEmailMandatory());
		BusinessDTO business = null;
		// Fetch business if exists
		try {
			business = businessService.getBusinessLiteDTOWithLocation(Constants.BUSINESS_NUMBER,liveChatMessageDTO.getBusinessNumber().toString());
		}catch (Exception e){
			log.error("Exception while getting business from core business number:{} ",liveChatMessageDTO.getBusinessNumber(),e);
		}
		if (business == null) {
			throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.INVALID_CHAT_SEND_SMS_BUSINESS_NO,
					ComponentCodeEnum.LIVECHAT, HttpStatus.BAD_REQUEST));
		}
		// Integration with NLP Service
		if (nlpService.isTextProfane(liveChatMessageDTO.getMessage())) {
			log.warn("Message detected as profane for businessNumber :: {} and message :: {} ", liveChatMessageDTO.getBusinessNumber(), liveChatMessageDTO.getMessage());
			return;
		}
		Integer businessId = business.getBusinessId();
		boolean isFreeAccount = BusinessDTO.isFreeAccount(business);
		if (isFreeAccount) {
			throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.FREE_ACCOUNT_NOT_SUPPORTED,
					ComponentCodeEnum.LIVECHAT, HttpStatus.BAD_REQUEST));
		}

		LiveChatSessionToken sessionToken = sessionTokenOptional.get();
		Integer customerId = sessionToken.getCustomerId();
		Integer mcId = liveChatMessageDTO.getMc_id();
		String customerPhone = liveChatMessageDTO.getMobileNumber();

		// this is because decryption is using the formatted phone
		// but input of live-chat-send request is a non-formatted number
		// we should encrypt and decrypt using E164 format everywhere

		CustomerDTO customerDto = contactService.findByIdNoCaching(customerId);
		if(customerDto == null) {
			log.info("Customer is not present");
			return;
		}
		customerPhone = customerDto.getPhone();
		Optional<Lock> lockOpt = Optional.empty();
		try {
			String lockKey = Constants.CUSTOMER_ID_PREFIX+customerDto.getId();
			lockOpt = redisLockService.tryLock(lockKey, 1,TimeUnit.SECONDS);
			if (!lockOpt.isPresent()) {
				//Reject such events
				log.info("[Webchat send] Unable to acquire lock key:{}",lockKey);
				throw new MessengerException(ErrorCode.WEBCHAT_SEND_LOCK);
			}
			// update firebase message for typing event
			updateFireBaseMessenger(businessId,liveChatMessageDTO.getMc_id());

			// Incoming Message to LiveChatMessage conversion
			LiveChatMessageObject incomingCustomerMessage = prepareLiveChatMessageDto(liveChatMessageDTO, customerId, businessId);
			incomingCustomerMessage.setPageUrlTrackedByGoogleAnalytics(liveChatMessageDTO.getPageUrlTrackedByGoogleAnalytics());
			log.debug("[LiveChatServiceImpl]Customer to Business SMS Message for businessId::{}, apiKey:{}, customerId:{} ",
					businessId, liveChatMessageDTO.getApiKey(), customerId);

			// Incoming customer message persisted to sms entity and encrypted.
			LiveChatMessage liveChatMessage = liveChatMessageService.saveLiveChatMessageFromCustomer(incomingCustomerMessage,MessageStatusEnum.RECEIVED);

			// TODO, set communication direction accordingly
			ConversationDTO conversationDTO = fetchLiveChatConversationDTO(CommunicationDirection.RECEIVE, MessageType.CHAT);
			LiveChatMessageObject liveChatMessageObject=new LiveChatMessageObject(liveChatMessage, conversationDTO);
			liveChatMessageObject.setPageUrlTrackedByGoogleAnalytics(liveChatMessageDTO.getPageUrlTrackedByGoogleAnalytics());
			liveChatMessageObject.setMessageBodyUnencrypted(liveChatMessageDTO.getMessage());
			if(Objects.nonNull(liveChatMessageDTO.getClientIp())) {
				liveChatMessageObject.setClientIp(liveChatMessageDTO.getClientIp());
			}

			MessengerContact messengerContactForSpamCheck = messengerContactService.findById(mcId);

			if (liveChatMessageDTO.getMessage() != null) {
				liveChatMessageDTO.setBusinessDTO(business);
				liveChatMessageDTO.setCustomerDTO(customerDto);
				liveChatMessageDTO.setMessengerContact(messengerContactForSpamCheck);
				liveChatMessageDTO.setSource(Source.LIVE_CHAT_RECEIVE.getSourceId());
				spamDetectionService.spamDetectionAllChannels(liveChatMessageDTO, messengerContactForSpamCheck, customerDto, MessageTag.UNREAD);
			}
			
			liveChatMessageObject.setSpam(messengerContactForSpamCheck.getSpam());
			if(StringUtils.isNotBlank(liveChatMessageObject.getDevice())) {
				liveChatMessageObject.setDevice(liveChatMessageObject.getDevice());
			}
			messengerMessageService.saveMessengerMessage(liveChatMessageObject, null);
			//Perform PostActivity on receiving a live-chat message, like ES update, etc.
			Entry<MessageDocument, ContactDocument> messageAndContactDocument =
					messengerContactService.postLiveChatMessageReceiveActivity
					(business, mcId, liveChatMessageObject, MessengerEvent.LIVECHAT_RECEIVE, customerPhone, MessageTag.UNREAD, null,customerDto,null);
			MessageDocument messageDocument = messageAndContactDocument.getKey();
			ContactDocument contactDocument = messageAndContactDocument.getValue();

			MessengerContact messengerContact = messengerContactService.findById(contactDocument.getM_c_id());
			if(Objects.nonNull(messengerContact)) {
				if(Objects.isNull(messengerContact.getRtmPauseTagging()) || Boolean.FALSE.equals(messengerContact.getRtmPauseTagging())) {
					ResponseTimeOutBox responseTimeOutBox = responseTimeCalculationService.processResTimeMessage(messengerContact, liveChatMessage.getId() + MessengerUtil.getMessageTypeSuffix(MessageType.CHAT, liveChatMessage.getSource()), liveChatMessage.getCreateDate().getTime(), liveChatMessage.getSource(), "R");
					MessengerData messengerData = messengerContactService.saveOrUpdateMessengerContact(messengerContact, responseTimeOutBox);
					if(Objects.nonNull(responseTimeOutBox)) kafkaService.publishToKafkaAsync(KafkaTopicEnum.RESPONSE_TIME_CALC, messengerData.getResponseTimeOutBox());
				}
			}

			boolean hasAgentRespondedWithinConfiguredTimeWindow = messengerService.hasAgentRespondedWithinConfiguredTimeWindow(business.getRoutingId(), mcId);
			// Check the session status if in AUTO mode.
			BusinessTimingDTO businessTimingDTO = businessService.getBusinessTimings(businessId,false);
			Boolean isReceivedDuringBusinessHour = null;
			if (businessTimingDTO != null){
				isReceivedDuringBusinessHour = BusinessHoursUtility.isReceivedDuringBusinessHr(businessTimingDTO,new Date());
			}
			if(CollectionUtils.isNotEmpty(liveChatMessageDTO.getWidgetAgent()) && leadGenAgentTriggerService.isLeadGenAgentTriggerEnabledWeb(business.getBusinessId(),liveChatMessageDTO.getMessage(),contactDocument,messageDocument,liveChatMessageDTO.getWidgetAgent().get(0),liveChatMessageDTO.isHidden())){
				return;
			}
			Optional<ChatbotQueryResponse> chatbotQueryResponseOptional = Optional.empty();
			if(sessionToken.getStatus() == ChatSessionStatus.AUTO && !hasAgentRespondedWithinConfiguredTimeWindow) {
				log.info("[LiveChatSessionServiceImpl] receive message chatsessionstatus is AUTO for businessId:{}", businessId);
				LivechatAnonymousConversationDataDto livechatAnonymousConversationDataDto = new LivechatAnonymousConversationDataDto();
				if(Boolean.TRUE.equals(liveChatMessageDTO.getShowPreChatForm())){
					livechatAnonymousConversationDataDto.setPrechatEnable(true);
				}
				if((StringUtils.isNotEmpty(customerDto.getEmailId()) && customerDto.getEmailId().contains("@anonymous-webchat.com")) && StringUtils.isEmpty(customerDto.getPhoneE164())){
					livechatAnonymousConversationDataDto.setContactDetails(false);
				}
				livechatAnonymousConversationDataDto.setUiContactCheckFlag(liveChatMessageDTO.getContainsContactInfo());
				chatbotQueryResponseOptional = chatbotService
						.getQueryResponse(business.getBusinessId(), business.getRoutingId(), liveChatMessageDTO.getMessage(), getQueryChannel(liveChatMessageDTO.getSource()),
								customerDto.getId(), mcId, isReceivedDuringBusinessHour,livechatAnonymousConversationDataDto,liveChatMessageDTO.getWidgetConfigId());
			}
			boolean chatbotToReply = chatbotQueryResponseOptional.isPresent() && !BooleanUtils.isTrue(messengerContact.getSpam());
//			boolean triggerPushNotification = !chatbotToReply || chatbotQueryResponseOptional.get().isDefault();
			// push received message to firebase, opt for browser notification only if chatbot is not going to reply
			fcmService.pushToFireBaseSync(contactDocument, messageDocument, null, null, true);
			
			if(sessionToken.getStatus() != ChatSessionStatus.MANUAL && !BooleanUtils.isTrue(messengerContact.getSpam())){
				// ****** Mail notification wherever applicable.****** //
				notificationService.processUnreadMessageNotification(business, messageDocument);
			} else if (sessionToken.getStatus() == ChatSessionStatus.MANUAL && !BooleanUtils.isTrue(messengerContact.getSpam())) {
				if(sessionToken.getBusinessUserReplied()!=null && sessionToken.getBusinessUserReplied()!=true) {
					notificationService.processUnreadMessageNotification(business, messageDocument);
				}
			}

			boolean sendLiveChatAutoReply=sessionToken.getAutoReplySent() == null;
			if (chatbotToReply) {
				ChatbotQueryResponse chatbotQueryResponse = chatbotQueryResponseOptional.get();
				cacheFaqResponse(chatbotQueryResponse, business.getRoutingId());
				sendIntentBasedChatbotReply(contactDocument,messageDocument,business, businessId, mcId,chatbotQueryResponse,
						liveChatMessageDTO, customerDto,sendLiveChatAutoReply, sessionToken);
				pushToFirebase(contactDocument, chatbotQueryResponse.getSuggestionHolder(),null,false);
			} else if (sessionToken.getStatus() == ChatSessionStatus.MANUAL && !BooleanUtils.isTrue(messengerContact.getSpam())) {
				if (sessionToken.getAutoReplySent() == null ) {
					BusinessChatWidgetConfig businessChatWidgetConfig = businessChatWidgetConfigService.getWebChatConfigByWidgetId(liveChatMessageDTO.getWidgetConfigId());
					Boolean isLiveChatEnabled = businessChatWidgetConfig!=null && businessChatWidgetConfig.getLivechatEnabled()!=null && businessChatWidgetConfig.getLivechatEnabled()==1;
					if(!hasAgentRespondedWithinConfiguredTimeWindow && isLiveChatEnabled) {
						sendAutoReplyMessageBasedOnBusinessHours(liveChatMessageDTO, business, customerDto,false,isReceivedDuringBusinessHour,false);
					}
					if (isReceivedDuringBusinessHour != null && isReceivedDuringBusinessHour){
						pushToFirebase(contactDocument,null,null,true);
					}
				}
				if (isReceivedDuringBusinessHour != null && !isReceivedDuringBusinessHour && !hasAgentRespondedWithinConfiguredTimeWindow){
					pushToFirebase(contactDocument,null,isReceivedDuringBusinessHour,false);
				}
			}

			if((chatbotQueryResponseOptional.isPresent() && chatbotQueryResponseOptional.get().isDefault()) || (sessionToken.getStatus() == ChatSessionStatus.MANUAL)) {
				if(!hasAgentRespondedWithinConfiguredTimeWindow) {
					sessionToken.setAutoReplySent(true);
				}
			}
			// TODO: Update the last updated date in session token table, and prevent its timeout, not working
			sessionToken.setUpdated(new Date());
			sessionService.save(sessionToken);
			// handle PulseSurveyContext
			PulseSurveyContext context = null;
			try {
				context = pulseSurveyService.handlePulseSurveyContext(null, customerDto, business);
			} catch (Exception ex) {
				log.error("Getting exception while executing handlePulseSurveyContext method {}", ex);
			}
			ContactDocument exContactDocument = new ContactDocument();
			if (context != null && PulseSurveyContext.isOngoingPulseSurvey(context.getStatus())){
				exContactDocument.setOngoingPulseSurvey(PulseSurveyContext.isOngoingPulseSurvey(context.getStatus()));
			} else {
				exContactDocument.setOngoingPulseSurvey(false);
			}
			if(Boolean.TRUE.equals(customerDto.getBlocked())) {
				exContactDocument.setSpam(true);
				exContactDocument.setBlocked(true);
			}
			messengerContactService.updateContactOnES(messengerContact.getId(), exContactDocument, business.getAccountId());
			if(chatbotQueryResponseOptional.isPresent() && ((StringUtils.isNotEmpty((CharSequence)chatbotQueryResponseOptional.get().getData().get("phoneNumber"))) || StringUtils.isNotEmpty((CharSequence)chatbotQueryResponseOptional.get().getData().get("email")))){
				try{
					checkAndMergeAnonymousConversation(chatbotQueryResponseOptional.get(),contactDocument,business);
				}catch(Exception e){
					log.error("Getting exception while merging conversation {}", e);
				}
			}
		} finally {
			if (lockOpt.isPresent()) {
				redisLockService.unlock(lockOpt.get());
			}
		}
	}


	private void sendIntentBasedChatbotReply(ContactDocument contactDocument, MessageDocument messageDocument,
											 BusinessDTO business, Integer businessId,Integer mcId,ChatbotQueryResponse chatbotQueryResponse,
											 LiveChatMessageDTO liveChatMessageDTO, CustomerDTO customerDTO, boolean sendLiveChatAutoReply,
											 LiveChatSessionToken sessionToken) {
		MessageDocument.RobinResponseType robinResponseType=null;
		if("DEFAULT_FALLBACK".equals(chatbotQueryResponse.getIntentType())) {
			if (Objects.nonNull(chatbotQueryResponse.getData().get("support")) &&
					Objects.nonNull(chatbotQueryResponse.getData().get("response"))) {
				Boolean support = (Boolean) chatbotQueryResponse.getData().get("support");
				if (Objects.nonNull(support) && support) {
					String response = chatbotQueryResponse.getData().get("response").toString();
					Boolean answerFlag = Boolean.FALSE;
					if (Objects.nonNull(chatbotQueryResponse.getData().get("answerFlag"))) {
						answerFlag = (Boolean) chatbotQueryResponse.getData().get("answerFlag");
					}
			    	String customMessageLocations = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("ai_custom_reply_locations", "1478071,1478072,1478073,1478074,1478076,1478077,1478078,1478079,1478080,1478081");
			    	Set<Integer> customMessageLocationIds=ControllerUtil.getTokensListIntegerFromString(customMessageLocations);
			    	if (customMessageLocationIds.contains(businessId)) {
			    		String customMsgPropertyName="ai_custom_reply_msg:"+business.getAccountId();
			    		response = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(customMsgPropertyName, "My apologies I am unable to answer this, feel free to ask any other questions.");
			    	}
					log.info("DEFAULT_FALLBACK answerFlag : {}", answerFlag);
					response = replaceTokensLivechat(business, customerDTO, null, response);
					sendChatbotAutoReply(business,sessionToken.getEmailMandatory(),sessionToken.getMcid(),customerDTO.getPhone(),
							response,messageDocument.getSource(),customerDTO,null,true, answerFlag);
				}
			}else {
				sendLiveChatAutoReply(contactDocument, business, false, liveChatMessageDTO, customerDTO, sendLiveChatAutoReply);
			}
//			notificationService.processUnreadMessageNotification(business, messageDocument);
			robinResponseType=MessageDocument.RobinResponseType.DEFAULT;
		}else if(chatbotQueryResponse.getSuggestionHolder()==null){
			if(IntentType.INTENT_TYPES_STRINGS.contains(chatbotQueryResponse.getIntentType()) ||
					"FAQ".equals(chatbotQueryResponse.getIntentType())) {
				//response received from Robin 1.0
				chatbotQueryResponse.getData().put("answerFlag", true);
			}
			sendChatbotReply(business, businessId, customerDTO.getId(), mcId, customerDTO.getPhone(), chatbotQueryResponse,
					liveChatMessageDTO.getSource(), customerDTO,sessionToken);
			robinResponseType=MessageDocument.RobinResponseType.INTENT;
			if("FAQ".equals(chatbotQueryResponse.getIntentType())) {
				robinResponseType=MessageDocument.RobinResponseType.FAQ;
			}
			if("GPT".equals(chatbotQueryResponse.getIntentType())) {
				robinResponseType=MessageDocument.RobinResponseType.GPT;
			}
		}else if(chatbotQueryResponse.getSuggestionHolder()!=null){
			robinResponseType=MessageDocument.RobinResponseType.SUGGESTION;
		}
		if(robinResponseType!=null) {
			commonService.updateRobinResponseTypeInMessage(messageDocument,robinResponseType);
		}
	}

	private void sendLiveChatAutoReply(ContactDocument contactDocument, BusinessDTO business,
									   Boolean updateLastRespondedAt, LiveChatMessageDTO liveChatMessageDTO, CustomerDTO customerDTO, boolean sendLiveChatAutoReply) {
		BusinessChatWidgetConfig businessChatWidgetConfig = businessChatWidgetConfigService
				.getBusinessChatWidgetConfig(liveChatMessageDTO.getWidgetConfigId());
		BusinessTimingDTO businessTimingDTO = businessService.getBusinessTimings(business.getBusinessId(),false);
		Boolean isReceivedDuringBusinessHr = null;

		if (businessTimingDTO != null){
			isReceivedDuringBusinessHr = BusinessHoursUtility.isReceivedDuringBusinessHr(businessTimingDTO,new Date());
		}
		Boolean isLiveChatEnabled = businessChatWidgetConfig!=null && businessChatWidgetConfig.getLivechatEnabled()!=null && businessChatWidgetConfig.getLivechatEnabled()==1;
		if(isLiveChatEnabled) {
			if(sendLiveChatAutoReply) {
				sendAutoReplyMessageBasedOnBusinessHours(liveChatMessageDTO, business, customerDTO,updateLastRespondedAt,isReceivedDuringBusinessHr,true);
				sendClosingMessageEventToFirebase(isLiveChatEnabled,isReceivedDuringBusinessHr,contactDocument);
			}
		}else {
			sendClosingMessageEventToFirebase(isLiveChatEnabled,isReceivedDuringBusinessHr,contactDocument);
		}

	}

	private void sendAutoReplyMessageBasedOnBusinessHours(LiveChatMessageDTO liveChatMessageDTO, BusinessDTO business, CustomerDTO customerDTO, Boolean updateLastRespondedAt, Boolean isReceivedDuringBusinessHour, boolean sendUser) {
		if(isReceivedDuringBusinessHour == null) {
			log.info("Business hours is not configured for businessId:{}",business.getBusinessId());
			return;
		}
		//Based on business hours we need to get the response.
		LiveChatWidgetConfig liveChatWidgetConfig = liveChatMessageConfigRepository.findByWidgetId(liveChatMessageDTO.getWidgetConfigId());
		if (liveChatWidgetConfig == null){
			log.info("Live chat message config not found for widget config id:{}",liveChatMessageDTO.getWidgetConfigId());
			return;
		}
		String message = getMessagebyBusinessHours(isReceivedDuringBusinessHour,business,liveChatWidgetConfig,customerDTO.getFirstName());
		if (StringUtils.isNotEmpty(message)){
			sendChatbotAutoReply(business,liveChatMessageDTO.getEmailMandatory(),liveChatMessageDTO.getMc_id(), liveChatMessageDTO.getMobileNumber(), message, liveChatMessageDTO.getSource(), customerDTO,updateLastRespondedAt,sendUser, Boolean.FALSE);
		}
	}


	@Override
	public void pushToFirebase(ContactDocument contactDocument, SuggestionHolder suggestionHolder,Boolean isReceivedDuringBusinessHours, Boolean showTimer) {
		if(suggestionHolder!=null || Objects.nonNull(isReceivedDuringBusinessHours) || BooleanUtils.isTrue(showTimer)) {
			fcmService.pushToFirebase(contactDocument, suggestionHolder,isReceivedDuringBusinessHours,showTimer);
		}
	}

	private void cacheFaqResponse(ChatbotQueryResponse chatbotQueryResponse, Integer accountId) {
		if(chatbotQueryResponse.getSuggestionHolder()!=null && "FAQ".equals(chatbotQueryResponse.getIntentType())) {
			if(CollectionUtils.isNotEmpty(chatbotQueryResponse.getSuggestionHolder().getSuggestions())) {
				List<Suggestion> suggestions=chatbotQueryResponse.getSuggestionHolder().getSuggestions();
				suggestions.stream().forEach(suggestion->{
					if(StringUtils.isNotBlank(suggestion.getKey()) && StringUtils.isNotBlank(suggestion.getAnswer())
							&& (!suggestion.isPartial()) && (!suggestion.isSecure()))
						redisHandler.setOpsForValueWithExpiry(getFormattedReddisKey(suggestion.getValue(),accountId), suggestion.getAnswer(), 3L, TimeUnit.MINUTES);
					suggestion.setAnswer(null);
				});

			}
		}
	}

	private String getFormattedReddisKey(String key, Integer accountId) {
		return key.replaceAll("[?]", "").replaceAll(" ", "")+"_"+accountId;
	}

	private MessengerFilter getESQueryData(MessageRequest messageRequest, BusinessDTO businessDTO) {
		MessengerFilter esQueryData = new MessengerFilter();
		esQueryData.setConversationId(messageRequest.getMessengerContactId());
		esQueryData.setStartIndex(messageRequest.getStartIndex());
		esQueryData.setCount(messageRequest.getCount());
		esQueryData.setAccountId(businessDTO.getRoutingId());
		esQueryData.setMessageTypes(Arrays.asList(""));
		esQueryData.setQueryFile(Constants.Elastic.GET_MESSAGES_V2);
		if(Objects.nonNull(messageRequest.getLastMessageTime())) {
			DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			esQueryData.setStartDate(df.format(new Date(messageRequest.getLastMessageTime())));
		}
		return esQueryData;
	}

	private void sendChatbotReply(BusinessDTO business, Integer businessId, Integer customerId, Integer mcId,
								  String customerPhone, ChatbotQueryResponse chatbotQueryResponse, Integer receiveSource,CustomerDTO customerDTO,
								  LiveChatSessionToken sessionToken) {

		ChatbotReplyDTO chatbotReplyDTO = new ChatbotReplyDTO();
		chatbotReplyDTO.setIntentType(chatbotQueryResponse.getIntentType());
		if ("GPT".equals(chatbotQueryResponse.getIntentType())) {
			chatbotReplyDTO.setIntentType("FAQ");
		}
		chatbotReplyDTO.setType("intent");
		chatbotReplyDTO.setData(chatbotQueryResponse.getData());

		log.info("[LiveChatSessionServiceImpl] receive message chatbot reply is {} for businessId:{}", chatbotQueryResponse.getData(), businessId);
		boolean secureFaq = false;
		boolean partial = false;

		Boolean answerFlag = Boolean.FALSE;
		if (Objects.nonNull(chatbotQueryResponse.getData().get("answerFlag"))) {
			answerFlag = (Boolean) chatbotQueryResponse.getData().get("answerFlag");
		}

		if (chatbotReplyDTO.getData().get("secureFaq") != null)
			secureFaq  = (boolean) chatbotReplyDTO.getData().get("secureFaq");
		if (chatbotReplyDTO.getData().get("partial") != null)
			partial = (boolean) chatbotReplyDTO.getData().get("partial");
		String response = null;
		if (partial) {
			//all tokens not replaced
			response = "We could not find the requested information for your number "+ customerPhone;
			if(BooleanUtils.isTrue(sessionToken.getEmailMandatory())) {
				response = "There seems to be no information found against your contact information.";
			}
			chatbotReplyDTO.getData().put("response", response);
		} else if (secureFaq) {
			//send sms
			Gson gson = new Gson();
			ChatbotReplyDTO chatbotReplySMS = gson.fromJson(gson.toJson(chatbotReplyDTO), ChatbotReplyDTO.class);
			sendMessageForSecuredFAQ(business, customerDTO, chatbotReplySMS,sessionToken.getEmailMandatory());
			response = chatbotReplyDTO.getData().get("response").toString();
			Integer sourceId=Source.SMS.getSourceId();
			if (StringUtils.isNotEmpty(response)) {
				//Modify webchat robin response - if response found
				response = "Requested information has been sent to your number "+ customerPhone;
				if(BooleanUtils.isTrue(sessionToken.getEmailMandatory())) {
					response = "Requested information has been sent to your emailId "+ customerDTO.getEmailId();
					sourceId=Source.EMAIL.getSourceId();
				}
			}
			chatbotReplyDTO.getData().put("response", response);
			ActivityType activityType = ActivityType.LIVECHAT_START;
			logLiveChatActivity(sessionToken, sourceId, activityType, null);
		}
		if (Objects.nonNull(chatbotReplyDTO.getData().get("response"))) {
			// Stringify the chatbot reply
			String msgBody = chatbotReplyDTO.getData().get("response").toString();
			replaceTokensLivechat(business, customerDTO, chatbotReplyDTO, msgBody);
		}
		String stringifiedChatbotReply = JSONUtils.toJSON(chatbotReplyDTO);
		// Update the response from chatbot to our persistence channels - DB/ES.
		//SmsDTO chatbotReplyMessage = prepareChatbotSmsDto(stringifiedChatbotReply, customerId, businessId, receiveSource);
		LiveChatMessageObject incomingCustomerMessage = prepareLiveChatMessageDto(stringifiedChatbotReply, customerId, businessId, receiveSource);
		incomingCustomerMessage.setEmailMandatory(sessionToken.getEmailMandatory());
		LiveChatMessage liveChatMessage = liveChatMessageService.saveLiveChatMessageFromCustomer(incomingCustomerMessage,MessageStatusEnum.SENT);
		ConversationDTO chatbotConversationDTO = fetchLiveChatConversationDTO(CommunicationDirection.SEND, MessageType.RICH_CONTENT_CHAT);
		//SmsDTO chatbotSms = new SmsDTO(chatbotSavedSMS, chatbotConversationDTO);
		LiveChatMessageObject liveChatMessageObject=new LiveChatMessageObject(liveChatMessage, chatbotConversationDTO);
		liveChatMessageObject.setMessageBodyUnencrypted(stringifiedChatbotReply);
		liveChatMessageObject.setAnswerFlag(answerFlag);
		if(Objects.nonNull(chatbotQueryResponse.getData().get("citations"))){
			liveChatMessageObject.setCitations((List<String>)chatbotQueryResponse.getData().get("citations"));
			liveChatMessageObject.setFaqFlag((Boolean)chatbotQueryResponse.getData().get("faqFlag"));
			liveChatMessageObject.setFileFlag((Boolean)chatbotQueryResponse.getData().get("fileFlag"));
		}
		// Update SMS reply on ES, hard-coded -10 as userId for chatbot user reply
		Entry<MessageDocument, ContactDocument> chatbotMessageAndContactDocument =
				messengerContactService.postLiveChatMessageReceiveActivity
						(business, mcId, liveChatMessageObject, MessengerEvent.LIVECHAT_SEND, customerPhone, MessageTag.INBOX, MessengerConstants.ROBIN_REPLY_USER,customerDTO,true);

		// TODO make sure it is in the same format as the above received message
		// push response to Firebase
		fcmService.pushToFireBase(chatbotMessageAndContactDocument.getValue(), chatbotMessageAndContactDocument.getKey(), null, null);
	}

	private String replaceTokensLivechat(BusinessDTO business, CustomerDTO customerDTO, ChatbotReplyDTO chatbotReplyDTO,
			String msgBody) {
		
		String phoneNumber = smsService.getFormattedBusinessNumber(business.getBusinessId(),
				business.getPhone());
		String textingNumber = smsService.getFormattedBusinessNumber(business.getBusinessId());
		
		BusinessTimingDTO businessTimingDTO = null;
		if (msgBody.contains(Constants.BUSINESS_HOURS_OLD) || msgBody.contains(Constants.BUSINESS_HOURS_NEW)){
			businessTimingDTO = businessService.getBusinessTimings(business.getBusinessId(),true);
		}
		PublicDataBusinessDTO publicBusinessDto = null;
		if (msgBody.contains(Constants.BUSINESS_SERVICES) || msgBody.contains(Constants.BUSINESS_PAYMENT_OPTIONS)
				|| msgBody.contains(Constants.BUSINESS_LANGUAGES) || msgBody.contains(Constants.BUSINESS_SERVICES_CAP)
				|| msgBody.contains(Constants.BUSINESS_PAYMENT_OPTIONS_CAP) || msgBody.contains(Constants.BUSINESS_LANGUAGES_CAP)){
			publicBusinessDto = businessService.getPublicBusinessData(business.getBusinessId());
		}
		BusinessLocationCustomFieldsTokensDto customFields = businessService.getBusinessCustomFieldsAndTokenByBusinessID(business.getBusinessId());
		BusinessProfileData businessProfileData = businessService.getBusinessProfileData(business.getBusinessId());
		String formattedBody = ControllerUtil.replaceTokens(business, customerDTO.getFirstName(),
				msgBody, phoneNumber, textingNumber, businessTimingDTO, publicBusinessDto,businessProfileData,customFields);
		if (chatbotReplyDTO != null && chatbotReplyDTO.getData() != null) {
			chatbotReplyDTO.getData().put("response", formattedBody);
		}
		return formattedBody;
	}

	@Async
	private void sendMessageForSecuredFAQ(BusinessDTO business, CustomerDTO customerDTO, ChatbotReplyDTO chatbotReplyDTO, Boolean emailMandatory) {
		SendMessageDTO sendMessageDTO = getSMSDataForProtectedField(chatbotReplyDTO, business, customerDTO);
		try {
			if(BooleanUtils.isTrue(emailMandatory)) {
				sendMessageDTO.setSource(Source.EMAIL.getSourceId());
				emailSendEventHandler.handle(sendMessageDTO);
			}else {
				smsSendEventHandler.handle(sendMessageDTO);
			}
		} catch (Exception e) {
			log.error("Exception while sending message:{}",e);
		}
	}

	private SendMessageDTO getSMSDataForProtectedField(ChatbotReplyDTO chatbotReplyDTO, BusinessDTO businessDTO,
													   CustomerDTO customer) {
		SendMessageDTO sendMessageDTO = new SendMessageDTO();
		MessengerContact messengerContact = messengerContactService.findByCustomerId(customer.getId());
		sendMessageDTO.setFromBusinessId(businessDTO.getBusinessId());
		sendMessageDTO.setToCustomerId(String.valueOf(messengerContact.getId()));
		sendMessageDTO.setCustomerId(customer.getId());
		sendMessageDTO.setBusinessIdentifierId(String.valueOf(businessDTO.getBusinessId()));
		sendMessageDTO.setMessengerContact(messengerContact);
		// default source is sms
		if (sendMessageDTO.getSource() == null) {
			sendMessageDTO.setSource(1);
		}
		sendMessageDTO.setSource(sendMessageDTO.getSource());

		String autoReplyText = chatbotReplyDTO.getData().get("response").toString();
		if(StringUtils.isEmpty(autoReplyText)) {
			return null;
		}
		sendMessageDTO.setBody(autoReplyText);

		UserDTO userDTO = communicationHelperService.getUserDTO(MessengerConstants.ROBIN_REPLY_USER);
		sendMessageDTO.setUserDTO(userDTO);
		sendMessageDTO.setBOT(true);
		sendMessageDTO.setUpdateLastMessage(false);
		sendMessageDTO.setSecureFaq(true);
		return sendMessageDTO;
	}

	private ConversationDTO fetchLiveChatConversationDTO(CommunicationDirection communicationDirection, MessageType messageType) {
		ConversationDTO conversationDTO = new ConversationDTO();
		conversationDTO.setMessageType(messageType);
		conversationDTO.setChannel(Channel.LIVE_CHAT);
		conversationDTO.setCommunicationDirection(communicationDirection);
		return conversationDTO;
	}

	private SmsDTO prepareSmsDto(LiveChatMessageDTO liveChatMessageDTO, Integer customerId,
								 Integer businessId) {
		SmsDTO smsDto = new SmsDTO();
		smsDto.setBusinessId(businessId);
		smsDto.setCustomerId(customerId);
		// TODO: We should use E164 phone number from customer.
		//smsDto.setToNumber(customer.getPhoneE164());
		smsDto.setMessageBodyUnencrypted(liveChatMessageDTO.getMessage());
		smsDto.setSource(getWebchatSource(liveChatMessageDTO.getSource()));
		return smsDto;
	}

	private LiveChatMessageObject prepareLiveChatMessageDto(LiveChatMessageDTO liveChatMessageDTO, Integer customerId,
															Integer businessId) {
		LiveChatMessageObject liveChatMessageObject = new LiveChatMessageObject();
		liveChatMessageObject.setBusinessId(businessId);
		liveChatMessageObject.setMessengerContactId(liveChatMessageDTO.getMc_id());
		liveChatMessageObject.setCustomerId(customerId);
		// TODO: We should use E164 phone number from customer.
		//smsDto.setToNumber(customer.getPhoneE164());
		liveChatMessageObject.setMessageBodyUnencrypted(liveChatMessageDTO.getMessage());
		liveChatMessageObject.setSource(getWebchatSource(liveChatMessageDTO.getSource()));
		liveChatMessageObject.setEmailMandatory(liveChatMessageDTO.getEmailMandatory());
		liveChatMessageObject.setEmail(liveChatMessageDTO.getEmail());
		return liveChatMessageObject;
	}

	private SmsDTO prepareSmsDto(String message,int source, Integer customerId,
								 Integer businessId) {
		SmsDTO smsDto = new SmsDTO();
		smsDto.setBusinessId(businessId);
		smsDto.setCustomerId(customerId);
		// TODO: We should use E164 phone number from customer.
		//smsDto.setToNumber(customer.getPhoneE164());
		smsDto.setMessageBodyUnencrypted(message);
		smsDto.setSource(source);
		return smsDto;
	}

	private LiveChatMessageObject prepareLiveChatMessageDto(String message,int source, Integer customerId,
															Integer businessId) {
		LiveChatMessageObject liveChatMessageObject = new LiveChatMessageObject();
		liveChatMessageObject.setBusinessId(businessId);
		liveChatMessageObject.setCustomerId(customerId);
		liveChatMessageObject.setMessageBodyUnencrypted(message);
		liveChatMessageObject.setSource(source);
		return liveChatMessageObject;
	}

	private LiveChatMessageObject prepareLiveChatMessageDto(String message, Integer customerId,
															Integer businessId, Integer receiveSource) {
		LiveChatMessageObject liveChatMessageDto = new LiveChatMessageObject();
		liveChatMessageDto.setBusinessId(businessId);
		liveChatMessageDto.setCustomerId(customerId);
		// This is set afterwards using DB fetch, hence removed from here
		//smsDto.setToNumber(customer.getPhone());
		liveChatMessageDto.setMessageBodyUnencrypted(message);
		Source source = Source.getValue(receiveSource);
		Source sendSource = source==Source.LIVE_CHAT_BIRDEYE_PROFILE_RECEIVE?Source.LIVE_CHAT_BIRDEYE_PROFILE_SEND:Source.LIVE_CHAT_SEND;
		liveChatMessageDto.setSource(sendSource.getSourceId());
		return liveChatMessageDto;
	}

	private static int getWebchatSource(Integer source) {
		if (source != null) {
			return source;
		}
		return Source.LIVE_CHAT_RECEIVE.getSourceId();
	}

	private static QueryChannel getQueryChannel(Integer sourceId) {
		if (sourceId != null) {
			return QueryChannel.LIVECHAT;
		} else {
			Source source = Source.getValue(sourceId);
			switch (source) {
				case FACEBOOK:
					return QueryChannel.FACEBOOK;
				case LIVE_CHAT_BIRDEYE_PROFILE_RECEIVE:
					return QueryChannel.LIVECHAT_BIRDEYE_PROFILE;
				case LIVE_CHAT_RECEIVE:
					return QueryChannel.LIVECHAT;
				case SMS:
					return QueryChannel.SMS;
				default:
					break;
			}
		}
		// Set default channel
		return QueryChannel.LIVECHAT;
	}

	@Override
	public void sessionClose(String sessionId, Integer source) {
		Optional<LiveChatSessionToken> sessionTokenOptional = sessionService.findActiveSessionBySessionId(sessionId);
		if(sessionTokenOptional.isPresent()) {
			LiveChatSessionToken sessionToken = sessionTokenOptional.get();
			ConversationStateDTO convState = new ConversationStateDTO(sessionToken);
			convState.setBusinessId(sessionToken.getBusinessId());
			convState.setMc_id(sessionToken.getMcid());
			convState.setState(ActivityType.LIVECHAT_END.getLabel());
			convState.setSessionId(sessionId);
			convState.setSource(source);
			convState.setSentAt(ZonedDateTime.now(ZoneId.of("UTC")).toInstant().toEpochMilli());
			addTypingEventToRedis(null, convState);
			pushMessageToSQSQueue(convState, 10);
		}


	}


	/**
	 * Activities performed when the session is closed.
	 */
	@Override
	public LiveChatSessionToken sessionTerminated(String sessionId, Integer source, ActivityType activityType) {
		Optional<LiveChatSessionToken> sessionTokenOptional = sessionService.findActiveSessionBySessionId(sessionId);
		if(sessionTokenOptional.isPresent()) {
			LiveChatSessionToken sessionToken = sessionTokenOptional.get();
			// Update the last status to the current status
			sessionToken.setLastStatus(sessionToken.getStatus());
			sessionToken.setStatus(ChatSessionStatus.TERMINATED);
			sessionToken.setUpdated(new Date());
			sessionToken.setAutoReplySent(null);
			LiveChatSessionToken liveChatSessionToken = sessionService.save(sessionToken);

			log.info("[sessionClose][{}] - invoking persistActivityInStore for sessionId:{}", activityType, sessionToken.getSessionId());
			// adding timeout activity
			logLiveChatActivity(sessionToken, source, activityType,null);
			return liveChatSessionToken;
		}else {
			log.info("[LiveChatServiceImpl] sessionTimeout invalid/inactive sessionToken:{}", sessionId);
		}
		return null;
	}

	@Override
	public LiveChatSessionTimeoutResponse sessionTimeout(String sessionId, Integer source,Integer widgetConfigId,
														 boolean sendAutoReply, boolean lastMessageReplied, String agentId) {
		// Perform Session Close operations
		LiveChatSessionToken liveChatSessionToken = sessionTerminated(sessionId, source, ActivityType.LIVECHAT_TIMEOUT);

//		if (liveChatSessionToken != null){
//			Integer businessId = liveChatSessionToken.getBusinessId();
//			BusinessDTO businessDTO = businessService.getBusinessLiteDTO(businessId);
//			if (businessDTO != null){
//				BusinessTimingDTO businessTimingDTO = businessService.getBusinessTimings(businessId);
//				LiveChatWidgetConfig liveChatWidgetConfig = liveChatMessageConfigRepository.findByWidgetId(widgetConfigId);
//				return getClosingMessageBasedOnBizHours(businessTimingDTO,businessDTO, liveChatWidgetConfig);
//			}
//		}
		
		if (liveChatSessionToken != null) {
			// New customer follow-up functionality when agentId is provided
			if (StringUtils.isNotBlank(agentId)) {
				log.info("Processing customer follow-up for sessionId: {}, agentId: {}", sessionId, agentId);
				try {
					triggerCustomerFollowup(liveChatSessionToken, agentId);
				} catch (Exception e) {
					log.error("Error triggering customer follow-up for sessionId: {}, agentId: {}: {}", 
							sessionId, agentId, e.getMessage(), e);
					// Continue with existing flow even if follow-up fails
				}
			} else {
				// Existing auto-reply flow when agentId is not provided
				log.info("email mandatory value: {} for widgetConfigId: {}", BooleanUtils.isTrue(liveChatSessionToken.getEmailMandatory()), widgetConfigId);
				if (!BooleanUtils.isTrue(liveChatSessionToken.getEmailMandatory())) {
					if (sendAutoReply) {
						log.info("sending auto reply after session timeout for sessionId: {} and widgetConfigId: {}",
								sessionId, widgetConfigId);
						sendAutoReplyMessageToCustomer(liveChatSessionToken.getBusinessId(),
								liveChatSessionToken.getCustomerId(),liveChatSessionToken.getMcid(), widgetConfigId, false);
					} else if (!lastMessageReplied) {
						log.info(
								"sending livechat text auto reply after session timeout for sessionId: {} and widgetConfigId: {}",
								sessionId, widgetConfigId);
						sendAutoReplyMessageToCustomer(liveChatSessionToken.getBusinessId(),
								liveChatSessionToken.getCustomerId(),liveChatSessionToken.getMcid(), widgetConfigId, !lastMessageReplied);
					}
				}
			}
			
			LiveChatSessionTimeoutResponse liveChatSessionTimeoutResponse = new LiveChatSessionTimeoutResponse();
			liveChatSessionTimeoutResponse.setMessage("Thank you for contacting us.");
			return liveChatSessionTimeoutResponse;
		}
		return null;
	}

	// Session Refresh returns back the same session by marking it active.
	@Override
	public LiveChatSessionRefreshResponseDTO sessionRefresh(String sessionId, Integer widgetId, Integer source) {
		Optional<LiveChatSessionToken> sessionTokenOptional = sessionService.findBySessionId(sessionId);
		if (sessionTokenOptional.isPresent()) {
			LiveChatSessionToken sessionToken = sessionTokenOptional.get();
			Boolean isReceivedDuringBusinessHr = null;
			BusinessTimingDTO businessTimingDTO = businessService.getBusinessTimings(sessionToken.getBusinessId(),false);
			if (businessTimingDTO != null) {
				isReceivedDuringBusinessHr = BusinessHoursUtility.isReceivedDuringBusinessHr(businessTimingDTO,
						new Date());
			}
			sessionToken.setStatus(getSessionStatusOnRefresh(sessionToken, widgetId, source,isReceivedDuringBusinessHr,businessTimingDTO));
			sessionToken.setLastStatus(sessionToken.getStatus());
			sessionToken.setBusinessUserReplied(false);
			sessionService.save(sessionToken);
			LiveChatSessionRefreshResponseDTO liveChatSessionRefreshResponseDTO = convertToSessionRefreshDTO(
					sessionToken);
			liveChatSessionRefreshResponseDTO.setIsReceivedDuringBusinessHours(isReceivedDuringBusinessHr);
			return liveChatSessionRefreshResponseDTO;
		}
		throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.INVALID_LIVECHAT_REFRESH_EVENT,  HttpStatus.BAD_REQUEST));
	}

	private ChatSessionStatus getSessionStatusOnRefresh(LiveChatSessionToken sessionToken, Integer widgetId, Integer source,Boolean isReceivedDuringBusinessHr,BusinessTimingDTO businessTimingDTO) {
		Date lastActivityDate = sessionToken.getUpdated();
		LocalDateTime lastActivityDateTime = DateUtils.convertDateToLocalDateTime(lastActivityDate);

		log.info("[LivechatSessionServiceImpl] getSessionStatusOnRefresh for sessionToken:{} lastActivityDate:{}", sessionToken.getSessionId(), lastActivityDateTime);
		// If customer refreshed a session before it timed-out, return the last state of the session.
		if(ChatSessionStatus.TERMINATED != sessionToken.getStatus() && lastActivityDateTime.isAfter(LocalDateTime.now().minus(Duration.ofSeconds(Constants.LIVECHAT_TIMEOUT_IN_SEC)))) {
			log.info("[LivechatSessionServiceImpl] getSessionStatusOnRefresh for sessionToken:{} lastActivityDate:{} returning last state", sessionToken, lastActivityDateTime);
			removeLivechatEndKey(sessionToken.getMcid());
			return sessionToken.getLastStatus();
		}
		// If refresh happens anytime after the timeout window, handle it as per business CHATBOT configuration
		else {
			BusinessDTO business = businessService.getBusinessDTO(sessionToken.getBusinessId());
			log.info("[LivechatSessionServiceImpl] getSessionStatusOnRefresh for sessionToken:{} lastActivityDate:{} checking if chatbot is enabled..", sessionToken, lastActivityDateTime);
			// adding Livechat-restart activity on after session is renewed.
			logLiveChatActivity(sessionToken, source, ActivityType.LIVECHAT_RESTART,null);
			BusinessChatWidgetConfig businessChatWidgetConfig = null;
			if(widgetId!=null) {
				// this path should be used always
				businessChatWidgetConfig = businessChatWidgetConfigService.getWebChatConfigByWidgetId(widgetId);
			} else {
				businessChatWidgetConfig = businessChatWidgetConfigService.getBusinessChatWidgetConfig(business);
			}
			return isChatBotEnabledForBusiness(business, widgetId,isReceivedDuringBusinessHr,businessTimingDTO,businessChatWidgetConfig);
		}
	}

	private void removeLivechatEndKey(Integer mcid) {
		String redisKey = "LIVECHAT_END_EVENT:" + mcid;
		redisHandler.deleteKey(redisKey);
	}

	private LiveChatSessionRefreshResponseDTO convertToSessionRefreshDTO(LiveChatSessionToken liveChatSessionToken) {
		LiveChatSessionRefreshResponseDTO liveChatSessionRefreshResponseDTO = new LiveChatSessionRefreshResponseDTO();
		liveChatSessionRefreshResponseDTO.setAccountId(liveChatSessionToken.getAccountId());
		liveChatSessionRefreshResponseDTO.setBusinessId(liveChatSessionToken.getBusinessId());
		liveChatSessionRefreshResponseDTO.setCustomerId(liveChatSessionToken.getCustomerId());
		liveChatSessionRefreshResponseDTO.setMc_id(liveChatSessionToken.getMcid());
		liveChatSessionRefreshResponseDTO.setSessionId(liveChatSessionToken.getSessionId());
		CustomerDTO customer = contactService.findByIdNoCaching(liveChatSessionToken.getCustomerId());
		if(StringUtils.isNotEmpty(customer.getEmailId())){
			liveChatSessionRefreshResponseDTO.setCustomerEmail(customer.getEmailId());
		}
		if(StringUtils.isNotEmpty(customer.getPhoneE164())){
			liveChatSessionRefreshResponseDTO.setCustomerPhone(customer.getPhoneE164());
		}
		liveChatSessionRefreshResponseDTO.setCustomerPhone(customer.getPhone());
		return liveChatSessionRefreshResponseDTO;
	}

	@Override
	public void pushTypingStateChangeEvent(String type, ConversationStateDTO event) {
		log.info("inside pushTypingStateChangeEvent for type {} event {} and eventState {}  ", type, event,
				StringUtils.isNotBlank(event.getState()) ? event.getState() : null);
		// Fetch business if exists
		BusinessDTO business = null;
		if (event.getBusinessNumber() != null)
			business = businessService.getBusinessByBusinessNumber(event.getBusinessNumber());

		if (business == null) {
			log.warn("No business found for businessId {}", event.getBusinessId());
			return;
		}
		if (event.getSource() != null && event.getSource() == 16) {
			event.setBusinessId(business.getBusinessId());
			event.setEnterpriseId(business.getEnterpriseId());
			event.setEnterpriseNumber(business.getEnterpriseNumber());
			addAppleTypingEventToRedis(type, event);
			pushMessageToSQSQueue(event, 5);
			return;
		}
		updateTypingEvent(type, event, business.getBusinessId());
		addTypingEventToRedis(type, event);
		pushMessageToSQSQueue(event, 5);
		updateSessionIfCustomer(type, event);
	}

	private void addAppleTypingEventToRedis(String type, ConversationStateDTO event) {
		if (TypingEventSource.USER.source.equals(type) || TypingEventSource.ROBIN.source.equals(type)) {
			String redisKey = "TYPING_EVENT:" + event.getMc_id() + "_" + event.getTypingUsers().get(0).getId();
			Long redisKeyTTL = redisHandler.getRedisKeyTTL(redisKey, TimeUnit.SECONDS);
			if (redisKeyTTL <= 0) {
				redisHandler.setOpsForValueWithExpiry(redisKey, event.getTypingUsers().get(0).getId(), 5l,
						TimeUnit.SECONDS);
				appleSendHandler.sendTypingEvent(event, Constants.APPLE_TYPING_START_EVENT);
			} else if (redisKeyTTL <= 4) {
				redisHandler.setOpsForValueWithExpiry(redisKey, event.getTypingUsers().get(0).getId(), 5l,
						TimeUnit.SECONDS);
			}
		}
	}

	@Override
	public void addTypingEventForRobin(ConversationStateDTO event) {
		addAppleTypingEventToRedis("robin", event);
		pushMessageToSQSQueue(event, 5);
	}

	private void updateSessionIfCustomer(String type, ConversationStateDTO event) {
		if(TypingEventSource.CUSTOMER.source.equals(type)) {
			Optional<LiveChatSessionToken> session = sessionService.getExistingSession(event.getBusinessId(), event.getMc_id());
			if(session.isPresent() && session.get().getStatus() != ChatSessionStatus.TERMINATED) {
				session.get().setUpdated(new Date());
				sessionService.save(session.get());
			}else {
				log.info("no active session found for bid : {}, mcid : {} ", event.getBusinessId(), event.getMc_id());
				// silently return if no session is found. currenly UI sends typing event
				// irrespective whether a livechat session is active or not
			}
		}
	}

	/**
	 * Mark stale sessions as inactive, invoked in a scheduled manner
	 */
	@Override
	public void terminateStaleSessions() {
		LocalDateTime updatedLocalDateTimeBefore = LocalDateTime.now()
				.minus(Duration.ofSeconds(getStaleSessionsBeforeSeconds()));
		Date updatedDateBefore = DateUtils.convertDateFromLocalDateTime(updatedLocalDateTimeBefore);
		List<LiveChatSessionToken> staleActiveSessions = sessionService.findStaleActiveSessions(updatedDateBefore);

		// Note: Push to Kafka for throttled processing, if number of such sessions are in bulk
		if (CollectionUtils.isNotEmpty(staleActiveSessions)) {
			List<String> sessionIds = staleActiveSessions.stream().map(sas -> sas.getSessionId()).collect(Collectors.toList());
			log.info("[LiveChatStaleSessionTerminationScheduler] marking sessions inactive for sessionIds:{}", sessionIds);
			sessionService.updateStatusForStaleSessions(sessionIds);
			// adding Livechat-timeout activity for all stale sessions
			staleActiveSessions.forEach(sessionToken -> {
				logLiveChatActivity(sessionToken, 0, ActivityType.LIVECHAT_TIMEOUT, null);
                try {
                    if(Objects.nonNull(sessionToken) && Objects.nonNull(sessionToken.getBusinessId()) && Objects.nonNull(sessionToken.getAccountId()) &&
                            Objects.nonNull(sessionToken.getMcid()) && Objects.nonNull(sessionToken.getCustomerId()) && Objects.nonNull(sessionToken.getWidgetId())) {
                        
                        String autoReplyEnabledAccounts = CacheManager.getInstance().getCache(SystemPropertiesCache.class)
                                .getProperty("accounts_with_stale_session_auto_reply_enabled", "261968");
                        List<String> autoReplyEnabledAccountsList = ControllerUtil.getTokensListFromString(autoReplyEnabledAccounts);
                        
                        if (CollectionUtils.isNotEmpty(autoReplyEnabledAccountsList) && 
                                autoReplyEnabledAccountsList.contains(String.valueOf(sessionToken.getAccountId()))) {
                            sendAutoReplyMessageToCustomer(
                                    sessionToken.getBusinessId(),
                                    sessionToken.getCustomerId(),
                                    sessionToken.getMcid(),
                                    sessionToken.getWidgetId(),
                                    true
                            );
                        } else {
                            log.info("Auto reply not enabled for account: {}", sessionToken.getAccountId());
                        }
                    }
                } catch (Exception e) {
					log.error("Error sending auto reply message for session: {}",
							sessionToken != null ? sessionToken.getSessionId() : "null", e);
                }
            });
		} else {
			log.info("[LiveChatStaleSessionTerminationScheduler] NO stale sessions found before timestamp:{}", updatedLocalDateTimeBefore.toString());
		}
	}

	// TODO make it config driven
	private int getStaleSessionsBeforeSeconds() {
		return Constants.LIVECHAT_TIMEOUT_IN_SEC;
	}

	private void logLiveChatActivity(LiveChatSessionToken liveChatSessionToken, Integer source, ActivityType activityType, TeamDto team) {
		log.info("[logLiveChatActivity][{}] - Received log livechat activity event for source {} and session details {}", activityType, source, liveChatSessionToken);
		conversationActivityService.persistActivityInStoreAndMirror(liveChatSessionToken, source, activityType,team);
		updateTypingEventForCustomer(liveChatSessionToken.getBusinessId(), liveChatSessionToken.getMcid(), activityType);
	}


	private void addTypingEventToRedis(String type, ConversationStateDTO event) {
		if (ActivityType.LIVECHAT_END.getLabel().equalsIgnoreCase(event.getState())) {
			String redisKey = "LIVECHAT_END_EVENT:" + event.getMc_id();
			redisHandler.setOpsForValueWithExpiry(redisKey, event.getMc_id(), 30l, TimeUnit.SECONDS);
		} else if (TypingEventSource.USER.source.equals(type)) {
			String redisKey = "TYPING_EVENT:" + event.getMc_id() + "_" + event.getTypingUsers().get(0).getId();
			Long redisKeyTTL = redisHandler.getRedisKeyTTL(redisKey, TimeUnit.SECONDS);
			if (redisKeyTTL <= 0) {
				redisHandler.setOpsForValueWithExpiry(redisKey, event.getTypingUsers().get(0).getId(), 5l,
						TimeUnit.SECONDS);
				updateFirebaseMessageForTypingEvent(event, false);
			} else if (redisKeyTTL <= 4) {
				redisHandler.setOpsForValueWithExpiry(redisKey, event.getTypingUsers().get(0).getId(), 5l,
						TimeUnit.SECONDS);
			}

		} else if (TypingEventSource.CUSTOMER.source.equals(type)) {
			String redisKey = "CUSTOMER_TYPING_EVENT:" + event.getBusinessId() + "_" + event.getMc_id();
			Long customerKeyTTL = redisHandler.getRedisKeyTTL(redisKey, TimeUnit.SECONDS);
			if (customerKeyTTL <= 0) {
				redisHandler.setOpsForValueWithExpiry(redisKey, "true", 5l, TimeUnit.SECONDS);
				updateFirebaseMessageForTypingEvent(event, false);
			} else if (customerKeyTTL <= 4) {
				redisHandler.setOpsForValueWithExpiry(redisKey, "true", 5l, TimeUnit.SECONDS);
			}
		}
	}

	public String pushMessageToSQSQueue(ConversationStateDTO event, Integer delay) {
		log.info("Going to produce SQS message for businessId {} and mc_id {}", event.getBusinessId(),
				event.getMc_id());
		String sqsMessageId = amazonSQSUtility.publishMessageToSQS(JSONUtils.toJSON(event), sqsQueueUrl,
				delay);
		log.info("SQS messageID {} for businessId {} and mc_id {}", sqsMessageId, event.getBusinessId(),
				event.getMc_id());
		return sqsMessageId;
	}


	@Override
	public void consumeSQSMessage(ConversationStateDTO event) {
		try {
			if (event.getSource() != null && event.getSource() == 16) {
				if (CollectionUtils.isNotEmpty(event.getTypingUsers())) {
					UserDetailDTO user = event.getTypingUsers().get(0);
					String redisKey = "TYPING_EVENT:" + event.getMc_id() + "_" + user.getId();
					String values = redisHandler.getKeyValueFromRedis(redisKey);
					if (StringUtils.isBlank(values)) {
						appleSendHandler.sendTypingEvent(event, Constants.APPLE_TYPING_END_EVENT);
					}
				}
				return;
			}else if(ActivityType.LIVECHAT_END.getLabel().equalsIgnoreCase(event.getState())) {
				String redisKey = "LIVECHAT_END_EVENT:" + event.getMc_id();
				if (StringUtils.isNotBlank(redisHandler.getKeyValueFromRedis(redisKey))) {
					ConversationStateDTO savedConvState = getCommStateFromRedis(event.getBusinessId(),
							event.getMc_id());
					if (savedConvState == null || event.getSentAt() > savedConvState.getSentAt()) {
						sessionTerminated(event.getSessionId(), event.getSource(), ActivityType.LIVECHAT_END);
					}
					removeLivechatEndKey(event.getMc_id());
				}
			}else if (CollectionUtils.isNotEmpty(event.getTypingUsers())) {
				UserDetailDTO user = event.getTypingUsers().get(0);
				String redisKey = "TYPING_EVENT:" + event.getMc_id() + "_" + user.getId();
				String values = redisHandler.getKeyValueFromRedis(redisKey);
				if (StringUtils.isBlank(values)) {
					updateFirebaseMessageForTypingEvent(event, true);
				}
			}else {
				String customerTypingRedisKey = "CUSTOMER_TYPING_EVENT:" + event.getBusinessId() + "_" + event.getMc_id();
				String redisValue = redisHandler.getKeyValueFromRedis(customerTypingRedisKey);
				if (StringUtils.isBlank(redisValue)) {
					updateFirebaseMessageForTypingEvent(event, true);
				}
			}
		} catch (Exception e) {
			log.error("Exception while consuming SQS messsage for businessId {} and mc_id {} ", event.getBusinessId(),
					event.getMc_id(), e);
		}
	}

	@Override
	public void updateConvStateOnMessageSend(SendMessageDTO message) {
		ConversationStateDTO typingevent = new ConversationStateDTO(message);
		updateFirebaseMessageForTypingEvent(typingevent, true);
	}

	public void updateTypingEventForCustomer(Integer businessId, Integer conversationId, ActivityType activityType) {
		log.info("[updateTypingEventForCustomer] - update typing event invoked for businessId: {}, conversationId: {} and activity: {}", businessId, conversationId, activityType);
		ConversationStateDTO commState = getCommStateFromRedis(businessId, conversationId);
		if(commState != null) {
			log.info("[updateTypingEventForCustomer] - typing event found for businessId: {}, conversationId: {} and activity: {} is {}", businessId, conversationId, activityType, commState);
			setStateIfCustomer("customer", commState, activityType);
		} else {
			log.info("[updateTypingEventForCustomer] - no typing event found for businessId: {}, conversationId: {} and activity: {}", businessId, conversationId, activityType);
			commState = createDefaultCommState(businessId, conversationId, activityType);
		}
		updateTypingUserDetailsToRedis(commState);
		fcmService.pushTypingEventToFirebase(commState);
	}

	private ConversationStateDTO createDefaultCommState(Integer businessId, Integer conversationId, ActivityType activityType) {
		ConversationStateDTO convState = new ConversationStateDTO();
		convState.setBusinessId(businessId);
		convState.setMc_id(conversationId);
		convState.setSentAt(ZonedDateTime.now(ZoneId.of("UTC")).toInstant().toEpochMilli());
		convState.setCustomerTyping(false);
		convState.setTypingUsers(new ArrayList<>());
		if (activityType != null && StringUtils.isNotBlank(activityType.getLabel())) {
			convState.setState(activityType.getLabel());
		}
		return convState;
	}

	private void setStateIfCustomer(String type, ConversationStateDTO event, ActivityType activityType) {
		if(TypingEventSource.CUSTOMER.source.equals(type))
			event.setState(activityType.getLabel());
	}

	public ConversationStateDTO getCommState(ConversationStateDTO event) {
		ConversationStateDTO savedConvState = getCommStateFromRedis(event.getBusinessId(), event.getMc_id());
		if(savedConvState == null) {
			savedConvState = createDefaultCommState(event.getBusinessId(), event.getMc_id(), null);
		}
		return savedConvState;
	}


	public ConversationStateDTO getCommStateFromRedis(Integer businessId, Integer conversationId) {
		String redisKey = "USERS_DETAIL:" + businessId + "_" + conversationId;
		try {
			if (StringUtils.isNotBlank(redisHandler.getKeyValueFromRedis(redisKey))) {
				return convertToTypingEvent(redisHandler.getKeyValueFromRedis(redisKey));
			}
		} catch (Exception e) {
			log.error("Exception while fetching value from redis for mc_id {} and businessId {} ", conversationId,
					businessId, e);
		}
		return null;
	}

	private void updateTypingUserDetailsToRedis(ConversationStateDTO event) {
		String redisKey = "USERS_DETAIL:" + event.getBusinessId() + "_" + event.getMc_id();
		redisHandler.setOpsForValueWithExpiry(redisKey, event, 30l, TimeUnit.MINUTES);
	}


	private ConversationStateDTO convertToTypingEvent(String json) {
		ObjectMapper mapper = new ObjectMapper();
		ConversationStateDTO typingEventDto = null;
		try {
			typingEventDto = mapper.readValue(json, ConversationStateDTO.class);
		} catch (Exception e) {
			log.error("Error while converting typing event to userDetail ", e);
		}
		return typingEventDto;
	}

	private List<UserDetailDTO> convertToUsersDeatil(ConversationStateDTO event) {
		return CollectionUtils.isNotEmpty(event.getTypingUsers()) ? event.getTypingUsers()
				: new ArrayList<UserDetailDTO>();
	}

	private ConversationStateDTO updateTypingEvent(String type, ConversationStateDTO event, Integer businessId) {
		event.setSentAt(ZonedDateTime.now(ZoneId.of("UTC")).toInstant().toEpochMilli());
		event.setBusinessId(businessId);
		return event;
	}

	@Override
	public void updateFirebaseMessageForAppleTypingEvent(ConversationStateDTO event, Integer businessId, Integer mcId,
														 boolean customerTyping) {
		log.info("Going to update firebase msg for mc_id  {}   ", mcId);
		ConversationStateDTO savedTypingEvent = null;
		if (Objects.isNull(event)) {
			savedTypingEvent = new ConversationStateDTO();
			savedTypingEvent.setMc_id(mcId);
			savedTypingEvent.setBusinessId(businessId);
			savedTypingEvent.setSentAt(new Date().getTime());
			savedTypingEvent.setCustomerTyping(customerTyping);
		}else {
			savedTypingEvent = event;
		}
		fcmService.pushTypingEventToFirebase(savedTypingEvent);
	}

	@Override
	public void updateFirebaseMessageForTypingEvent(ConversationStateDTO event, boolean typingStopped) {
		log.info("Going to update firebase msg for mc_id  {}   ", event.getMc_id());
		String lockKey = "ACQUIRE_LOCK:" + event.getMc_id();
		if (event != null) {
			int retryCount = 0;
			while (!redisHandler.accureLockOnRedisKey(lockKey, "lock") && retryCount < 10) {
				try {
					retryCount++;
					Thread.sleep(100);
				} catch (InterruptedException e) {
					log.error("Interuppted while waiting to acquire lock on live chat object in redis, Reason: {}", e);
				}
			}
			ConversationStateDTO savedTypingEvent = getCommState(event);
			if (CollectionUtils.isNotEmpty(event.getTypingUsers())) {
				if(typingStopped) {
					UserDetailDTO user = event.getTypingUsers().get(0);
					convertToUsersDeatil(savedTypingEvent).removeIf(existing -> user.getId().equals(existing.getId()));
				}else {
					savedTypingEvent.getTypingUsers().add(event.getTypingUsers().get(0));
				}
			} else {
				savedTypingEvent.setSentAt(ZonedDateTime.now(ZoneId.of("UTC")).toInstant().toEpochMilli());
				if(typingStopped) {
					savedTypingEvent.setCustomerTyping(false);
				}else {
					savedTypingEvent.setCustomerTyping(true);
				}
			}
			updateTypingUserDetailsToRedis(savedTypingEvent);
			redisHandler.deleteKey(lockKey);
			fcmService.pushTypingEventToFirebase(savedTypingEvent);
		}
	}


	@Override
	public void receiveSuggestion(String sessionId, LiveChatMessageDTO liveChatMessageDTO) {

		// 1. Validate the incoming liveChatMessageDTO
		RequestDtoValidator.validateLiveChatSuggestionRequest(liveChatMessageDTO);

		// 2. Fetch an Active Session if exists.
		Optional<LiveChatSessionToken> sessionTokenOptional = sessionService.findActiveSessionBySessionId(sessionId);
		if (!sessionTokenOptional.isPresent()) {
			log.info("[LiveChatServiceImpl] communicateViaLiveChat invalid sessionId:{}", sessionId);
			throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.INVALID_LIVECHAT_SESSION_ID,
					ComponentCodeEnum.LIVECHAT, HttpStatus.BAD_REQUEST));
		}
		liveChatMessageDTO.setEmailMandatory(sessionTokenOptional.get().getEmailMandatory());
		// Fetch business if exists
		BusinessDTO business = null;
		try {
			business = businessService.getBusinessLiteDTOWithLocation(Constants.BUSINESS_NUMBER,liveChatMessageDTO.getBusinessNumber().toString());
		}catch (Exception e){
			log.error("Exception while getting business from core business number:{} ",liveChatMessageDTO.getBusinessNumber(),e);
		}
		if (business == null) {
			throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.INVALID_CHAT_SEND_SMS_BUSINESS_NO,
					ComponentCodeEnum.LIVECHAT,HttpStatus.BAD_REQUEST));
		}

		Integer businessId = business.getBusinessId();
		boolean isFreeAccount = BusinessDTO.isFreeAccount(business);
		if (isFreeAccount) {
			throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.FREE_ACCOUNT_NOT_SUPPORTED, HttpStatus.BAD_REQUEST));
		}

		LiveChatSessionToken sessionToken = sessionTokenOptional.get();
		Integer customerId = sessionToken.getCustomerId();
		Integer mcId = liveChatMessageDTO.getMc_id();
		String customerPhone = liveChatMessageDTO.getMobileNumber();

		// this is because decryption is using the formatted phone
		// but input of live-chat-send request is a non-formatted number
		// we should encrypt and decrypt using E164 format everywhere
		CustomerDTO cust = contactService.findById(customerId);
		customerPhone = cust.getPhone();
		Optional<Lock> lockOpt = Optional.empty();
		try {
			String lockKey = Constants.CUSTOMER_ID_PREFIX+cust.getId();
			lockOpt = redisLockService.tryLock(lockKey, 1,TimeUnit.SECONDS);
			if (!lockOpt.isPresent()) {
				//Reject such events
				log.info("[Webchat send] Unable to acquire lock key:{}",lockKey);
				throw new MessengerException(ErrorCode.WEBCHAT_SEND_LOCK);
			}
			// update firebase message for typing event
			updateFireBaseMessenger(businessId,liveChatMessageDTO.getMc_id());

			// Incoming Message to Sms conversion
			liveChatMessageDTO.setMessage(liveChatMessageDTO.getSuggestion().getValue());
			LiveChatMessageObject incomingCustomerMessage = prepareLiveChatMessageDto(liveChatMessageDTO, customerId, businessId);
			incomingCustomerMessage.setPageUrlTrackedByGoogleAnalytics(liveChatMessageDTO.getPageUrlTrackedByGoogleAnalytics());
			log.debug("[LiveChatServiceImpl]Customer to Business SMS Message for businessId::{}, apiKey:{}, customerId:{} ",
					businessId, liveChatMessageDTO.getApiKey(), customerId);

			// Incoming customer message persisted to sms entity and encrypted.
			LiveChatMessage liveChatMessage = liveChatMessageService.saveLiveChatMessageFromCustomer(incomingCustomerMessage,MessageStatusEnum.RECEIVED);

			// TODO, set communication direction accordingly
			ConversationDTO conversationDTO = fetchLiveChatConversationDTO(CommunicationDirection.RECEIVE, MessageType.CHAT);
			LiveChatMessageObject liveChatMessageObject=new LiveChatMessageObject(liveChatMessage, conversationDTO);
			liveChatMessageObject.setPageUrlTrackedByGoogleAnalytics(liveChatMessageDTO.getPageUrlTrackedByGoogleAnalytics());
			liveChatMessageObject.setMessageBodyUnencrypted(liveChatMessageDTO.getMessage());

			// TODO Why ??
			messengerMessageService.saveMessengerMessage(liveChatMessageObject, null);

			//Perform PostActivity on receiving a live-chat message, like ES update, etc.
			Entry<MessageDocument, ContactDocument> messageAndContactDocument =
					messengerContactService.postLiveChatMessageReceiveActivity
							(business, mcId, liveChatMessageObject, MessengerEvent.LIVECHAT_RECEIVE, customerPhone, MessageTag.UNREAD, null,cust,null);
			MessageDocument messageDocument = messageAndContactDocument.getKey();
			ContactDocument contactDocument = messageAndContactDocument.getValue();
			//-----------------------
			// Check the session status if in AUTO mode.

			Optional<ChatbotQueryResponse> chatbotQueryResponseOptional = Optional.empty();
			if (sessionToken.getStatus() == ChatSessionStatus.AUTO && !Type.DEFAULT.equals(liveChatMessageDTO.getSuggestion().getType())) {
				chatbotQueryResponseOptional = getSuggestionResponse(liveChatMessageDTO.getSuggestion(),business,getQueryChannel(liveChatMessageDTO.getSource()), customerId);
			}
			boolean chatbotToReply = chatbotQueryResponseOptional.isPresent();
			// push received message to firebase, opt for browser notification only if chatbot is not going to reply
			fcmService.pushToFireBaseSync(contactDocument, messageDocument, null, null, !chatbotToReply);

			if (chatbotToReply) {
				sendChatbotReply(business, businessId, cust.getId(), mcId, cust.getPhone(), chatbotQueryResponseOptional.get(),
						liveChatMessageDTO.getSource(), cust, sessionToken);
				commonService.updateRobinResponseTypeInMessage(messageDocument,getRobinResponseType(chatbotQueryResponseOptional.get().getIntentType()));
			}
			// Before sending a mail to business user, check if the status is not manual
			else if(sessionToken.getStatus() != ChatSessionStatus.MANUAL){
				// ****** Mail notification wherever applicable.****** //
				notificationService.processUnreadMessageNotification(business, messageDocument);
			}
			if (Type.DEFAULT.equals(liveChatMessageDTO.getSuggestion().getType())) {
//				sessionToken.setStatus(ChatSessionStatus.MANUAL);
				boolean sendLiveChatAutoReply=sessionToken.getAutoReplySent() == null;
				boolean hasAgentRespondedWithinConfiguredTimeWindow = messengerService.hasAgentRespondedWithinConfiguredTimeWindow(business.getRoutingId(), mcId);
				if(!hasAgentRespondedWithinConfiguredTimeWindow) {
					sendLiveChatAutoReply(contactDocument, business, null, liveChatMessageDTO, cust,sendLiveChatAutoReply);
				}
				if (sessionToken.getAutoReplySent() == null && !hasAgentRespondedWithinConfiguredTimeWindow){
					sessionToken.setAutoReplySent(true);
				}
			}
			// TODO: Update the last updated date in session token table, and prevent its timeout, not working
			sessionToken.setUpdated(new Date());
			sessionService.save(sessionToken);
		} finally {
			if (lockOpt.isPresent()) {
				redisLockService.unlock(lockOpt.get());
			}
		}
	}

	private RobinResponseType getRobinResponseType(String intentType) {
		RobinResponseType responseType = null;
		if (RobinResponseType.INTENT.name().equals(intentType)) {
			responseType = RobinResponseType.INTENT;
		} else if (RobinResponseType.FAQ.name().equals(intentType)) {
			responseType = RobinResponseType.FAQ;
		}
		return responseType;
	}

	private Optional<ChatbotQueryResponse> getSuggestionResponse(Suggestion suggestion,
																 BusinessDTO business, QueryChannel queryChannel, Integer customerId) {
		Optional<ChatbotQueryResponse> chatbotQueryResponseOptional=Optional.empty();
		if (Type.FAQ.equals(suggestion.getType())) {
			chatbotQueryResponseOptional = getQueryResponseFromCache(suggestion, business.getRoutingId(),business.getBusinessId(),queryChannel);
			if (chatbotQueryResponseOptional.isPresent()) {
				return chatbotQueryResponseOptional;
			}
		}
		return chatbotQueryResponseOptional = chatbotService.getSuggestionResponse(business.getRoutingId(),business.getBusinessId() ,suggestion,queryChannel, customerId);
	}

	private Optional<ChatbotQueryResponse> getQueryResponseFromCache(Suggestion suggestion,
																	 Integer accountId, Integer businessId, QueryChannel queryChannel) {
		String value = redisHandler
				.getKeyValueFromRedis(getFormattedReddisKey(suggestion.getValue(), accountId));
		ChatbotQueryResponse chatbotQueryResponse = null;
		if (StringUtils.isNotBlank(value)) {
			Map<String, Object> data = new HashMap<>();
			chatbotQueryResponse = new ChatbotQueryResponse();
			chatbotQueryResponse.setIntentType("FAQ");
			chatbotQueryResponse.setData(data);
			data.put("response", value);
			chatbotQueryResponse.setData(data);
			Integer auditId=chatbotService.auditChatBotQuery(suggestion,accountId,businessId,queryChannel,"FAQ");
			if(auditId==null) {
				throw new MessengerException(ErrorCode.ERROR_WHILE_AUDITING_CHATBOT_QUERY);
			}
			populateEventIdForFeedback(chatbotQueryResponse, auditId);
		}
		return Optional.ofNullable(chatbotQueryResponse);
	}

	@Override
	public void receiveFeedback(String sessionId, FeedbackRequest feedbackRequest) {

		Optional<LiveChatSessionToken> liveChatSessionTokenOptional =  sessionService.findActiveSessionBySessionId(sessionId);
		if (liveChatSessionTokenOptional.isPresent() && CollectionUtils.isEmpty(feedbackRequest.getWidgetAgent())){
			String message = feedbackRequest.getValue() == 1? MessengerConstants.FEEDBACK_MESSAGE_YES:MessengerConstants.FEEDBACK_MESSAGE_NO;
			boolean frenchLocale = checkIfWidgetLocaleIsFrench(liveChatSessionTokenOptional.get().getWidgetId());
			if (frenchLocale) {
				message = feedbackRequest.getValue() == 1? MessengerConstants.FEEDBACK_MESSAGE_YES_FRENCH:MessengerConstants.FEEDBACK_MESSAGE_NO_FRENCH;
			}
			
			LiveChatSessionToken liveChatSessionToken = liveChatSessionTokenOptional.get();
			Integer businessId = liveChatSessionToken.getBusinessId();

			BusinessDTO business = null;
			try {
				business = businessService.getBusinessLiteDTOWithLocation(Constants.BUSINESS_ID,businessId.toString());
			}catch (Exception e){
				log.error("Exception while getting business from core business Id:{} ",businessId,e);
			}
			if (business == null) {
				throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.INVALID_CHAT_SEND_SMS_BUSINESS_NO, HttpStatus.BAD_REQUEST));
			}
			Integer mcId = liveChatSessionToken.getMcid();
			Integer customerId = liveChatSessionToken.getCustomerId();
			CustomerDTO cust = contactService.findById(customerId);
			Optional<Lock> lockOpt = Optional.empty();
			try {
				String lockKey = Constants.CUSTOMER_ID_PREFIX+cust.getId();
				lockOpt = redisLockService.tryLock(lockKey, 1,TimeUnit.SECONDS);
				if (!lockOpt.isPresent()) {
					//Reject such events
					log.info("[Webchat send] Unable to acquire lock key:{}",lockKey);
					throw new MessengerException(ErrorCode.WEBCHAT_SEND_LOCK);
				}
				updateFireBaseMessenger(businessId,customerId);
				//SmsDTO incomingCustomerMessage = prepareSmsDto(message,getWebchatSource(feedbackRequest.getSource()), customerId, businessId);
				if (feedbackRequest.getValue() == 0) {
					// BIRD-96871 - create activity when user clicks on talk to an agent.
					ActivityDto talkToAnAgentActivity = ActivityDto.builder().mcId(liveChatSessionToken.getMcid())
							.created(new Date()).actorId(liveChatSessionToken.getCustomerId())
							.activityType(ActivityType.LIVECHAT_TALK_TO_AGENT_CLICK)
							.from(liveChatSessionToken.getMcid()).to(liveChatSessionToken.getBusinessId())
							.accountId(liveChatSessionToken.getAccountId()).source(feedbackRequest.getSource())
							.businessId(liveChatSessionToken.getBusinessId()).build();
					saveActivity(talkToAnAgentActivity);
				}
				LiveChatMessageObject incomingCustomerMessage = prepareLiveChatMessageDto(message,getWebchatSource(feedbackRequest.getSource()), customerId, businessId);
				incomingCustomerMessage.setEmailMandatory(liveChatSessionToken.getEmailMandatory());
				LiveChatMessage liveChatMessage = liveChatMessageService.saveLiveChatMessageFromCustomer(incomingCustomerMessage,MessageStatusEnum.RECEIVED);

				ConversationDTO conversationDTO = fetchLiveChatConversationDTO(CommunicationDirection.RECEIVE, MessageType.CHAT);

				LiveChatMessageObject liveChatMessageObject=new LiveChatMessageObject(liveChatMessage, conversationDTO);
				liveChatMessageObject.setMessageBodyUnencrypted(message);
				liveChatMessageObject.setMessengerContactId(mcId);
				messengerMessageService.saveMessengerMessage(liveChatMessageObject, null);
				//Perform PostActivity on receiving a live-chat message, like ES update, etc.
				Entry<MessageDocument, ContactDocument> messageAndContactDocument =
						messengerContactService.postLiveChatMessageReceiveActivity
								(business, mcId, liveChatMessageObject, MessengerEvent.LIVECHAT_RECEIVE, cust.getPhone(), MessageTag.UNREAD, null,cust,null);
				MessageDocument messageDocument = messageAndContactDocument.getKey();
				ContactDocument contactDocument = messageAndContactDocument.getValue();
				fcmService.pushToFireBaseSync(contactDocument, messageDocument, null, null, true);
				if (feedbackRequest.getValue() == 0) {
					notificationService.processUnreadMessageNotification(business,messageDocument);
					BusinessTimingDTO businessTimingDTO = businessService.getBusinessTimings(business.getBusinessId(),false);
					Boolean isReceivedDuringBusinessHr = null;
					if (businessTimingDTO != null){
						isReceivedDuringBusinessHr=BusinessHoursUtility.isReceivedDuringBusinessHr(businessTimingDTO,new Date());
					}
					BusinessChatWidgetConfig businessChatWidgetConfig=businessChatWidgetConfigService
							.getBusinessChatWidgetConfig(feedbackRequest.getWidgetConfigId());
					Boolean isLiveChatEnabled=businessChatWidgetConfig!=null&&businessChatWidgetConfig.getLivechatEnabled()!=null&&businessChatWidgetConfig.getLivechatEnabled()==1;
					if(isReceivedDuringBusinessHr){
						if(isLiveChatEnabled) {
							sendOkMessage(liveChatSessionToken, business, feedbackRequest, cust);
						}else {
							sendClosingMessageEventToFirebase(isLiveChatEnabled, isReceivedDuringBusinessHr, contactDocument);
						}
					}else{
						if(isLiveChatEnabled){
							if(liveChatSessionToken.getAutoReplySent()==null){
								boolean hasAgentRespondedWithinConfiguredTimeWindow=messengerService.hasAgentRespondedWithinConfiguredTimeWindow(business.getRoutingId(),mcId);
								if(!hasAgentRespondedWithinConfiguredTimeWindow){
									sendAutoReplyMessageBasedOnBusinessHours(liveChatSessionToken,business,feedbackRequest,
											cust,isReceivedDuringBusinessHr);
									liveChatSessionToken.setAutoReplySent(true);
									sessionService.save(liveChatSessionToken);
								}
							}
							sendClosingMessageEventToFirebase(isLiveChatEnabled,isReceivedDuringBusinessHr,contactDocument);
						}else{
							sendClosingMessageEventToFirebase(isLiveChatEnabled,isReceivedDuringBusinessHr,contactDocument);
						}
					}
				}else {
					sendAutoReplyMessage(liveChatSessionToken,business,feedbackRequest,cust,MessengerConstants.FEEDBACK_RESPONSE_YES);
				}

				chatbotService.updateFeedback(feedbackRequest, business.getAccountId());
				try {
					if(MessengerConstants.FEEDBACK_MESSAGE_NO.equals(message)) {
						roundRobinAssignmentService.goForRoundRobinAssignment(business, contactDocument);
					}
				} catch (Exception ex) {
					log.info("Error in RoundRobin Assignment for accId : {} and mcID : {}", contactDocument.getE_id(), contactDocument.getM_c_id());
				}
			} finally {
				if (lockOpt.isPresent()) {
					redisLockService.unlock(lockOpt.get());
				}
			}
		}else if(CollectionUtils.isNotEmpty(feedbackRequest.getWidgetAgent())){
			BusinessDTO business = null;
			LiveChatSessionToken liveChatSessionToken = liveChatSessionTokenOptional.get();
			Integer businessId = liveChatSessionToken.getBusinessId();
			try {
				business = businessService.getBusinessLiteDTOWithLocation(Constants.BUSINESS_ID,businessId.toString());
			}catch (Exception e){
				log.error("Exception while getting business from core business Id:{} ",businessId,e);
			}
			if (business == null) {
				throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.INVALID_CHAT_SEND_SMS_BUSINESS_NO, HttpStatus.BAD_REQUEST));
			}
			chatbotService.updateFeedback(feedbackRequest, business.getAccountId());
		}
	}


	private void sendAutoReplyMessageToCustomer(Integer businessId, Integer customerId,
												Integer mcId, Integer widgetConfigId, boolean sendLiveChatTextMessage) {
//		platformService.triggerAutoReplyForWebchat(businessId, customerId, widgetConfigId);
		BusinessDTO businessDto = getBusinessDto(businessId);
		boolean hasAgentRespondedWithinConfiguredTimeWindow = messengerService.hasAgentRespondedWithinConfiguredTimeWindow(businessDto.getRoutingId(), mcId);
		if (!hasAgentRespondedWithinConfiguredTimeWindow) {
			CustomerDTO customer = contactService.findById(customerId);
			webchatService.sendAutoReplyToCustomerForWebchatSMS(widgetConfigId, businessDto, customer,
					sendLiveChatTextMessage);
		}
	}

	private BusinessDTO getBusinessDto(Integer businessId) {
		BusinessDTO businessDto = null;
		try {
			businessDto = businessService.getBusinessLiteDTOWithLocation(Constants.BUSINESS_ID,businessId.toString());
		}catch (Exception e){
			log.error("Exception while getting business from core business Id:{} ",businessId,e);
		}
		if (businessDto == null) {
			throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.INVALID_CHAT_SEND_SMS_BUSINESS_NO, HttpStatus.BAD_REQUEST));
		}
		String countryCode=(Objects.nonNull(businessDto.getLocation()) && StringUtils.isNotBlank(businessDto.getLocation().getCountryCode()))?businessDto.getLocation().getCountryCode():null;
		businessDto.setPhoneNumber(businessDto.getPhone());
		businessDto.setCountryCode(countryCode);
		return businessDto;
	}

	private void updateFireBaseMessenger(Integer businessId , Integer mcId){
		ConversationStateDTO event = new ConversationStateDTO();
		event.setBusinessId(businessId);
		event.setMc_id(mcId);
		event.setCustomerTyping(false);
		updateFirebaseMessageForTypingEvent(event, true);
	}

	private void sendOkMessage(LiveChatSessionToken liveChatSessionToken, BusinessDTO business,FeedbackRequest feedbackRequest,CustomerDTO customerDTO){

		sendChatbotAutoReply(business,liveChatSessionToken.getEmailMandatory(),liveChatSessionToken.getMcid(),customerDTO.getPhone(),MessengerConstants.OKAY_MESSAGE,feedbackRequest.getSource(),customerDTO,null,true, Boolean.FALSE);
	}

	private void sendAutoReplyMessageBasedOnBusinessHours(LiveChatSessionToken liveChatSessionToken, BusinessDTO business,FeedbackRequest feedbackRequest,CustomerDTO customerDTO,Boolean isReceivedDuringBusinessHour){
		if (isReceivedDuringBusinessHour == null ){
			log.info("Business hour detail not found for business :{}",business.getBusinessId());
			return;
		}

		//Based on business hours we need to get the response.
		LiveChatWidgetConfig liveChatWidgetConfig = liveChatMessageConfigRepository.findByWidgetId(feedbackRequest.getWidgetConfigId());
		if (liveChatWidgetConfig == null){
			log.info("Live chat message config not found for widget config id:{}",feedbackRequest.getWidgetConfigId());
			return;
		}
		String message = getMessagebyBusinessHours(isReceivedDuringBusinessHour,business, liveChatWidgetConfig,customerDTO.getFirstName());
		if (StringUtils.isNotEmpty(message)){
			sendChatbotAutoReply(business,liveChatSessionToken.getEmailMandatory(),liveChatSessionToken.getMcid(),customerDTO.getPhone(),message,feedbackRequest.getSource(),customerDTO,null,true, Boolean.FALSE);
		}
	}


	private String getMessagebyBusinessHours(Boolean isReceivedDuringBusinessHour, BusinessDTO business, LiveChatWidgetConfig liveChatWidgetConfig,String customerFirstName){
		String phoneNumber  = smsService.getFormattedBusinessNumber(business.getBusinessId(), business.getPhone());
		String msgBody = null;
		String textingNumber  = smsService.getFormattedBusinessNumber(business.getBusinessId());
		if (isReceivedDuringBusinessHour) {
			msgBody = liveChatWidgetConfig.getOnlineWelcomeMessage();
		}else {
			msgBody = liveChatWidgetConfig.getOfflineWelcomeMessage();
		}
		//BIRDEYE-98645
		BusinessTimingDTO businessTimingDTO = null;
		if (msgBody.contains(Constants.BUSINESS_HOURS_OLD) || msgBody.contains(Constants.BUSINESS_HOURS_NEW)){
			businessTimingDTO = businessService.getBusinessTimings(business.getBusinessId(),true);
		}
		PublicDataBusinessDTO publicBusinessDto = null;
		if (msgBody.contains(Constants.BUSINESS_SERVICES) || msgBody.contains(Constants.BUSINESS_PAYMENT_OPTIONS)
				|| msgBody.contains(Constants.BUSINESS_LANGUAGES) || msgBody.contains(Constants.BUSINESS_SERVICES_CAP)
				|| msgBody.contains(Constants.BUSINESS_PAYMENT_OPTIONS_CAP) || msgBody.contains(Constants.BUSINESS_LANGUAGES_CAP)){
			publicBusinessDto = businessService.getPublicBusinessData(business.getBusinessId());
		}
		BusinessLocationCustomFieldsTokensDto customFields = businessService.getBusinessCustomFieldsAndTokenByBusinessID(business.getBusinessId());
		BusinessProfileData businessProfileData = businessService.getBusinessProfileData(business.getBusinessId());
		String formattedBody = ControllerUtil.replaceTokens(business, customerFirstName,msgBody,phoneNumber, textingNumber, businessTimingDTO, publicBusinessDto,businessProfileData,customFields);

		return formattedBody;
	}



//	private LiveChatSessionTimeoutResponse getClosingMessageBasedOnBizHours(BusinessTimingDTO businessTimingDTO,BusinessDTO businessDTO,LiveChatWidgetConfig liveChatWidgetConfig){
//		LiveChatSessionTimeoutResponse liveChatSessionTimeoutResponse = new LiveChatSessionTimeoutResponse();
//		if (liveChatWidgetConfig != null){
//			if (BusinessHoursUtility.isReceivedDuringBusinessHr(businessTimingDTO,new Date())) {
//				liveChatSessionTimeoutResponse.setMessage(ControllerUtil.replaceTokens(businessDTO, liveChatWidgetConfig.getOnlineClosingMessageBody()));
//				liveChatSessionTimeoutResponse.setHeader(liveChatWidgetConfig.getOnlineClosingMessageHeader());
//				return liveChatSessionTimeoutResponse;
//			}else {
//				liveChatSessionTimeoutResponse.setMessage(ControllerUtil.replaceTokens(businessDTO, liveChatWidgetConfig.getOfflineClosingMessageBody()));
//				liveChatSessionTimeoutResponse.setHeader(liveChatWidgetConfig.getOfflineClosingMessageHeader());
//				return liveChatSessionTimeoutResponse;
//			}
//		}
//		return liveChatSessionTimeoutResponse;
//	}


	private void sendAutoReplyMessage(LiveChatSessionToken liveChatSessionToken, BusinessDTO business,FeedbackRequest feedbackRequest,CustomerDTO customerDTO,String message){
		if (StringUtils.isNotEmpty(message)){
			sendChatbotAutoReply(business,liveChatSessionToken.getEmailMandatory()
					,liveChatSessionToken.getMcid(),customerDTO.getPhone(),message,feedbackRequest.getSource(),customerDTO,true,true, Boolean.FALSE);
		}
	}


	private ContactDocument sendChatbotAutoReply(BusinessDTO business,Boolean emailMandatory, Integer mcId,
												 String customerPhone,String message, Integer receiveSource,CustomerDTO customerDTO,
												 Boolean updateLastRespondedAt, boolean sendRobinAsUser, Boolean answerFlag) {

		// Update the response from chatbot to our persistence channels - DB/ES.
		//SmsDTO chatbotReplyMessage = prepareChatbotSmsDto(message, customerId, business.getBusinessId(), receiveSource);
		LiveChatMessageObject chatbotReplyMessage=prepareLiveChatMessageDto(message, customerDTO.getId(), business.getBusinessId(), receiveSource);
		chatbotReplyMessage.setEmailMandatory(emailMandatory);
		//Sms chatbotSavedSMS = smsService.saveSmsForChatbotReply(chatbotReplyMessage);
		LiveChatMessage liveChatMessage = liveChatMessageService.saveLiveChatMessageFromCustomer(chatbotReplyMessage,MessageStatusEnum.SENT);
		Integer userId = sendRobinAsUser ? MessengerConstants.ROBIN_REPLY_USER : MessengerConstants.AUTO_REPLY_LIVE_CHAT_USER;
		ConversationDTO chatbotConversationDTO = fetchLiveChatConversationDTO(CommunicationDirection.SEND, MessageType.CHAT);
		//SmsDTO chatbotSms = new SmsDTO(chatbotSavedSMS, chatbotConversationDTO);
		LiveChatMessageObject liveChatMessageObject=new LiveChatMessageObject(liveChatMessage, chatbotConversationDTO);
		liveChatMessageObject.setMessageBodyUnencrypted(message);
		liveChatMessageObject.setAnswerFlag(answerFlag); // GPT answerFlag
		log.info("livechat message object for auto-reply: {}",liveChatMessageObject);
		Entry<MessageDocument, ContactDocument> chatbotMessageAndContactDocument = null;
		// Update SMS reply on ES, hard-coded -10 as userId for chatbot user reply

		chatbotMessageAndContactDocument =
				messengerContactService.postLiveChatMessageReceiveActivity
						(business, mcId, liveChatMessageObject, MessengerEvent.LIVECHAT_SEND, customerPhone, MessageTag.INBOX, userId,customerDTO,updateLastRespondedAt);


		// TODO make sure it is in the same format as the above received message
		// push response to Firebase
		fcmService.pushToFireBaseSync(chatbotMessageAndContactDocument.getValue(), chatbotMessageAndContactDocument.getKey(), null, null,true);
		return chatbotMessageAndContactDocument.getValue();
	}


	@Override
	public void sendFeedbackMessage(String sessionId, Integer sourceId) {

		Optional<LiveChatSessionToken> liveChatSessionTokenOptional = sessionService.findActiveSessionBySessionId(sessionId);
		if (liveChatSessionTokenOptional.isPresent()){
			LiveChatSessionToken liveChatSessionToken = liveChatSessionTokenOptional.get();
			BusinessDTO businessDTO = businessService.getBusinessLiteDTO(liveChatSessionToken.getBusinessId());
			CustomerDTO cust = contactService.findById(liveChatSessionToken.getCustomerId());
			ContactDocument cd = sendChatbotAutoReply(businessDTO,liveChatSessionToken.getEmailMandatory(),liveChatSessionToken.getMcid(),cust.getPhone(),MessengerConstants.FEEDBACK_MESSAGE,sourceId,cust,null,true, Boolean.FALSE);
			if (cd != null){
				sendFeedbackSuggestions(cd);
			}
		}
	}

	private void sendFeedbackSuggestions(ContactDocument contactDocument){
		List<Suggestion> suggestions = new ArrayList<>();
		suggestions.add(new Suggestion(Suggestion.Type.FEEDBACK,"Yes","Yes"));
		suggestions.add(new Suggestion(Suggestion.Type.FEEDBACK,"No","No"));
		SuggestionHolder suggestionHolder = new SuggestionHolder();
		suggestionHolder.setSuggestions(suggestions);
		fcmService.pushToFirebase(contactDocument,suggestionHolder,null,null);
	}

	/**
	 * Populate an eventId which would be an auditId
	 * Any query feedback would be mapped to this eventId(=auditId)
	 * @param chatbotQueryResponse
	 * @param eventId
	 */
	private void populateEventIdForFeedback(ChatbotQueryResponse chatbotQueryResponse, Integer eventId) {
		try {
			if(chatbotQueryResponse !=null && MapUtils.isNotEmpty(chatbotQueryResponse.getData()) && eventId!=null) {
				log.info("Adding eventId in dataMap: {}", eventId);
				chatbotQueryResponse.getData().put(Constants.EVENT_ID, eventId);
			}
		} catch (Exception e) {
			log.error("Exception occured while populating eventId for feedback for auditId: {}", eventId, e);
		}
	}


	private void sendClosingMessageEventToFirebase(Boolean isLiveChatEnabled ,Boolean isReceivedDuringBusinessHr,ContactDocument contactDocument ){
		//if livechat is off need to send closing event based on business hour to UI
		// and if live chat is ON then we need to  send closing event only in case of offline hours
		if(!isLiveChatEnabled && isReceivedDuringBusinessHr != null ){
			pushToFirebase(contactDocument,null,isReceivedDuringBusinessHr,false);
		}else if (isLiveChatEnabled && isReceivedDuringBusinessHr != null && !isReceivedDuringBusinessHr ){
			pushToFirebase(contactDocument,null,isReceivedDuringBusinessHr,false);
		}else if (isLiveChatEnabled && BooleanUtils.isTrue(isReceivedDuringBusinessHr) ) {
			String closingEventDisabledAccounts = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("closing_event_disabled_accounts", "261968");
			List<String> closingEventDisabledAccountsList = ControllerUtil.getTokensListFromString(closingEventDisabledAccounts);
			if (CollectionUtils.isNotEmpty(closingEventDisabledAccountsList) && closingEventDisabledAccountsList.contains(contactDocument.getE_id().toString())) {
				pushToFirebase(contactDocument, null, null, false);
			}else {
				pushToFirebase(contactDocument, null, null, true);
			}
		}
	}

	private void checkAndMergeAnonymousConversation(ChatbotQueryResponse chatbotQueryResponse,ContactDocument contactDocument,BusinessDTO businessDTO) throws Exception{
		MergeCustomerRequest mergeCustomerRequest = new MergeCustomerRequest();
		Boolean pushFirebaseEventForCustomerInfo = false;
		UpdateWebchatContactDetailsRequest updateWebchatContactDetailsRequest = new UpdateWebchatContactDetailsRequest();
		if(StringUtils.isNotBlank((CharSequence)chatbotQueryResponse.getData().get("phoneNumber"))){
			mergeCustomerRequest.setPhone2(chatbotQueryResponse.getData().get("phoneNumber").toString());
			mergeCustomerRequest.setCountryCode(businessDTO.getCountryCode());
			updateWebchatContactDetailsRequest.setCustomerPhone(mergeCustomerRequest.getPhone2());
			pushFirebaseEventForCustomerInfo = true;
		}
		if(StringUtils.isNotBlank((CharSequence)chatbotQueryResponse.getData().get("email"))){
			mergeCustomerRequest.setEmail2(chatbotQueryResponse.getData().get("email").toString());
			updateWebchatContactDetailsRequest.setCustomerEmail(mergeCustomerRequest.getEmail2());
			pushFirebaseEventForCustomerInfo = true;
		}
		updateWebchatContactDetailsRequest.setBusinessId(contactDocument.getB_id());
		updateWebchatContactDetailsRequest.setAccountId(contactDocument.getE_id());
		updateWebchatContactDetailsRequest.setCustomerInfoSource(ContactInfoSourceEnum.AI);
		updateWebchatContactDetailsRequest.setMc_id(contactDocument.getM_c_id());

		mergeCustomerRequest.setCid1(contactDocument.getC_id());
		mergeCustomerRequest.setBusinessId1(contactDocument.getB_id());
		mergeCustomerRequest.setBusinessId2(contactDocument.getB_id());
		mergeCustomerRequest.setAccountId(contactDocument.getE_id());
		mergeCustomerRequest.setCustomerName2(buildCustomerName(updateWebchatContactDetailsRequest));
		List<MergeCustomerResponse> mergeCustomerResponses = new ArrayList<>();
		if(Boolean.TRUE.equals(pushFirebaseEventForCustomerInfo)){
			createCustomerInfoCapturedActivity(updateWebchatContactDetailsRequest);
			mergeCustomerResponses = contactService.anonymousContactMerge(mergeCustomerRequest);
		}
		if(CollectionUtils.isNotEmpty(mergeCustomerResponses)){
			for(MergeCustomerResponse mergeCustomerResponse: mergeCustomerResponses){
				if(!mergeCustomerResponse.getOldCid().equals(mergeCustomerResponse.getNewCid())){
					MessengerContact fromMessengerContact = messengerContactService.getOrCreateContactForExistingCustomer(mergeCustomerResponse.getBusinessId(),mergeCustomerResponse.getOldCid(),contactDocument.getE_id());
					MessengerContact toMessengerContact = messengerContactService.getOrCreateContactForExistingCustomer(mergeCustomerResponse.getBusinessId(),mergeCustomerResponse.getNewCid(),contactDocument.getE_id());
					mergeConversations(fromMessengerContact,toMessengerContact,contactDocument.getE_id());
				}
				updateContactDocument(mergeCustomerResponse.getNewCid());
			}
		}

	}

	private void populateAnonymousCustomerData(LiveChatInitDTO liveChatInitDTO,BusinessDTO businessDto){
		String firstNames = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("anonymous_first_name", "Violet");
		String lastNames = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("anonymous_last_name", "Grill");
		Random random = new Random();

		List<String> firstNameList = Arrays.asList(firstNames.split(","));
		List<String> lastNameList = Arrays.asList(lastNames.split(","));

		// Generate random first and last names
		String firstName = firstNameList.get(random.nextInt(firstNameList.size()));
		String lastName = lastNameList.get(random.nextInt(lastNameList.size()));

		if (liveChatInitDTO.getBusinessNumber() == null) {
			throw new InputValidationException(ErrorCode.INVALID_CHAT_SEND_SMS_BUSINESS_NO);
		}
		String  customerName = null;
		if(Objects.nonNull(liveChatInitDTO) && StringUtils.isNotBlank(liveChatInitDTO.getCustomerLocation())){
			customerName =  String.format("%s %s from %s", firstName, lastName, liveChatInitDTO.getCustomerLocation());
		}else {
			customerName =  String.format("%s %s", firstName, lastName);
		}
		long timestamp = System.currentTimeMillis();
		String customerEmail = String.format("%d_%s%s", timestamp, businessDto.getBusinessId(),Constants.ANONYMOUS_CUSTOMER_DOMAIN);

		if(StringUtils.isBlank(liveChatInitDTO.getName())){
			liveChatInitDTO.setName(customerName);
		}
		if(StringUtils.isBlank(liveChatInitDTO.getEmail())){
			liveChatInitDTO.setEmail(customerEmail);
		}
	}

	private void switchAnonymousConversationLocation(LiveChatInitDTO liveChatInitDTO,ContactDocument contactDocument,CustomerDTO customerDTO,MessengerContact messengerContact){
		BusinessDTO oldBusinessDTO = businessService.getBusinessDTO(liveChatInitDTO.getFromBusinessId());
		Assert.notNull(oldBusinessDTO,
				"SwitchAnonymousConversationLocation: No business returned by core-service for id" + liveChatInitDTO.getFromBusinessId());

		BusinessDTO newBusinessDTO = businessService.getBusinessByBusinessNumber(liveChatInitDTO.getBusinessNumber());
		Assert.notNull(newBusinessDTO,
				"SwitchAnonymousConversationLocation: No business returned by core-service for id" + liveChatInitDTO.getBusinessNumber());

		UserDTO userDTO = getUserDTO(Constants.INBOX_USER_ID);
		if (userDTO == null) {
			throw new NotFoundException(ErrorCode.USER_NOT_FOUND);
		}

		if (ObjectUtils.isEmpty(messengerContact)) {
			// MessengerContact does not exist on newBusinessId
			log.info("messenger contact does not present for the given mcid : {}",liveChatInitDTO.getFromMcId());
		}

			// Tag should be unread
			MessageTag tag = MessageTag.UNREAD;

		LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
		lastMessageMetadataPOJO.setLastMessageType("RECEIVE"); // Setting as RECEIVE so as not to add prefix in
		// conversation
		messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));
		messengerContact.setLastMsgOn(new Date());
		messengerContact.setLastMessage("Added from "
				+(StringUtils.isNotBlank(oldBusinessDTO.getBusinessAlias())?oldBusinessDTO.getBusinessAlias()
				:oldBusinessDTO.getBusinessName()));
		if(Boolean.TRUE.equals(messengerContact.getIsNew())){
			messengerContact.setLastMessage(null);
		}
		messengerContact.setTag(tag.getCode());
		messengerContact.setUpdatedAt(new Date());
		messengerContact.setEncrypted(0);
		messengerContact.setViewedBy(userDTO.getId() != null ? String.valueOf(userDTO.getId()) : null);
		messengerContact= messengerContactService.saveOrUpdateMessengerContact(messengerContact);

		ContactDocument chatTransferDoc = messengerContactService.updateContactOnES(messengerContact, customerDTO,
				newBusinessDTO, tag, userDTO);

		Date created = new Date();

		// Activity for oldBusinessId
		ActivityDto oldBusinessChatTransferActivity = ActivityDto.builder().mcId(liveChatInitDTO.getFromMcId())
				.created(created).updated(created)
				.activityType(ActivityType.WEBCHAT_CONVERSATION_MERGE)
				.activityMeantFor(ActivityMeantFor.CUSTOMER)
				.actorId(customerDTO.getId()).actorName(customerDTO.getDisplayName())
				.source(Source.LIVE_CHAT_RECEIVE.getSourceId())
				.fromBusinessId(oldBusinessDTO.getBusinessId())
				.fromBusinessName(
						StringUtils.isNotBlank(oldBusinessDTO.getBusinessAlias()) ? oldBusinessDTO.getBusinessAlias()
								: oldBusinessDTO.getBusinessName())
				.fromMcId(liveChatInitDTO.getFromMcId())
				.toBusinessId(newBusinessDTO.getBusinessId())
				.toBusinessName(
						StringUtils.isNotBlank(newBusinessDTO.getBusinessAlias()) ? newBusinessDTO.getBusinessAlias()
								: newBusinessDTO.getBusinessName())
				.toMcId(messengerContact.getId())
				.accountId(oldBusinessDTO.getAccountId()).businessId(oldBusinessDTO.getBusinessId())
				.build();

		saveActivity(oldBusinessChatTransferActivity);

		// Activity for newBusinessId
		ActivityDto newBusinessChatTransferActivity = ActivityDto.builder().mcId(messengerContact.getId())
				.created(created).updated(created)
				.activityType(ActivityType.WEBCHAT_CONVERSATION_MERGE)
				.activityMeantFor(ActivityMeantFor.CUSTOMER)
				.actorId(customerDTO.getId()).actorName(customerDTO.getDisplayName())
				.source(Source.LIVE_CHAT_RECEIVE.getSourceId())
				.fromBusinessId(oldBusinessDTO.getBusinessId())
				.fromBusinessName(
						StringUtils.isNotBlank(oldBusinessDTO.getBusinessAlias()) ? oldBusinessDTO.getBusinessAlias()
								: oldBusinessDTO.getBusinessName())
				.fromMcId(liveChatInitDTO.getFromMcId())
				.toBusinessId(newBusinessDTO.getBusinessId())
				.toBusinessName(
						StringUtils.isNotBlank(newBusinessDTO.getBusinessAlias()) ? newBusinessDTO.getBusinessAlias()
								: newBusinessDTO.getBusinessName())
				.toMcId(messengerContact.getId())
				.accountId(newBusinessDTO.getAccountId()).businessId(newBusinessDTO.getBusinessId())
				.build();

		MessageDocument transferActivityDoc = saveActivity(newBusinessChatTransferActivity);

		//push to firebase on account bucket
		fcmService.pushToFireBaseAccountBucket(chatTransferDoc, transferActivityDoc, userDTO.getId(), null,true);

	}

	private UserDTO getUserDTO(Integer userId) {
		UserDTO userDTO = null;
		if (!Objects.isNull(userId)) {
			userDTO = communicationHelperService.getUserDTO(userId);
		}
		return userDTO;
	}

	private MessageDocument saveActivity(ActivityDto activity) {
		conversationActivityService.persistActivityInDatabase(activity, null);
		return conversationActivityService.persistActivityInES(activity);
	}

	private void sendGreetingMessage(MessengerContact messengerContact,BusinessDTO businessDTO,boolean updateLastResponseAt, LiveChatInitDTO liveChatInitDTO) throws Exception {
		SendMessageDTO sendMessageDTO = new SendMessageDTO();
		sendMessageDTO.setFromBusinessId(businessDTO.getBusinessId());
		sendMessageDTO.setToCustomerId(String.valueOf(messengerContact.getId()));
		sendMessageDTO.setCustomerId(messengerContact.getCustomerId());
		sendMessageDTO.setBusinessIdentifierId(String.valueOf(businessDTO.getBusinessId()));
		sendMessageDTO.setSource(Source.LIVE_CHAT_SEND.getSourceId());
		String msgBody = Constants.WEBCHAT_GREETING_MESSAGE;
		if (Objects.nonNull(businessDTO) && Objects.nonNull(businessDTO.getAccountId())) {
			String customGreetingMessageCacheKey="custom_greeting_message:"+businessDTO.getAccountId();
			msgBody = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(customGreetingMessageCacheKey, Constants.WEBCHAT_GREETING_MESSAGE);
		}
		if(CollectionUtils.isNotEmpty(liveChatInitDTO.getWidgetAgent()) && StringUtils.isNotBlank(liveChatInitDTO.getWidgetAgent().get(0))){
			msgBody = responseGeneratorToolService.customGreetingMessageFromResponseGeneratorConfig(liveChatInitDTO.getWidgetAgent().get(0));
		}
		String businessName = StringUtils.isNotBlank(businessDTO.getBusinessName()) ? businessDTO.getBusinessName()
				: businessDTO.getBusinessAlias();
		String formattedText = msgBody.replace("[Business Name]", businessName != null ? businessName : "");
		sendMessageDTO.setBody(formattedText);

		UserDTO userDTO = new UserDTO();
		sendMessageDTO.setUserId(MessengerConstants.ROBIN_REPLY_USER);
		userDTO.setId(MessengerConstants.ROBIN_REPLY_USER);

		sendMessageDTO.setBOT(true);
		sendMessageDTO.setUpdateLastMessage(false);
//      sendMessageDTO.setSentThrough(MessageDocument.SentThrough.WEB);
//		sendMessageDTO.setUpdateLastResponseAt(updateLastResponseAt);
		boolean hasAgentRespondedWithinConfiguredTimeWindow = messengerService.hasAgentRespondedWithinConfiguredTimeWindow(businessDTO.getRoutingId(), messengerContact.getId());
		if(Boolean.FALSE.equals(hasAgentRespondedWithinConfiguredTimeWindow)){
			liveChatSendEventHandler.handle(sendMessageDTO);
		}
	}

	private void mergeConversations(MessengerContact fromMessengerContact, MessengerContact toMessengerContact,
			Integer accountId) throws Exception{
		updateMessengerMessages(fromMessengerContact, toMessengerContact);
		// Merge with the existing conversation available with customer provided
		// We will be transferring notes and reviews to the attributed conversation.
		Optional<ContactDocument> fromContactDocument = messengerContactService.getContactDocument(accountId,
				fromMessengerContact.getId());
		Optional<ContactDocument> toContactDocument = messengerContactService.getContactDocument(accountId,
				toMessengerContact.getId());
		Optional<ContactDocument> contactDocumentOptional = mergeContactDocument(fromContactDocument, toContactDocument,
				toMessengerContact);
		//TODO Revisit this logger after sometime.
		log.info("**** fromContactDocument - {}, toContactDocument-{}", fromContactDocument, toContactDocument);
		updateMessengerContact(toMessengerContact);
		if (contactDocumentOptional.isPresent()) {
			ContactDocument contactDocument = contactDocumentOptional.get();
			messengerContactService.upsertContactDocumentOnESWithRefresh(contactDocument,
					contactDocument.getM_c_id().toString(), contactDocument.getE_id(), false);
		}
		// update Message document with the new McId
		updateMessageDocument(fromMessengerContact.getId(), toMessengerContact.getId(), accountId);
		// After successful merging of the unattributed conversation to the attributed
		// one.
		// Delete the unattributed conversation.
		deleteUnattributedConversation(fromMessengerContact, accountId, fromMessengerContact.getBusinessId());
		SwitchConversationFirebaseEvent switchConversationFirebaseEvent = new SwitchConversationFirebaseEvent(fromMessengerContact.getId(),toMessengerContact.getId(),toMessengerContact.getBusinessId(),"ACTIVITY",ActivityType.WEBCHAT_CONVERSATION_SWITCH);
		pushSwitchConversationEventToFirebase(switchConversationFirebaseEvent);
	}

	private void updateMessengerMessages(MessengerContact from, MessengerContact to) {
		messengerMessageService.updateMessengerMessagesForConversation(from.getId(), to.getId());
	}

	private void updateMessengerContact(MessengerContact messengerContact) {
		messengerContact.setUpdatedAt(new Date());
		messengerContactService.saveOrUpdateMessengerContactWithExistingTransaction(messengerContact);
	}

	private Optional<ContactDocument> mergeContactDocument(Optional<ContactDocument> fromContactDocumentOptional,
			Optional<ContactDocument> toContactDocumentOptional, MessengerContact toMessengerContact) throws Exception{
		if (fromContactDocumentOptional.isPresent() && toContactDocumentOptional.isPresent()) {
			ContactDocument fromContactDocument = fromContactDocumentOptional.get();
			ContactDocument toContactDocument = toContactDocumentOptional.get();
			DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
			toContactDocument.setUpdatedAt(df.format(new Date()));
			if (CollectionUtils.isEmpty(toContactDocument.getReviews())) {
				toContactDocument.setReviews(fromContactDocument.getReviews());
				toContactDocument.setL_review_on(fromContactDocument.getL_review_on());
				toContactDocument.setLastIncomingMessageTime(fromContactDocument.getL_review_on());
			} else {
				List<ContactDocument.Review> reviews = toContactDocument.getReviews();
				reviews.addAll(fromContactDocument.getReviews());
				toContactDocument.setReviews(reviews);
				if (fromContactDocument.getL_review_on() > toContactDocument.getL_review_on()) {
					toContactDocument.setL_review_on(fromContactDocument.getL_review_on());
					toContactDocument.setLastIncomingMessageTime(fromContactDocument.getL_review_on());
				}
			}
			if (toContactDocument.getC_tag().equals(MessengerTagEnum.CAMPAIGN.getId())) {
				toContactDocument.setC_tag(MessengerTagEnum.UNREAD.getId());
				toMessengerContact.setTag(MessengerTagEnum.UNREAD.getId());
				toContactDocument.setHide(false);
			}
			// this is required as unattributed review could have internal notes.
			// so there might be possibility that internal note in unattributed conversation
			// is latest.
			if ((Objects.nonNull(fromContactDocument.getL_msg_on()) && Objects.nonNull(toContactDocument.getL_msg_on())
					&& fromContactDocument.get_l_msg_on_epoch() > toContactDocument.get_l_msg_on_epoch())
					||
					(Objects.nonNull(fromContactDocument.getL_msg_on())
							&& Objects.isNull(toContactDocument.getL_msg_on()))) {
				updateLastMessage(fromContactDocument, toContactDocument, toMessengerContact);
			}
			return Optional.of(toContactDocument);
		}
		return Optional.empty();
	}

	private void updateLastMessage(ContactDocument fromContactDocument, ContactDocument toContactDocument,
			MessengerContact toMessengerContact) throws Exception{
		String lastMessage = null;
		try{
			if(StringUtils.isNotBlank(fromContactDocument.getL_msg())&&fromContactDocument.getIs_encrypted()==1){
				lastMessage=EncryptionUtil.decrypt(fromContactDocument.getL_msg(),
						StringUtils.join(fromContactDocument.getB_num(),fromContactDocument.getC_phone()),
						StringUtils.join(fromContactDocument.getC_phone(),fromContactDocument.getB_num()),false);
				if(StringUtils.isEmpty(lastMessage)){
					lastMessage=EncryptionUtil.decrypt(fromContactDocument.getL_msg(),
							StringUtils.join(fromContactDocument.getB_num(),fromContactDocument.getM_c_id()),
							StringUtils.join(fromContactDocument.getM_c_id(),fromContactDocument.getB_num()),false);
				}
			}
		}catch(Exception e){
			log.error("Error in Decrypting from contact document last message in conversation merge {}",e);
			lastMessage = fromContactDocument.getL_msg();
		}
		toContactDocument.setIs_encrypted(encryptLastMessageForConversationMerge(toContactDocument,lastMessage,toMessengerContact,fromContactDocument));
		toContactDocument.setL_msg_on(fromContactDocument.getL_msg_on());
//		toContactDocument.setL_msg(fromContactDocument.getL_msg());
		toContactDocument.setLastMessageMetaData(fromContactDocument.getLastMessageMetaData());
		toContactDocument.setLastMessageType(fromContactDocument.getLastMessageType());
		toContactDocument.setLastMsgSource(fromContactDocument.getLastMsgSource());
		toContactDocument.setLastMessageUserId(fromContactDocument.getLastMessageUserId());
		toContactDocument.setLastMessageUserName(fromContactDocument.getLastMessageUserName());
//		toMessengerContact.setLastMessage(fromContactDocument.getL_msg());
		toMessengerContact.setLastMsgOn(new Date(fromContactDocument.get_l_msg_on_epoch()));
		toMessengerContact.setLastMessageMetaData(JSONUtils.toJSON(fromContactDocument.getLastMessageMetaData()));
	}

	private void updateMessageDocument(Integer fromMcId, Integer toMcId, Integer accountId) {
		List<MessageDocument> messageDocuments = getMessageDocuments(fromMcId, accountId);
		if (CollectionUtils.isNotEmpty(messageDocuments)) {
			messageDocuments.stream().forEach(messageDocument -> messageDocument.setC_id(toMcId.toString()));
			BulkUpsertPayload<MessageDocument> bulkUpsertActivities = new BulkUpsertPayload<>(messageDocuments,
					accountId, accountId, Constants.Elastic.MESSAGE_INDEX);
			try {
				elasticSearchService.performBulkRequestWithRefresh(bulkUpsertActivities,DocWriteRequest.OpType.INDEX,WriteRequest.RefreshPolicy.NONE);
			} catch (Exception e) {
				log.error("Exception while updating bulk messages", e);
				throw new MessengerException(ErrorCode.ERROR_WHILE_BULK_UPDATING_MESSAGES);
			}
		}

	}

	private List<MessageDocument> getMessageDocuments(Integer mcId, Integer accountId) {
		log.info("Get message doc from ES mcId:{} , accountId:{}", mcId, accountId);
		Map<String, Object> messageFilter = new HashMap<>();
		messageFilter.put("mcids", ControllerUtil.toCommaSeparatedString(Collections.singleton(mcId)));
		Map<String, Object> data = new HashMap<>();
		data.put("messageFilter", messageFilter);
		ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
				.addRoutingId(accountId).addTemplateAndDataModel(Constants.Elastic.GET_MESSAGES_BY_MCIDS, data).build();
		ElasticData messageData = elasticSearchService.getDataFromElastic(esRequest,
				MessageDocument.class);
		if (CollectionUtils.isNotEmpty(messageData.getResults())) {
			return messageData.getResults();
		}
		return Collections.emptyList();
	}

	private void deleteUnattributedConversation(MessengerContact messengerContact, Integer accountId,Integer businessId) {
		Map<String, List<Integer>> result = null;
		Integer mcId = messengerContact.getId();
		boolean conversationDeleted = conversationService.deleteConversation(messengerContact.getId(), accountId,
				WriteRequest.RefreshPolicy.IMMEDIATE);
		if (conversationDeleted) {
			// 2. Delete From MessengerContact from DB
			boolean deleted = messengerContactService.deleteMessengerContact(mcId);
			// 3. Remove data from MessageIndex
			messageService.deleteMessagesByMcIdWithRefresh(mcId, accountId, false);
			if (deleted) {
				messageService.deleteMessagesDataFromDBOnCustomerDelete(mcId, messengerContact.getCustomerId());
			}
			FirebaseDto firebaseDto = new FirebaseDto();
			firebaseDto.setAccountId(accountId);
			firebaseDto.setBusinessId(businessId);
			firebaseDto.setMcId(mcId);
			fcmService.mirrorOnWeb(firebaseDto);
		}
	}

	@Override
	public void updateContactDetails(UpdateWebchatContactDetailsRequest request) throws Exception{
		request.setCustomerInfoSource(ContactInfoSourceEnum.WIDGET);
		createCustomerInfoCapturedActivity(request);
		MergeCustomerRequest mergeCustomerRequest = new MergeCustomerRequest();
		if(Objects.nonNull(request.getCustomerPhone())){
			mergeCustomerRequest.setPhone2(request.getCustomerPhone());
			mergeCustomerRequest.setCountryCode(request.getCountryCode().getCode());
		}
		if(Objects.nonNull(request.getCustomerEmail())){
			mergeCustomerRequest.setEmail2(request.getCustomerEmail());
		}

		MessengerContact messengerContact = messengerContactService.findById(request.getMc_id());
		mergeCustomerRequest.setCustomerName2(buildCustomerName(request));
		mergeCustomerRequest.setCid1(messengerContact.getCustomerId());
		mergeCustomerRequest.setBusinessId1(request.getBusinessId());
		mergeCustomerRequest.setBusinessId2(messengerContact.getBusinessId());
		mergeCustomerRequest.setAccountId(request.getAccountId());

		List<MergeCustomerResponse> mergeCustomerResponses = contactService.anonymousContactMerge(mergeCustomerRequest);
		if(CollectionUtils.isNotEmpty(mergeCustomerResponses)){
			for(MergeCustomerResponse mergeCustomerResponse: mergeCustomerResponses){
				if(!mergeCustomerResponse.getOldCid().equals(mergeCustomerResponse.getNewCid())){
					MessengerContact fromMessengerContact = messengerContactService.getOrCreateContactForExistingCustomer(mergeCustomerResponse.getBusinessId(),mergeCustomerResponse.getOldCid(),request.getAccountId());
					MessengerContact toMessengerContact = messengerContactService.getOrCreateContactForExistingCustomer(mergeCustomerResponse.getBusinessId(),mergeCustomerResponse.getNewCid(),request.getAccountId());
					mergeConversations(fromMessengerContact,toMessengerContact,request.getAccountId());
				}
				updateContactDocument(mergeCustomerResponse.getNewCid());
			}
		}
	}

	private void createCustomerInfoCapturedActivity(UpdateWebchatContactDetailsRequest request){
		ActivityDto activityDto = null;
		ActivityType activityType = ActivityType.WEBCHAT_CUSTOMER_DETAILS;
		UserDTO userDTO = communicationHelperService.getUserDTO(MessengerConstants.ROBIN_REPLY_USER);
		CustomerContactInfo customerContactInfo = new CustomerContactInfo();
		if(Objects.nonNull(request.getCustomerPhone())){
			customerContactInfo.setPhone(request.getCustomerPhone());
		}
		if(Objects.nonNull(request.getCustomerEmail())){
			customerContactInfo.setEmailId(request.getCustomerEmail());
		}
		if(Objects.nonNull(request.getCountryCode())){
			customerContactInfo.setCountryCode(request.getCountryCode());
		}

		activityDto = ActivityDto.builder().mcId(request.getMc_id()).created(new Date())
				.actorId(MessengerConstants.ROBIN_REPLY_USER).activityType(activityType)
				.from(MessengerConstants.ROBIN_REPLY_USER)
				.accountId(request.getAccountId()).source(Source.LIVE_CHAT_RECEIVE.getSourceId())
				.businessId(request.getBusinessId()).customerContactInfo(customerContactInfo).customerContactInfoSource(request.getCustomerInfoSource()).build();

		// adding activity to conversation_ activity and messenger_message
		conversationActivityService.persistActivityInDatabase(activityDto, userDTO);
		// adding activity related data into message index
		MessageDocument messageDocument = conversationActivityService.persistActivityInES(activityDto);
		log.info("[persistActivityInStore][{}] - Syncing Firebase WEBDB for businessId {} and accountId {}",
				activityType, request.getBusinessId(), request.getAccountId());
		MessangerBaseFilter messengerBaseFilter = new MessangerBaseFilter();
		messengerBaseFilter.setAccountId(request.getAccountId());
		messengerBaseFilter.setConversationId(request.getMc_id());
		messengerBaseFilter.setCount(1);
		List<ContactDocument> contactDocuments = messengerContactService.getContactFromES(messengerBaseFilter);
		if (!org.springframework.util.CollectionUtils.isEmpty(contactDocuments)) {
			fcmService.mirrorOnWeb(DtoToEntityConverter.fromContactDoc(contactDocuments.get(0)));
			fcmService.mirrorOnMobile(contactDocuments.get(0), messageDocument);
		}
	}

		private void pushSwitchConversationEventToFirebase(SwitchConversationFirebaseEvent switchConversationFirebaseEvent){
        fcmService.pushSwitchWebchatConversationEventToFirebase(switchConversationFirebaseEvent);
		}

		private void updateContactDocument(Integer customerId){
			CustomerDTO customerDTO=contactService.findByIdNoCaching(customerId);
			if(customerDTO!=null){
				Optional<Lock> lockOpt=Optional.empty();
				try{
					String lockKey=Constants.CUSTOMER_ID_PREFIX+customerDTO.getId();
					lockOpt=redisLockService.tryLock(lockKey,1,TimeUnit.SECONDS);
					if(!lockOpt.isPresent()){
						//Reject such events
						log.info("[Livechat init] Unable to acquire lock key:{}",lockKey);
						throw new MessengerException(ErrorCode.WEBCHAT_SEND_LOCK);
					}
					MessengerContact messengerContact=messengerContactService.findByCustomerId(customerId);
					BusinessDTO businessDTO=businessService.getBusinessDTO(messengerContact.getBusinessId());
					messengerContactService.updateContactOnES(messengerContact,customerDTO,businessDTO,MessageTag.INBOX,null);
				}finally{
					if(lockOpt.isPresent()){
						redisLockService.unlock(lockOpt.get());
					}
				}
			}
		}

	private String buildCustomerName(UpdateWebchatContactDetailsRequest request){
		String name = "";
		if (StringUtils.isEmpty(name) && StringUtils.isNotBlank(request.getCustomerPhone())) {
			name = request.getCustomerPhone();
		}
		if (StringUtils.isEmpty(name) && StringUtils.isNotBlank(request.getCustomerEmail())
				&& StringUtils.contains(request.getCustomerEmail(), "@")){
			String[] customerEmailId=request.getCustomerEmail().split("@");
			name=StringUtils.replace(customerEmailId[0],"."," ").replaceAll("_"," ");
		}
		return name;
	}

	private Integer encryptLastMessageForConversationMerge(ContactDocument contactDocument,String lastMessage,MessengerContact messengerContact,ContactDocument fromContactDocument) {

		Integer isEncrypted = 0;
		boolean smsEncrypted = fromContactDocument.getIs_encrypted() != null
				&& fromContactDocument.getIs_encrypted() == 1;
		if (MessengerUtil.isEncryptionEnabled() && smsEncrypted
				&& StringUtils.isNotEmpty(lastMessage)) {
			try {
				Long businessNumber = contactDocument.getB_num();
				String encryptedMessage = "";
				if(StringUtils.isBlank(contactDocument.getC_phone())){
					encryptedMessage = EncryptionUtil.encrypt(lastMessage,
							StringUtils.join(businessNumber, contactDocument.getM_c_id()),
							StringUtils.join(contactDocument.getM_c_id(), businessNumber));
				}
				else {
					encryptedMessage = EncryptionUtil.encrypt(lastMessage,
							StringUtils.join(businessNumber, contactDocument.getC_phone()),
							StringUtils.join(contactDocument.getC_phone(), businessNumber));
				}
				contactDocument.setL_msg(encryptedMessage);
				messengerContact.setLastMessage(encryptedMessage);
				isEncrypted = 1;
			} catch (Exception e) {
				log.error("[encryptLastMessage] Failed to encrypt the message, saving as unencrypted ", e);
				// TODO: For encryption failure case, consider having normal message instaed of
				// empty.
				contactDocument.setL_msg(lastMessage);
				messengerContact.setLastMessage(lastMessage);
			}
		}
		return isEncrypted;

	}

	private Boolean checkRobinActiveHours(List<RobinActiveHours> robinActiveHours,Boolean isReceivedDuringBusinessHours,BusinessTimingDTO businessTimingDTO){
		if(CollectionUtils.isNotEmpty(robinActiveHours) && Objects.nonNull(isReceivedDuringBusinessHours) && Objects.nonNull(businessTimingDTO)){
			log.info("checking active robin hours for the business and widget: {} for business hours: {}",robinActiveHours,isReceivedDuringBusinessHours);
			if(Boolean.TRUE.equals(isReceivedDuringBusinessHours) && robinActiveHours.get(0).getRobinInsideBusinessHours() == 1){
				return true;
			}else if(Boolean.FALSE.equals(isReceivedDuringBusinessHours) && robinActiveHours.get(0).getRobinOutsideBusinessHours() == 1){
				return true;
			}else if(BusinessHoursUtility.isReceivedDuringChatBotHr(businessTimingDTO,robinActiveHours,new Date())){
                return true;
			}else{
				return  false;
			}
		}
		return true;
	}

	/**
	 * Triggers customer follow-up using the existing customer follow-up service
	 * @param liveChatSessionToken the session token containing all necessary data
	 * @param agentId the agent ID for follow-up configuration
	 */
	private void triggerCustomerFollowup(LiveChatSessionToken liveChatSessionToken, String agentId) throws Exception {
		log.info("Triggering customer follow-up for sessionId: {}, agentId: {}, businessId: {}, mcId: {}, customerId: {}",
				liveChatSessionToken.getSessionId(), agentId, liveChatSessionToken.getBusinessId(),
				liveChatSessionToken.getMcid(), liveChatSessionToken.getCustomerId());

		// Build the customer follow-up request using session data
		CustomerFollowupRequest followupRequest = new CustomerFollowupRequest();
		followupRequest.setAgentId(agentId);
		followupRequest.setChannel(Source.LIVE_CHAT_RECEIVE); // Web channel for livechat
		followupRequest.setBusinessId(liveChatSessionToken.getBusinessId());
		followupRequest.setMcId(liveChatSessionToken.getMcid());
		followupRequest.setCustomerId(liveChatSessionToken.getCustomerId());
		followupRequest.setAccountId(liveChatSessionToken.getAccountId());
		// Trigger the customer follow-up
		customerFollowupService.triggerCustomerFollowup(followupRequest);
	}

	private boolean checkIfWidgetLocaleIsFrench(Integer widgetId) {
		String widgetLocale = null;
		try{
			String customLanguageEnabledWidgets=CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("custom_language_enabled_widgets","261968-fr");
			List<String> customLanguageEnabledWidgetsList=ControllerUtil.getTokensListFromString(customLanguageEnabledWidgets);
			String supportedLanguages = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("custom_languages_for_widget","fr,sp");
			List<String> supportedLanguagesList=ControllerUtil.getTokensListFromString(supportedLanguages);
			if (CollectionUtils.isNotEmpty(customLanguageEnabledWidgetsList) &&
					CollectionUtils.isNotEmpty(supportedLanguagesList)) {

				for (String language : supportedLanguagesList) {
					String widgetWithLanguage = widgetId + "-" + language;
					if (customLanguageEnabledWidgetsList.contains(widgetWithLanguage)) {
						widgetLocale = language;
						break;
					}
				}
			}
			if (StringUtils.isNotBlank(widgetLocale) && widgetLocale.equalsIgnoreCase("fr")) {
				return true;
			}
		}catch(Exception e){
			log.info("exception in checking for custom language widget: {}",e);
		}
		return false;
	}
}


