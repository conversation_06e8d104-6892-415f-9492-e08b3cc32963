package com.birdeye.messenger.service.impl;


import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.SortedSet;
import java.util.TreeSet;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.MessengerConstants;
import com.birdeye.messenger.dao.entity.LiveChatSessionToken;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dao.entity.PulseSurveyContext;
import com.birdeye.messenger.dto.ActivityDto;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ConversationDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.MessageDocumentDTO;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.MessangerBaseFilter;
import com.birdeye.messenger.dto.MessengerGlobalFilter;
import com.birdeye.messenger.dto.MessengerMediaFileDTO;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.SendResponse;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.Channel;
import com.birdeye.messenger.dto.elastic.MessageDocument.SentThrough;
import com.birdeye.messenger.dto.whatsapp.WARestrictedFlags;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.MessageStatusEnum;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.ComponentCodeEnum;
import com.birdeye.messenger.exception.ErrorMessageBuilder;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.ConversationActivityService;
import com.birdeye.messenger.service.FacebookEventService;
import com.birdeye.messenger.service.LiveChatMessageService;
import com.birdeye.messenger.service.LiveChatService;
import com.birdeye.messenger.service.LiveChatSessionService;
import com.birdeye.messenger.service.PulseSurveyService;
import com.birdeye.messenger.service.SendMessageService;
import com.birdeye.messenger.service.SmsService;
import com.birdeye.messenger.service.WhatsappMessageService;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class LiveChatSendEventHandler extends MessageEventHandlerAbstract{
	@Autowired
    protected SendMessageService sendMessageService;
	private final MessengerEvent EVENT = MessengerEvent.LIVECHAT_SEND;
	@Autowired
	protected SmsService smsService;
	@Autowired
	protected MessengerMessageService messengerMessageService;
	@Autowired
    protected FacebookEventService facebookEventService;
	@Autowired
    protected CommonService commonService;
	@Autowired
	protected LiveChatService livechatService;
	@Autowired
	protected PulseSurveyService pulseSurveyService;
	@Autowired
	protected LiveChatMessageService liveChatMessageService;
	@Autowired
	protected LiveChatSessionService livechatSessionService;

	@Autowired
	private ConversationActivityService conversationActivityService;

	private final WhatsappMessageService whatsappMessageService;
	
    @Override
    BusinessDTO getBusinessDTO(MessageDTO messageDTO) {
        BusinessDTO businessDTO = messageDTO.getBusinessDTO();
        if (Objects.isNull(businessDTO)) {
            SendMessageDTO dto = (SendMessageDTO) messageDTO;
            Integer businessId = Integer.valueOf(dto.getBusinessIdentifierId());
            businessDTO = communicationHelperService.getBusinessDTO(businessId);
            dto.setBusinessDTO(businessDTO);
        }
        return businessDTO;
    }
    
    @Override
    CustomerDTO getCustomerDTO(MessageDTO messageDTO) {
        CustomerDTO customerDTO = messageDTO.getCustomerDTO();
        if (Objects.isNull(customerDTO)) {
        	SendMessageDTO dto = (SendMessageDTO) messageDTO;
            //customerDTO = communicationHelperService.getCustomerDTO(dto.getCustomerId());
			customerDTO = contactService.findByIdNoCaching(dto.getCustomerId());
            dto.setCustomerDTO(customerDTO);
        }
        return customerDTO;
    }
    
    @Override
    UserDTO getUserDTO(MessageDTO messageDTO) {
    	UserDTO userDTO = messageDTO.getUserDTO();
        if (Objects.isNull(userDTO)) {
        	SendMessageDTO dto = (SendMessageDTO) messageDTO;
        	userDTO = communicationHelperService.getUserDTO(dto.getUserId());
            dto.setUserDTO(userDTO);
        }
        return userDTO;
    }
    
    
    @Override
    MessageTag getMessageTag(MessageDTO messageDTO) {
    	if(messageDTO.getSource().equals(Source.WEB_CHAT.getSourceId())) {
    		messageDTO.setMessageTag(MessageTag.UNREAD);
		}
    	else {
    		messageDTO.setMessageTag(MessageTag.INBOX);
		}
    	return messageDTO.getMessageTag();
    }
    
    @Override
    MessageDocumentDTO getMessageDocumentDTO(MessageDTO messageDTO) {
		SendMessageDTO dto = (SendMessageDTO) messageDTO;
		if(Objects.isNull(messageDTO.getMessageDocumentDTO())) {
			MessageDocumentDTO messageDocumentDTO = new MessageDocumentDTO(dto.getConversationDTO(), getMessengerContact(messageDTO).getId());
			setPaymentInfoInMessage(messageDTO, messageDocumentDTO);
			messageDTO.setMessageDocumentDTO(messageDocumentDTO);
		}
		return messageDTO.getMessageDocumentDTO();
    }
    
	@Override
	public MessengerEvent getEvent() {
		return EVENT;
	}

	@Override
	void updateLastMessageMetaData(MessageDTO messageDTO) {
        MessengerContact messengerContact = getMessengerContact(messageDTO);
//        LastMessageMetaData lastMessageMetaData = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
//        lastMessageMetaData.setLastMessageSource(Source.SMS.getSourceId());
        //TODO: remove this comment once all SEND and RECEIVE api migrated to messenger-service
        //messengerContact.setLastMessageMetaData(ControllerUtil.getJsonTextFromObject(lastMessageMetaData));
        messengerContact.setLastResponseAt(((SendMessageDTO) messageDTO).getConversationDTO().getSentOn());
        messengerContact.setLastMsgOn(new Date());
        messengerContact.setUpdatedAt(new Date());
        UserDTO userDTO = getUserDTO(messageDTO);
		LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
		lastMessageMetadataPOJO.setLastMessageType("SEND");
		lastMessageMetadataPOJO.setLastMessageUserId(userDTO.getId());
		lastMessageMetadataPOJO.setLastMessageChannel(Channel.LIVE_CHAT.name());
		lastMessageMetadataPOJO.setLastMessageUserName(MessengerUtil.buildUserName(userDTO));
		messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));
	}

	@Override
	void alterAndUpdateLastMessage(MessageDTO messageDTO) {
        MessengerContact messengerContact = getMessengerContact(messageDTO);
        SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        if (StringUtils.isEmpty(sendMessageDTO.getBody()) && StringUtils.isNotEmpty(sendMessageDTO.getMediaurl())) {
            messengerContact.setLastMessage("you sent an attachment");
        } else {
            messengerContact.setLastMessage(sendMessageDTO.getBody());
        }
        boolean isEncrypted = false;
        if(BooleanUtils.isTrue(sendMessageDTO.getEmailMandatory())) {
        	isEncrypted=EncryptionUtil.encryptLastMessageWithMcId(messengerContact, sendMessageDTO.getEncrypted(), businessDTO.getBusinessNumber().toString());
        }else {
        	isEncrypted=EncryptionUtil.encryptLastMessage(messengerContact, sendMessageDTO.getEncrypted(), getCustomerDTO(messageDTO).getPhone(), businessDTO.getBusinessNumber());
        }
     	messengerContact.setEncrypted(isEncrypted ? 1 : 0);
	}

	@Override
	MessengerContact getMessengerContact(MessageDTO messageDTO) {
        MessengerContact messengerContact = messageDTO.getMessengerContact();
        SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
        if (Objects.isNull(messageDTO.getMessengerContact())) {
            BusinessDTO businessDTO = getBusinessDTO(messageDTO);
			messengerContact = messengerContactService.getOrCreateContactForExistingCustomer(
					businessDTO.getBusinessId(), sendMessageDTO.getCustomerId(), businessDTO.getAccountId());
            messageDTO.setMessengerContact(messengerContact);
        }
        if(messengerContact.getBlocked()) {
        	throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.CONTACT_IS_BLOCKED, ComponentCodeEnum.CUSTOMER, HttpStatus.BAD_REQUEST));
        	
		}
        return messengerContact;
	}

	@Override
	MessengerGlobalFilter getEmailNotificationMetaData(MessageDTO messageDTO) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	Integer getMessageId(MessageDTO messageDTO) {
		Integer messageId = messageDTO.getMessageId();
        if(Objects.isNull(messageId)) {
        	SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
            messageId = sendMessageDTO.getConversationDTO().getId();
            messageDTO.setMessageId(messageId);
        }
        return messageId;
	}
	
	
	@Override
	public SendResponse handle(MessageDTO messageDTO) throws Exception {
		messageDTO.setMsgTypeForResTimeCalc("S");
		try{
			if(messageDTO.getUserId() != MessengerConstants.ROBIN_REPLY_USER){
				persistLiveChatUserActivityOnWidget(messageDTO);
			}
		}catch(Exception e){
			log.info("Issue in creating activity for mcID:{}",Integer.valueOf(((SendMessageDTO) messageDTO).getToCustomerId()));
		}
		livechatService.updateConvStateOnMessageSend((SendMessageDTO) messageDTO);
		
		SortedSet<MessageResponse.Message> messages = sendMessage(messageDTO);
		BusinessDTO businessDTO = getBusinessDTO(messageDTO);
		int routeId = businessDTO.getEnterpriseId() != null
				? businessDTO.getEnterpriseId()
				: businessDTO.getBusinessId();
		// TODO : do we need to check for isFBSendAvailable??
		SendResponse sendResponse = new SendResponse((SendMessageDTO) messageDTO, messages,
				facebookEventService.isFBSendAvailable(getMessengerContact(messageDTO), routeId), false);
		WARestrictedFlags waRestrictedFlags = whatsappMessageService.isWAFreeflowSendAvailable(getMessengerContact(messageDTO), routeId);
		sendResponse.setReplyFromWAReceived(waRestrictedFlags.getReplyFromWAReceived());
		sendResponse.setRestrictWAReply(waRestrictedFlags.getRestrictWAReply());
		sendResponse.setLastMsgSource(messageDTO.getSource());
		sendResponse.setMessages(messages);
		
		CustomerDTO customerDTO = getCustomerDTO(messageDTO);
		// handle PulseSurveyContext
		PulseSurveyContext context = null;
		try {
			context = pulseSurveyService.handlePulseSurveyContext(null, customerDTO, messageDTO.getBusinessDTO());
			if (context != null && PulseSurveyContext.isOngoingPulseSurvey(context.getStatus())){
				customerDTO.setOngoingPulseSurvey(PulseSurveyContext.isOngoingPulseSurvey(context.getStatus()));
			} else {
				customerDTO.setOngoingPulseSurvey(false);
			}
			messageDTO.setCustomerDTO(customerDTO);
		} catch (Exception ex) {
			log.error("Getting exception while executing handlePulseSurveyContext method {}", ex);
		}
		// push message to firebase
		super.handle(messageDTO);
		return sendResponse;
	}
	
	private SortedSet<MessageResponse.Message> sendMessage(MessageDTO messageDTO) throws Exception {
		messageDTO.getUserId();
		SendMessageDTO sendMessageDTO =(SendMessageDTO) messageDTO;
		getBusinessDTO(messageDTO);
		// set customerId of customer table first
		sendMessageDTO.setCustomerId(getMessengerContact(messageDTO).getCustomerId());
		// prepare message text (handle message templates)
		commonService.setMessageBody(sendMessageDTO, getCustomerDTO(messageDTO), "sms");
		sendMessageDTO.setSource(prepareOutgoingMessageSource(messageDTO.getSource()));
		sendMessageDTO.setCustomerId(getMessengerContact(messageDTO).getCustomerId());
		sendMessageDTO.setFromBusinessId(getMessengerContact(messageDTO).getBusinessId());
		MessengerEvent event= MessengerEvent.LIVECHAT_SEND;
		messageDTO.setSentThrough(SentThrough.WEB);
		if (StringUtils.isNotBlank(sendMessageDTO.getMediaurl()) || CollectionUtils.isNotEmpty(sendMessageDTO.getMediaUrls())) {
			event= MessengerEvent.LIVECHAT_MEDIA_SEND;
		}
		
		SortedSet<MessageResponse.Message> messages = new TreeSet<>(MessageResponse.getMessageComparator());
		// TODO : copied from MMS_SEND, Review later
		if (event.equals(MessengerEvent.LIVECHAT_MEDIA_SEND)) {
			String messageBody=sendMessageDTO.getBody();
			for (int media = 0; media < sendMessageDTO.getMediaUrls().size(); media++) {
				MessengerMediaFile mediaFile = sendMessageDTO.getMediaUrls().get(media);
				sendMessageDTO.setMediaurl(mediaFile.getUrl());
				if (media < sendMessageDTO.getMediaUrls().size() - 1) {
					sendMessageDTO.setBody(null);
				} else {
					sendMessageDTO.setBody(messageBody);
				}
				MessengerMediaFileDTO messengerMediaFileDTO=new MessengerMediaFileDTO(mediaFile);
				messengerMediaFileDTO.setFileExtension(FilenameUtils.getExtension(mediaFile.getUrl()));
				messageDTO.setMessengerMediaFileDTO(messengerMediaFileDTO);
				if(media!=0) {
					messageDTO.setPartOfExistingMessage(true);
				}
				messages.add(pushReply(messageDTO,event));
			}
		} else {
			messages.add(pushReply(messageDTO,event));
		}
		return messages;
	}
	
	private Integer prepareOutgoingMessageSource(Integer source) {
		// should never be null (this is last message source)
		// default to LIVE_CHAT_SEND when source is null
		if (source != null && source.intValue() == Source.LIVE_CHAT_BIRDEYE_PROFILE_RECEIVE.getSourceId()) {
			return Source.LIVE_CHAT_BIRDEYE_PROFILE_SEND.getSourceId();
		} else {
			return Source.LIVE_CHAT_SEND.getSourceId();
		}
	}

	/**
	 * Message will be saved in SMS table (used for all messages - sms/fb/livechat)
	 */
	private SendResponse.Message pushReply(MessageDTO messageDTO,MessengerEvent event) throws Exception {
		SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
		UserDTO userDTO=getUserDTO(messageDTO);
		//ConversationDTO conversationDTO = new ConversationDTO(smsService.saveSMS(sendMessageDTO), Channel.LIVE_CHAT);
		ConversationDTO conversationDTO = new ConversationDTO(liveChatMessageService.saveLiveChatMessage(sendMessageDTO,MessageStatusEnum.SENT), Channel.LIVE_CHAT);
		conversationDTO.setSentThrough(messageDTO.getSentThrough());
		conversationDTO.setMessengerContactId(getMessengerContact(messageDTO).getId());
		sendMessageDTO.setConversationDTO(conversationDTO);
		messengerMessageService.saveMessengerMessage(conversationDTO, userDTO);
		// TODO : review with kamal, should we create an audit entry for outgoing live chat messages?
		//		createAuditEntry(event, sendMessageDTO, userDTO, conversationDTO);
		sendMessageService.saveMediaFile(messageDTO.getMessengerMediaFileDTO(), conversationDTO.getId());
		MessageResponse.Message message = new SendResponse.Message(userDTO, conversationDTO, new MessengerMediaFile(messageDTO.getMessengerMediaFileDTO()), messageDTO.getBusinessDTO().getTimeZoneId());
		message.setPaymentInfo(getMessageDocumentDTO(messageDTO).getPaymentInfo());
		return message;
	}

//	private void createAuditEntry(MessengerEvent event, SendMessageDTO sendMessageDTO, UserDTO userDTO,
//			ConversationDTO conversationDTO) {
//		if (userDTO != null) {
//	            MessengerAudit audit = new MessengerAudit(conversationDTO.getId(), sendMessageDTO.getUserDTO().getId(), event.toString());
//	            messengerAuditRepository.save(audit);
//	    }
//	}

	@Override
	void publishEvent(MessageDTO messageDTO) {
		publishEventIfRepliedOnUnassignedConversation(messageDTO);
	}

	void persistLiveChatUserActivityOnWidget(MessageDTO messageDTO){
		Optional<LiveChatSessionToken> session = livechatSessionService.getExistingSession(((SendMessageDTO) messageDTO).getFromBusinessId(),Integer.valueOf(((SendMessageDTO) messageDTO).getToCustomerId()));
		LiveChatSessionToken liveChatSessionToken = session.get();
		UserDTO userDTO = getUserDTO(messageDTO);
        if(session.isPresent() && Objects.nonNull(session) && Objects.nonNull(session.get().getBusinessUser()) && Objects.nonNull(userDTO)
				&& userDTO.getId().equals(session.get().getBusinessUser())){
			return;
		}
		ActivityDto activityDto = null;
		ActivityType activityType = ActivityType.LIVECHAT_USER;
		activityDto = ActivityDto.builder().mcId(liveChatSessionToken.getMcid()).created(new Date())
				.actorId(userDTO.getId()).activityType(activityType)
				.from(userDTO.getId()).fromName(userDTO.getName())
				.accountId(liveChatSessionToken.getAccountId()).source(Source.LIVE_CHAT_SEND.getSourceId())
				.businessId(liveChatSessionToken.getBusinessId()).build();

		// adding activity to conversation_ activity and messenger_message
		conversationActivityService.persistActivityInDatabase(activityDto, userDTO);
		// adding activity related data into message index
		MessageDocument messageDocument = conversationActivityService.persistActivityInES(activityDto);
		log.info("[persistActivityInStore][{}] - Syncing Firebase WEBDB for businessId {} and accountId {}",
				activityType, liveChatSessionToken.getBusinessId(), liveChatSessionToken.getAccountId());
		MessangerBaseFilter messengerBaseFilter = new MessangerBaseFilter();
		messengerBaseFilter.setAccountId(liveChatSessionToken.getAccountId());
		messengerBaseFilter.setConversationId(liveChatSessionToken.getMcid());
		messengerBaseFilter.setCount(1);
		List<ContactDocument> contactDocuments = messengerContactService.getContactFromES(messengerBaseFilter);
		if (!org.springframework.util.CollectionUtils.isEmpty(contactDocuments)) {
			fcmService.mirrorOnMobile(contactDocuments.get(0), messageDocument);
		}
		liveChatSessionToken.setBusinessUser(userDTO.getId());
		livechatSessionService.save(liveChatSessionToken);
	}
}
