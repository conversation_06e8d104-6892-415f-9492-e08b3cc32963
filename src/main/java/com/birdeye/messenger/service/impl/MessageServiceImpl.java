package com.birdeye.messenger.service.impl;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.SortedSet;
import java.util.TreeSet;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.SearchHit;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.MessengerMessage;
import com.birdeye.messenger.dao.entity.whatsapp.WhatsAppOnboardingStatus;
import com.birdeye.messenger.dao.repository.AppleMessageRepository;
import com.birdeye.messenger.dao.repository.WhatsAppOnboardingStatusRepository;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.BusinessLite;
import com.birdeye.messenger.dto.ChatTransferAuth;
import com.birdeye.messenger.dto.ConversationHistoryRequest;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.ElasticData;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.LiveChatFetchMessageRequest;
import com.birdeye.messenger.dto.MessageClientIpResponse;
import com.birdeye.messenger.dto.MessageRequest;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.MessengerFilter;
import com.birdeye.messenger.dto.SocialChannelIntegrationStatus;
import com.birdeye.messenger.dto.SocialChannelIntegrationStatusRequest;
import com.birdeye.messenger.dto.TeamAssigneeDto;
import com.birdeye.messenger.dto.UnansweredFaqAnsweredRequest;
import com.birdeye.messenger.dto.UnansweredFaqDeleteRequest;
import com.birdeye.messenger.dto.ViewedByUser;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.Channel;
import com.birdeye.messenger.dto.elastic.MessageDocument.MessageType;
import com.birdeye.messenger.dto.elastic.MessageDocument.UnansweredFaqType;
import com.birdeye.messenger.dto.report.DownloadReportFilter;
import com.birdeye.messenger.dto.report.GenericDownloadReportDTO;
import com.birdeye.messenger.dto.report.RobinUnansweredQuestionDTO;
import com.birdeye.messenger.dto.whatsapp.WARestrictedFlags;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.MessengerTagEnum;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.es.sro.ESUpdateByQueryRequest;
import com.birdeye.messenger.es.sro.ESUpdateByQueryRequest.Builder;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.BusinessException;
import com.birdeye.messenger.exception.ErrorMessageBuilder;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.exception.NotAuthorizedException;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.external.service.SocialService;
import com.birdeye.messenger.service.AddNoteService;
import com.birdeye.messenger.service.ConversationActivityService;
import com.birdeye.messenger.service.ConversationService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.EmailService;
import com.birdeye.messenger.service.FacebookEventService;
import com.birdeye.messenger.service.FacebookMessageService;
import com.birdeye.messenger.service.GoogleMessageService;
import com.birdeye.messenger.service.InstagramEventService;
import com.birdeye.messenger.service.InstagramMessageService;
import com.birdeye.messenger.service.MessageService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.SmsService;
import com.birdeye.messenger.service.VoiceCallService;
import com.birdeye.messenger.service.WhatsappMessageService;
import com.birdeye.messenger.service.googleBusinessMessaging.impl.GoogleUtils;
import com.birdeye.messenger.service.secure.messaging.SecureMessagingCommonService;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.JwtUtil;
import com.birdeye.messenger.util.MessengerUtil;
import com.birdeye.messenger.util.TimeZoneUtil;
import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class MessageServiceImpl implements MessageService {

    private final BusinessService businessService;
    private final MessengerContactService messengerContactService;
    private final FacebookEventService facebookEventService;
    @Lazy
    private final ConversationService conversationService;
    private final CommunicationHelperService communicationHelperService;
	private final GoogleUtils googleUtils;
	private final ElasticSearchExternalService elasticSearchService;
	private final InstagramEventService instagramEventService;
    private final EmailService emailService;
    private final MessengerMessageService messengerMessageService;
    private final AddNoteService noteService;
    private final FacebookMessageService facebookMessageService;
    private final InstagramMessageService  instagramMessageService;
    private final VoiceCallService voiceCallService;
    private final ConversationActivityService conversationActivityService;
    private final SmsService smsService;
    private final GoogleMessageService googleMessageService;
    private final AppleMessageRepository appleMessageRepository;
    private final SecureMessagingCommonService secureMessagingCommonService;
	private final SocialService socialService;
	private final ObjectMapper objectMapper;
	private final WhatsappMessageService whatsappMessageService;
	private final WhatsAppOnboardingStatusRepository whatsAppOnboardingStatusRepo;



        private static final List<MessageDocument.Channel> channelList = Arrays
			.asList(MessageDocument.Channel.SMS, MessageDocument.Channel.FACEBOOK, MessageDocument.Channel.LIVE_CHAT, MessageDocument.Channel.WEB_CHAT);
	private static final List<ActivityType> activityTypeList = Arrays.asList(ActivityType.LIVECHAT_START, ActivityType.LIVECHAT_END, ActivityType.LIVECHAT_TIMEOUT, ActivityType.LIVECHAT_RESTART, ActivityType.WEBCHAT_START);

    @Override
    @SuppressWarnings("unchecked")
    public MessageResponse getMessageV2(MessageRequest messageRequest, boolean isDelta, Integer userId, Integer accountId, String requestSource) {
    	log.info("getMessageV2: requestSource {}", requestSource);
        BusinessDTO businessDTO = businessService.getBusinessLiteDTO(messageRequest.getBusinessId());
        if (Objects.isNull(businessDTO)) {
            log.error("getMessageV2: businessDTO returned null from core-service for businessId {}", messageRequest.getBusinessId());
            throw new NotFoundException(ErrorCode.BUSINESS_NOT_FOUND);
        }
        //This is temporary fix to suppress exception in inbox,This check will be removed once it's fixed permanently from UI
        //https://birdeye.atlassian.net/browse/BIRDEYE-123755
        if (!businessDTO.getRoutingId().equals(accountId)) {
            log.error("Business accountId {} from request doesn't match with the session token accountId {},Hence returning...", messageRequest.getBusinessId(),accountId);
            return null;
        }
        MessengerContact messengerContact = messengerContactService.findById(messageRequest.getMessengerContactId());
        if (Objects.isNull(messengerContact)) {
            throw new NotFoundException(ErrorCode.MESSENGER_CONTACT_NOT_EXIST);
        }
        
        MessengerFilter esQueryData = getESQueryData(messageRequest, businessDTO, isDelta);
        ElasticData messageData = messengerContactService.getMessageData(esQueryData);
        if (!messageData.isSucceeded()) return null;

		List<MessageDocument> messageDocuments = (List<MessageDocument>) messageData.getResults();
		boolean hasMore = messageRequest.getStartIndex() + messageRequest.getCount() < messageData.getTotal();
		messageDocuments.parallelStream()
				.forEach(document -> document.setIsAppCompatible(messageRequest.getIsAppCompatible()));
		MessageResponse messageResponse = new MessageResponse(messageDocuments, businessDTO.getTimeZoneId(),
				messageRequest.getMessengerContactId(), hasMore,userId,requestSource);
		
		ContactDocument conversation = messengerContactService.getContact(accountId, messageRequest.getMessengerContactId());

		Integer lastMsgSource = messengerContactService.getLastMessageSource(conversation);
		messageResponse.setLastMsgSource(lastMsgSource != null ? lastMsgSource : 1);

		// Add custom channel if last message source is from custom channel
		if (lastMsgSource != null && lastMsgSource.equals(Source.CUSTOM.getSourceId())) {
			String lastMsgCustomChannel = messengerContactService.getLastMsgCustomChannel(conversation);
			messageResponse.setLastMsgCustomChannel(lastMsgCustomChannel);
		}

		setSocialChannelIntegrationStatusFlag(messageResponse,businessDTO);
		setFacebookFlags(1, businessDTO, messengerContact, messageResponse);
		setGoogleFlags(businessDTO,messengerContact,messageResponse);
		setInstagramFlags(businessDTO, messengerContact, messageResponse);
		setAppleFlags(conversation,businessDTO, messageResponse);
		setTextingNumber(businessDTO, messageResponse);
		setWhatsappFlags(businessDTO, messageResponse, messengerContact);
		if (!isDelta && messengerContact.getTag().equals(MessengerTagEnum.UNREAD.getId())) {
			conversationService.updateConversationStatus(messengerContact, MessengerTagEnum.INBOX.getId(), null,
					businessDTO.getRoutingId(),messageRequest);
		}
		
		// Show ViewedByUser list only for new messages/old unread messages
		if (messageRequest.isWeb() && (messengerContact.getIsRead() == null || !messengerContact.getIsRead())) {
			List<ViewedByUser> viewedByUsers = identifyAndUpdateViewedByUsers(userId, businessDTO,
					messengerContact, isDelta, accountId,conversation, messageRequest);
			messageResponse.setViewedByUsers(viewedByUsers);
		}
		
		//Added for Mobile App. Not being user by dashboard.
		messageResponse.setSentiment(conversation.getSentiment());
		messageResponse.setSentimentScore(conversation.getSentimentScore());
		if (Objects.nonNull(conversation.getOngoingPulseSurvey()))
				messageResponse.setOngoingPulseSurvey(conversation.getOngoingPulseSurvey());
		return messageResponse;
	}

	private void setTextingNumber(BusinessDTO businessDTO, MessageResponse messageResponse) {
		String textingNumber = businessService.getBusinessSMSNumber(businessDTO.getBusinessId());
		messageResponse.setTextingNumber(textingNumber);
	}

	private void setGoogleFlags(BusinessDTO businessDTO, MessengerContact messengerContact, MessageResponse messageResponse) {
		//GBM_SEND_NA - After one month from the received message
		messageResponse.setRestrictGoogleReply(googleUtils.isGoogleSendAvailable(messengerContact, businessDTO.getRoutingId()));
//		messageResponse.setGoogleUserReachable(googleEventService.isGoogleUserReachable(messengerContact));
	}

	private void setInstagramFlags(BusinessDTO businessDTO, MessengerContact messengerContact, MessageResponse messageResponse) {
		messageResponse.setRestrictInstagramReply(instagramEventService.isInstagramSendAvailable(messengerContact, businessDTO.getRoutingId()));
	}

	private List<ViewedByUser> identifyAndUpdateViewedByUsers(Integer userId, BusinessDTO businessDTO,
			MessengerContact messengerContact, boolean isDelta, Integer accountId, ContactDocument conversation,
			MessageRequest messageRequest) {
		List<Integer> viewedByUsersFromES = CollectionUtils.isNotEmpty(conversation.getViewedBy())
				? conversation.getViewedBy() : new ArrayList<>();
		Set<Integer> viewedByUsersForResponse = new HashSet<>(viewedByUsersFromES);
		viewedByUsersForResponse.add(userId);
		Map<Integer, TeamAssigneeDto> userDetails = communicationHelperService.getValidUserDTOs(userId,
				businessDTO.getRoutingId(), businessDTO.getBusinessId());

		// Remove last message user id from response viewed by list
		LastMessageMetaData lastMessageMetaData = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
		if (null != lastMessageMetaData && null != lastMessageMetaData.getLastMessageUserId() && !"RECEIVE".equalsIgnoreCase(lastMessageMetaData.getLastMessageType())) {
			viewedByUsersForResponse.remove(lastMessageMetaData.getLastMessageUserId());
		}
		List<ViewedByUser> response = new ArrayList<>();
		viewedByUsersForResponse.remove(userId);
		viewedByUsersForResponse.forEach(id -> {
			if (Objects.nonNull(userDetails.get(id))) {
				addNonBirdeyeDomainUsers(userDetails.get(id), response);
			}
		});
		if (!isDelta && !viewedByUsersFromES.contains(userId)) {
			viewedByUsersFromES.add(userId);
			conversationService.updateConversationStatus(messengerContact, null, viewedByUsersFromES,
					businessDTO.getRoutingId(), messageRequest);
		}
		return response;
	}

	/**
	 * @param userDTO
	 * @param response
	 */
	private void addNonBirdeyeDomainUsers(TeamAssigneeDto userDTO, List<ViewedByUser> response) {
		String domain = StringUtils.isNotBlank(userDTO.getEmailId())
				? userDTO.getEmailId().substring(userDTO.getEmailId().indexOf("@") + 1)
				: null;
		if (StringUtils.isNotBlank(domain) && !domain.contains("birdeye")) {
			response.add(new ViewedByUser(userDTO.getValue(), userDTO.getLabel()));
		}
	}


	private Integer addCustomActivityMessages(MessageResponse messageResponse, boolean hasMore) {
		SortedSet<MessageResponse.Message> messages = messageResponse.getMessages();
		SortedSet<MessageResponse.Message> customMessages = new TreeSet<>();
		Integer customMessagesCount = 0;
		MessageDocument.Channel channel = null;
		for(MessageResponse.Message msg : messages) {
			if(MessageDocument.MessageType.ACTIVITY.equals(msg.getMessageType()) && activityTypeList.contains(msg.getActivityType()))
				break;
			if(channel == null && channelList.contains(msg.getChannel()))
				channel =  msg.getChannel();
			if(MessageDocument.Channel.FACEBOOK.equals(channel) || MessageDocument.Channel.SMS.equals(channel)) {
				if(MessageDocument.Channel.LIVE_CHAT.equals(msg.getChannel()) || MessageDocument.Channel.WEB_CHAT.equals(msg.getChannel())) {
					channel = null;
					customMessages.add(createCustomMessage(msg, ActivityType.LIVECHAT_START));
					customMessagesCount++;
				}
			}
			if(MessageDocument.Channel.FACEBOOK.equals(msg.getChannel()) || MessageDocument.Channel.SMS.equals(msg.getChannel()))
				channel = msg.getChannel();
		}
		messages.addAll(customMessages);
		if(CollectionUtils.isNotEmpty(messages)) {
			if(hasMore) {
				messages.remove(messages.first());
			} else {
				MessageResponse.Message message = messages.first();
				if((MessageDocument.Channel.LIVE_CHAT.equals(message.getChannel()) || Channel.WEB_CHAT.equals(message.getChannel())) && !activityTypeList.contains(message.getActivityType())) {
					messages.add(createCustomMessage(message, ActivityType.LIVECHAT_START));
					customMessagesCount++;
				}
			}
		}
		return customMessagesCount;
	}

	private MessageResponse.Message createCustomMessage(MessageResponse.Message msg, ActivityType activityType) {
		MessageResponse.Message message = new MessageResponse.Message();
		message.setActivityType(activityType);
		message.setMessageType(MessageType.ACTIVITY);
		message.setSource(msg.getSource());
		message.setId("0" + msg.getId());
		message.setSentOn(msg.getSentOn());
		message.setSentAt(msg.getSentAt());
		message.setSentAtUTC(msg.getSentAtUTC());
		message.setCustom(true);
		return message;
	}

	private void setFacebookFlags(Integer messengerEnabled, BusinessDTO businessDTO, MessengerContact messengerContact, MessageResponse messageResponse) {
        // FB_SEND_NA - More than 24 hours elapsed from user's last incoming FB message 
        messageResponse.setRestrictFBReply(facebookEventService.isFBSendAvailable(messengerContact, businessDTO.getRoutingId()));
        
        // FB_SEND_NA - FB Integration page changed
        messageResponse.setFBUserReachable(facebookEventService.isFacebookUserReachable(messengerContact, businessDTO.getRoutingId()));
    }

	private void setAppleFlags(ContactDocument conversation, BusinessDTO businessDTO, MessageResponse messageResponse) {
		messageResponse.setAppleOptOut(Objects.nonNull(conversation.getAppleContextDocument())
				&& BooleanUtils.isTrue(conversation.getAppleContextDocument().getAppleOptOut()));
	}
    private MessengerFilter getESQueryData(MessageRequest messageRequest, BusinessDTO businessDTO, boolean isDelta) {
        MessengerFilter esQueryData = new MessengerFilter();
        esQueryData.setConversationId(messageRequest.getMessengerContactId());
        esQueryData.setStartIndex(messageRequest.getStartIndex());
		esQueryData.setQueryFile(Constants.Elastic.GET_MESSAGES_V2);
		if(isDelta) esQueryData.setQueryFile(Constants.Elastic.GET_MESSAGES_FOR_DELTA);
		if(!isDelta && messageRequest.isWeb()) {
			esQueryData.setCount(messageRequest.getCount() + 1);
		}else {
			esQueryData.setCount(messageRequest.getCount());
		}
		esQueryData.setAccountId(businessDTO.getRoutingId());
        if(Objects.nonNull(messageRequest.getLastMessageTime())) {
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            esQueryData.setStartDate(df.format(new Date(messageRequest.getLastMessageTime())));
			if(isDelta) esQueryData.setStartDate(String.valueOf(messageRequest.getLastMessageTime()));
        }
        // applying exclude filter for LIVECHAT ACTIVITIES for Mobile users
        Map<String, Object> params = new HashMap<>();
		//excluding user livechat activity
        params.put("activityType",ControllerUtil.getJsonTextFromObject(Arrays.asList(ActivityType.LIVECHAT_USER.name())));
		if(!messageRequest.isWeb()) {
			params.put("activityType",
					ControllerUtil.getJsonTextFromObject(
							Arrays.asList(ActivityType.LIVECHAT_START.name(),
									ActivityType.LIVECHAT_END.name(),
									ActivityType.LIVECHAT_TIMEOUT.name(),
									ActivityType.LIVECHAT_RESTART.name(),
									ActivityType.WEBCHAT_START.name(),
									ActivityType.EMAIL_START.name(),
									ActivityType.REFERRAL_LEAD_GEN.name(),
									ActivityType.REFERRER_LEAD_GEN.name(),
									ActivityType.THANK_YOU_NOTE_SENT_REFERRER.name(),
									ActivityType.CONVERSATION_SWITCHED_FROM.name(),
									ActivityType.CONVERSATION_SWITCHED_TO.name(),
									ActivityType.LIVECHAT_USER.name(),
									ActivityType.LIVECHAT_TALK_TO_AGENT_CLICK.name())));
		}
		esQueryData.setParams(params);
		if(!messageRequest.isShowReviews() || !messageRequest.getAccess().isReviews()) {
        	params.put("reviews", false);
        }
        else if(CollectionUtils.isNotEmpty(messageRequest.getExcludeReviewIds())) {
    		params.put("excludeReviewIds", ControllerUtil.toCommaSeparatedString(messageRequest.getExcludeReviewIds()));
    	}
        if(messageRequest.getAccess()!=null && !messageRequest.getAccess().isSurveys()) {
        	params.put("surveys", false);
        }
        if(MapUtils.isNotEmpty(params)) {
        	esQueryData.setParams(params);
        }
        return esQueryData;
    }

	private MessengerFilter getESQueryDataForLiveChats(LiveChatFetchMessageRequest liveChatFetchMessageRequest, BusinessDTO businessDTO) {
        MessengerFilter esQueryData = new MessengerFilter();

        // To fetch only the live-chat source messages
        Map<String, Object> params = new HashMap<>();
		params.put("source",
				Arrays.asList(Source.LIVE_CHAT_BIRDEYE_PROFILE_RECEIVE.getSourceId(),
						Source.LIVE_CHAT_BIRDEYE_PROFILE_SEND.getSourceId(), Source.LIVE_CHAT_RECEIVE.getSourceId(),
						Source.LIVE_CHAT_SEND.getSourceId(),
						Source.SMS.getSourceId(),
						Source.MOBILE.getSourceId(),
						Source.WEB_CHAT.getSourceId(),
						Source.INSTAGRAM.getSourceId(),
						Source.FACEBOOK.getSourceId(), Source.GOOGLE.getSourceId(),Source.APPLE.getSourceId(),Source.TWITTER.getSourceId()).toString());

        esQueryData.setConversationId(liveChatFetchMessageRequest.getMessengerContactId());
        esQueryData.setStartIndex(liveChatFetchMessageRequest.getStartIndex());
        esQueryData.setCount(liveChatFetchMessageRequest.getCount());
        esQueryData.setAccountId(businessDTO.getRoutingId());
        esQueryData.setQueryFile(Constants.Elastic.GET_MESSAGES_V2);
        esQueryData.setSecureFaq(true); //filter out secureFaq responses
        esQueryData.setParams(params);
        
        String customTimeHistoryConfig = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("livechat_msgs_custom_time", "now-4h");
        
        if (StringUtils.isNotEmpty(customTimeHistoryConfig)) {
        	esQueryData.setStartDate(customTimeHistoryConfig);
        }
        return esQueryData;
    }

	@Override
	@SuppressWarnings("unchecked")
	public MessageResponse getLiveChatMessages(LiveChatFetchMessageRequest liveChatFetchMessageRequest) {
		BusinessDTO businessDTO = businessService.getBusinessByBusinessNumber(liveChatFetchMessageRequest.getBusinessId());
        if (Objects.isNull(businessDTO)) {
            log.error("getMessageV2: businessDTO returned null from core-service for businessId {}", liveChatFetchMessageRequest.getBusinessId());
            throw new NotFoundException(ErrorCode.BUSINESS_NOT_FOUND);
        }
        Integer accountId = businessDTO.getAccountId();
        Integer messengerEnabled = businessService.isMessengerEnabled(accountId);
        if (Integer.valueOf(0).equals(messengerEnabled)) {
            log.info("getMessageV2: Messenger Not Enabled for business {}",
                    accountId);
            return null;
        }
        MessengerContact messengerContact = messengerContactService.findById(liveChatFetchMessageRequest.getMessengerContactId());
        if (Objects.isNull(messengerContact)) {
            throw new NotFoundException(ErrorCode.MESSENGER_CONTACT_NOT_EXIST);
        }
        MessengerFilter esQueryData = getESQueryDataForLiveChats(liveChatFetchMessageRequest, businessDTO);
        ElasticData messageData = messengerContactService.getMessageData(esQueryData);
        if (!messageData.isSucceeded()) return new MessageResponse();

        List<MessageDocument> messageDocuments = (List<MessageDocument>) messageData.getResults();

        boolean hasMore = liveChatFetchMessageRequest.getStartIndex() + liveChatFetchMessageRequest.getCount() < messageData.getTotal();
        MessageResponse messageResponse = new MessageResponse(messageDocuments, businessDTO.getTimeZoneId(), liveChatFetchMessageRequest.getMessengerContactId(), hasMore,null,null);
        return messageResponse;
	}

    /**
     *
     * @param mcId short for MessengerContact id
     * @param routingId
     * @return true if successful
     */
    @Override
    public boolean deleteMessagesByMcId(Integer mcId, Integer routingId) {
       return deleteMessagesByMcIdWithRefresh(mcId, routingId, true);
    }

    @Override
    public boolean deleteMessagesByConversationId(Integer mcId, Integer routingId) {
        Map<String, Object> dataModel = new HashMap<>();
        dataModel.put("c_id", String.valueOf(mcId));
        dataModel.put("size", "10000");
        ESRequest esRequest = new ESRequest.Builder(new ESRequest())
            .addRoutingId(routingId)
            .addTemplateAndDataModel(Constants.Elastic.DELETE_MESSAGE_BY_MCID, dataModel)
            .addIndex(Constants.Elastic.MESSAGE_INDEX)
            .build();
        
        return elasticSearchService.deleteByQuery(esRequest);
    }
    @Override
    public boolean deleteReviewByIdAndMcId(Integer reviewId,Integer mcId, Integer routingId){
        Map<String, Object> dataModel = new HashMap<>();
        dataModel.put("reviewId", String.valueOf(reviewId));
        dataModel.put("mcId", String.valueOf(mcId));
        dataModel.put("size", "1");
        ESRequest esRequest = new ESRequest.Builder(new ESRequest())
            .addRoutingId(routingId)
            .addTemplateAndDataModel(Constants.Elastic.DELETE_REVIEW_BY_ID, dataModel)
            .addIndex(Constants.Elastic.MESSAGE_INDEX)
            .build();
        
        return elasticSearchService.deleteByQueryWithRefresh(esRequest,false);
    }
    @Override
    public boolean  isSingleReviewConversation(Integer conversationId,Integer accountId,Integer reviewId) {
    	Map<String, Object> messageFilter = new HashMap<>();
		messageFilter.put("startIndex", 0);
		messageFilter.put("count", 0);
		messageFilter.put("conversationId", conversationId);
		messageFilter.put("reviewId", reviewId);
		Map<String, Object> dataModel = new HashMap<>();
		dataModel.put("messageFilter", messageFilter);
		ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
				.addRoutingId(accountId)
				.addTemplateAndDataModel(Constants.Elastic.GET_MESSAGES_EXCLUDING_REVIEW, dataModel).build();
		Long documentCount=elasticSearchService.getDocumentCount(esRequest);
		if(documentCount!=null & documentCount==0) {
			return true;
		}
		return false;
	}

    @Override
    public void updateNewConversationIdInMessageDoc(Integer reviewId, Integer accountId, Integer newConversationId, Integer oldConversationId) {
    	log.info("updating new conversation Id {} in message document {} ",newConversationId,reviewId);
    	Map<String, Object> messageFilter = new HashMap<>();
    	messageFilter.put("conversationId", oldConversationId);
    	messageFilter.put("reviewId", reviewId);
    	Map<String, Object> data = new HashMap<>();
    	data.put("messageFilter", messageFilter);
    	Builder builder = new ESUpdateByQueryRequest.Builder(new ESUpdateByQueryRequest());
    	builder.index(Constants.Elastic.MESSAGE_INDEX).queryTemplateFile(Constants.Elastic.GET_MESSAGES_BY_MCID_AND_REVIEWID).freeMarkerDataModel(data);
    	Map<String, Object> scriptData = new HashMap<>();
    	scriptData.put("inline", "ctx._source.c_id="+"'"+newConversationId+"';ctx._source.lastUpdateDate='"+(new Date()).getTime()+"'");
    	builder.scriptParam(scriptData);
    	builder.routingId(accountId);
    	boolean response = elasticSearchService.updateByQueryWithRefresh(builder.build(),false);
    	if(!response) {
            throw new MessengerException(ErrorCode.INTERNAL_SERVER_ERROR);
        }
    }

	@Override
	public void deleteActivitiesByConversationId(Integer conversationId, Integer accountId) {
        Map<String, Object> dataModel = new HashMap<>();
        dataModel.put("c_id", String.valueOf(conversationId));
        dataModel.put("size", "10000");
        ESRequest esRequest = new ESRequest.Builder(new ESRequest())
            .addRoutingId(accountId)
            .addTemplateAndDataModel(Constants.Elastic.DELETE_ACTIVITIES_BY_MCID, dataModel)
            .addIndex(Constants.Elastic.MESSAGE_INDEX)
            .build();
        
        elasticSearchService.deleteByQueryWithRefresh(esRequest,false);

	}
	
	@Override
	public void deleteActivitiesByConversationIds(List<Integer> conversationIds, Integer accountId) {
        Map<String, Object> dataModel = new HashMap<>();
        dataModel.put("cids", ControllerUtil.toCommaSeparatedString(conversationIds));
        dataModel.put("size", "10000");
        ESRequest esRequest = new ESRequest.Builder(new ESRequest())
            .addRoutingId(accountId)
            .addTemplateAndDataModel(Constants.Elastic.DELETE_ACTIVITIES_BY_MCIDS, dataModel)
            .addIndex(Constants.Elastic.MESSAGE_INDEX)
            .build();
        elasticSearchService.deleteByQuery(esRequest);


	}

	@Override
	public boolean deleteSurveyResponseById(Integer surveyResponseId, Integer routingId) {
		Map<String, Object> dataModel = new HashMap<>();
		dataModel.put("surveyResponseId", String.valueOf(surveyResponseId));
		dataModel.put("size", "10000");
		ESRequest esRequest = new ESRequest.Builder(new ESRequest())
				.addRoutingId(routingId)
				.addTemplateAndDataModel(Constants.Elastic.DELETE_SURVEY_RESPONSE_BY_ID, dataModel)
				.addIndex(Constants.Elastic.MESSAGE_INDEX)
				.build();
		return elasticSearchService.deleteByQueryWithRefresh(esRequest,false);
	}

	@Override
	public GenericDownloadReportDTO getRobinUnansweredQuestionsV1(Integer accountId, Integer pageSize, Integer startIndex) {
		GenericDownloadReportDTO genericDownloadReportDTO=new GenericDownloadReportDTO();
		List<RobinUnansweredQuestionDTO> result=new ArrayList<RobinUnansweredQuestionDTO>();
		Integer count=2500;
		boolean hasMore=true;
		while (hasMore && result.size() < pageSize) {
			ElasticData messageData = getRobinUnansweredQuestionsFromES(accountId,startIndex,count, null, Constants.Elastic.GET_ROBIN_UNASWERED_QUESTIONS);
			List<BusinessLite> businessLiteObjects = businessService.getBusinessLiteObjects(accountId);
			Map<Integer, String> businessIdMap = CollectionUtils.emptyIfNull(businessLiteObjects).stream()
						.collect(Collectors.toMap(BusinessLite::getBusinessId, BusinessLite::getTimeZoneId));
			List<MessageDocument> messageDocuments = messageData.getResults();
			if (CollectionUtils.isNotEmpty(messageDocuments)) {
				List<RobinUnansweredQuestionDTO> data = messageDocuments.stream()
						.filter(msg -> (StringUtils.endsWith(msg.getMsg_body(), "?")
								&& MessengerUtil.isWordCountWithinRange(msg.getMsg_body())))
						.limit(pageSize - result.size())
						.map(md -> new RobinUnansweredQuestionDTO(md.getMsg_body(),
								getUnansweredQuestionTime(md, businessIdMap.get(md.getB_id())),md.getM_id()))
						.collect(Collectors.toList());
				result.addAll(data);
			} else {
				hasMore = false;
			}
			startIndex += 2500;
		}
		genericDownloadReportDTO.setData(result);
		return genericDownloadReportDTO;
	}
	@Override
	public GenericDownloadReportDTO getRobinUnansweredQuestions(Integer accountId, Integer pageSize, Integer startIndex, String queryFile) {
		GenericDownloadReportDTO genericDownloadReportDTO=new GenericDownloadReportDTO();
		List<RobinUnansweredQuestionDTO> result=new ArrayList<RobinUnansweredQuestionDTO>();
		Map<Integer, String> businessIdMap=new HashMap<Integer, String>();
		List<Integer> businessIds=new ArrayList<Integer>();
		BusinessDTO business = businessService.getBusinessLiteDTO(accountId);
		if (Objects.isNull(business)) {
			log.error("getRobinUnansweredQuestions: businessDTO returned null from core-service for accountId {}", accountId);
			return genericDownloadReportDTO;
		}
		if(isEnterpriseAccount(business)) {
			List<BusinessLite> businessLiteObjects = businessService.getBusinessLiteObjects(accountId);
			if (CollectionUtils.isEmpty(businessLiteObjects)) {
				log.info("getRobinUnansweredQuestions: User doesn't have access to any business location for account {}",
						accountId);
				return genericDownloadReportDTO;
			}
			for (BusinessLite businessLite : businessLiteObjects) {
				businessIdMap.put(businessLite.getBusinessId(), businessLite.getTimeZoneId());
				businessIds.add(businessLite.getBusinessId());
			}
		}else {
			businessIdMap.put(business.getBusinessId(),business.getTimeZoneId());
		}
		ElasticData messageData = getRobinUnansweredQuestionsFromES(accountId,startIndex,pageSize,businessIds,queryFile);
		List<MessageDocument> messageDocuments = messageData.getResults();
		if (CollectionUtils.isNotEmpty(messageDocuments)) {
			messageDocuments = messageDocuments.stream().map(messageDocument -> {
				messageDocument.setMsg_body(MessengerUtil.decryptMessage(messageDocument));
				return messageDocument;
			}).collect(Collectors.toList());
			List<RobinUnansweredQuestionDTO> data = messageDocuments.stream()
					.filter(msg -> (StringUtils.endsWith(msg.getMsg_body(), "?")
							&& MessengerUtil.isWordCountWithinRange(msg.getMsg_body())))
					.limit(Constants.Elastic.ROBIN_UNANSWERED_QUESTION_COUNT_LIMIT)
					.map(md -> new RobinUnansweredQuestionDTO(md.getMsg_body(),
							getUnansweredQuestionTime(md, businessIdMap.get(md.getB_id())), md.getM_id()))
					.collect(Collectors.toList());
			result.addAll(data);
		}
		genericDownloadReportDTO.setData(result);
		genericDownloadReportDTO.setCount(messageData.getTotal().intValue());
		return genericDownloadReportDTO;
	}

	private boolean isEnterpriseAccount(BusinessDTO business) {
		return "Enterprise-Location".equals(business.getType()) || "Enterprise-Product".equals(business.getType());
	}

	private String getUnansweredQuestionTime(MessageDocument messageDocument,String timeZoneId) {
		String unansweredQuestionTime = null;
		Date createdDate = null;
		final DateFormat serverTimeFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		if(timeZoneId!=null) {
		if (StringUtils.isNotBlank(messageDocument.getCr_date())) {
			try {
				createdDate = serverTimeFormatter.parse(messageDocument.getCr_date()); // server time zone is UTC from
			} catch (ParseException e) {
				log.error("Exception in parsing Date for messageDoc {} ", messageDocument);
			}
		}
		if (createdDate != null) {
			unansweredQuestionTime = TimeZoneUtil.formatDateForTz(createdDate, Constants.FORMAT_YYYY_MM_DD_HH_MM_SS,
					timeZoneId);
		}
		}
		return unansweredQuestionTime;
	}

	private ElasticData getRobinUnansweredQuestionsFromES(Integer accountId, Integer startIndex,Integer count, List<Integer> businessIds,
			String queryFile) {
		MessengerFilter esQueryData = new MessengerFilter();
		Map<String, Object> params = new HashMap<>();
	    params.put("businessIds", ControllerUtil.toCommaSeparatedString(businessIds));
		esQueryData.setStartIndex(startIndex);
		esQueryData.setParams(params);
		esQueryData.setCount(count);
		esQueryData.setAccountId(accountId);
		esQueryData.setQueryFile(queryFile);
		ElasticData messageData = messengerContactService.getMessageData(esQueryData);
		return messageData;
	}

	@Override
	public String getMcIdByReviewId(Integer reviewId, Integer accountId) {
		String mcId = null;
		Map<String, Object> dataModel = new HashMap<>();
		dataModel.put("reviewId", reviewId);
		dataModel.put("accountId", accountId);
		ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addRoutingId(accountId)
				.addTemplateAndDataModel(Constants.Elastic.GET_MCID_BY_REVIEWID, dataModel)
				.addIndex(Constants.Elastic.MESSAGE_INDEX).build();
		ElasticData messagesData = elasticSearchService.getDataFromElastic(esRequest, MessageDocument.class);
		if (messagesData != null) {
			List<MessageDocument> messages = messagesData.getResults();
			if (CollectionUtils.isNotEmpty(messages)) {
				mcId = messages.get(0).getC_id();
			}
		}
		return mcId;
	}

	@Override
	public String getAgentLastResponseTime(Integer mcId, Integer accountId) {
		String agentLastResponseTime=null;
		Map<String, Object> dataModel = new HashMap<>();
		dataModel.put("mcId", mcId);
		dataModel.put("accountId", accountId);
		ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addRoutingId(accountId)
				.addTemplateAndDataModel(Constants.Elastic.GET_AGENT_LAST_RESPONSE_TIME, dataModel)
				.addIndex(Constants.Elastic.MESSAGE_INDEX).build();
		ElasticData messagesData = elasticSearchService.getDataFromElastic(esRequest, MessageDocument.class);
		if (messagesData != null) {
			List<MessageDocument> messages = messagesData.getResults();
			if (CollectionUtils.isNotEmpty(messages)) {
				agentLastResponseTime = messages.get(0).getCr_date();
			}
		}
		return agentLastResponseTime;
	}
	
	public MessageResponse getConversationHistoryMessages(@Valid ConversationHistoryRequest request, Integer userId,
														  Integer accountId) throws Exception {
		log.info("getConversationHistoryMessages: userId {} accountId {}", userId, accountId);

		BusinessDTO businessDTO = businessService.getBusinessDTO(request.getBusinessId());
		if (Objects.isNull(businessDTO)) {
			log.error("getConversationHistoryMessages: businessDTO returned null from core-service for businessId {}", request.getBusinessId());
			throw new NotFoundException(ErrorCode.BUSINESS_NOT_FOUND);
		}
		String token = request.getAuthToken();
		ChatTransferAuth authInfo;
		try {
			authInfo = (ChatTransferAuth) JwtUtil.decodeJWT(token);
		} catch (Exception e) {
			throw new NotAuthorizedException("Authorization Failed");
		}
		MessageRequest messageRequest = request;
		messageRequest.setLastMessageTime(authInfo.getTimeStamp());
		
		validateAuthToken(authInfo, messageRequest);

		MessengerContact messengerContact = messengerContactService.findById(messageRequest.getMessengerContactId());
		if (Objects.isNull(messengerContact)) {
			throw new NotFoundException(ErrorCode.MESSENGER_CONTACT_NOT_EXIST);
		}

		MessengerFilter esQueryData = getESQueryData(messageRequest, businessDTO, true);
		esQueryData.setQueryFile(Constants.Elastic.GET_MESSAGES_FOR_CONVERSATION_HISTORY);
		ElasticData messageData = messengerContactService.getMessageData(esQueryData);
		if (!messageData.isSucceeded()) return null;

		List<MessageDocument> messageDocuments = (List<MessageDocument>) messageData.getResults();
		boolean hasMore = messageRequest.getStartIndex() + messageRequest.getCount() < messageData.getTotal();
		MessageResponse messageResponse = new MessageResponse(messageDocuments, businessDTO.getTimeZoneId(),
				messageRequest.getMessengerContactId(), hasMore,null,null);

		ContactDocument conversation = messengerContactService.getContact(accountId, messageRequest.getMessengerContactId());

		// Show ViewedByUser list only for new messages/old unread messages
		if (messageRequest.isWeb() && (messengerContact.getIsRead() == null || !messengerContact.getIsRead())) {
			List<ViewedByUser> viewedByUsers = identifyAndUpdateViewedByUsers(userId, businessDTO,
					messengerContact, false, accountId,conversation, messageRequest);
			messageResponse.setViewedByUsers(viewedByUsers);
		}

		return messageResponse;
	}
	
	private void validateAuthToken(ChatTransferAuth authInfo, MessageRequest messageRequest) {
		if (!authInfo.getFromMcId().equals(messageRequest.getMessengerContactId())) {
			throw new NotAuthorizedException("Authorization Failed - Invalid Contact");
		}
		if (!authInfo.getFromBusinessId().equals(messageRequest.getBusinessId())) {
			throw new NotAuthorizedException("Authorization Failed - Invalid Business");
		}
	}
  
  @Override
	public MessageClientIpResponse getClientIps(MessageRequest request) {
        BusinessDTO businessDTO = businessService.getBusinessLiteDTO(request.getBusinessId());
        if (Objects.isNull(businessDTO)) {
            log.error("getClientIps: businessDTO returned null from core-service for businessId {}", request.getBusinessId());
            throw new NotFoundException(ErrorCode.BUSINESS_NOT_FOUND);
        }
        
        MessengerContact messengerContact = messengerContactService.findById(request.getMessengerContactId());
        if (Objects.isNull(messengerContact)) {
            throw new NotFoundException(ErrorCode.MESSENGER_CONTACT_NOT_EXIST);
        }
        
        MessengerFilter esQueryData = new MessengerFilter();
        esQueryData.setConversationId(request.getMessengerContactId());
        esQueryData.setQueryFile(Constants.Elastic.GET_CONTACT_CLIENT_IP);
        esQueryData.setAccountId(businessDTO.getRoutingId());
		
        ElasticData messageData = messengerContactService.getMessageData(esQueryData);
        if (!messageData.isSucceeded()) return null;

		List<MessageDocument> messageDocuments = (List<MessageDocument>) messageData.getResults();
		MessageClientIpResponse messageResponse = new MessageClientIpResponse(messageDocuments, request.getMessengerContactId());
		return messageResponse;
  }

  @Override
  @Async
  public void deleteMessagesDataFromDBOnCustomerDelete(Integer mcId, Integer cId) {
      try {
          log.info("deleteMessagesDataFromDBOnCustomerDelete called with mcId : {} and cId : {}", mcId, cId);
          List<MessengerMessage> messengerMessages = messengerMessageService.deleteByMCId(mcId);
          Map<String, List<Integer>> result = null;
          if (CollectionUtils.isNotEmpty(messengerMessages)) {
              result = messengerMessages.stream()
                      .collect(Collectors.groupingBy(MessengerMessage::getMessageType,
                              Collectors.mapping(MessengerMessage::getMessageId, Collectors.toList())));
          }
          noteService.deleteConversationNotes(result, cId);
          conversationActivityService.deleteConversationActivities(result, cId);
          emailService.deleteEmailByCustomerId(cId);
          smsService.deleteSmsUsingCustomerId(cId);
          facebookMessageService.deleteFacebookMessagesByMcId(mcId);
          instagramMessageService.deleteInstagramMessagesByMcId(mcId);
          voiceCallService.deleteVoiceCallsUsingCustomerId(cId);
          googleMessageService.deleteByCId(cId);
          secureMessagingCommonService.deleteAllEntitiesForSecureMessaging(mcId);
          appleMessageRepository.deleteByCId(cId);
      } catch (Exception e) {
          log.error("error : {} occurred in deleteMessagesDataFromDBOnCustomerDelete", e.getMessage());
      }

  }

  @Override
  public boolean deleteMessagesByMcIdWithRefresh(Integer mcId, Integer routingId, boolean refresh) {
      Map<String, Object> dataModel = new HashMap<>();
      dataModel.put("c_id", String.valueOf(mcId));
      dataModel.put("size", "10000");
      ESRequest esRequest = new ESRequest.Builder(new ESRequest())
              .addRoutingId(routingId)
              .addTemplateAndDataModel(Constants.Elastic.DELETE_MESSAGE_BY_MCID, dataModel)
              .addIndex(Constants.Elastic.MESSAGE_INDEX)
              .build();

      return elasticSearchService.deleteByQueryWithRefresh(esRequest, refresh);
  }
  
  private void setSocialChannelIntegrationStatusFlag(MessageResponse messageResponse,BusinessDTO businessDTO){
	  SocialChannelIntegrationStatusRequest request = new SocialChannelIntegrationStatusRequest(businessDTO.getBusinessId(),true,true,true,true,true);
	  SocialChannelIntegrationStatus response = socialService.getAllSocialChannelIntegrationStatus(request);
	  messageResponse.setFbIntegrationStatus(response.getFbIntegrationStatus());
	  messageResponse.setGoogleIntegrationStatus(response.getGoogleIntegrationStatus());
	  messageResponse.setInstagramIntegrationStatus(response.getInstagramIntegrationStatus());
	  messageResponse.setAppleIntegrationStatus(response.getAppleIntegrationStatus());
	  messageResponse.setTwitterIntegrationStatus(response.getTwitterIntegrationStatus());
  }

  @Override
  public GenericDownloadReportDTO getRobinUnansweredFaq(DownloadReportFilter downloadFilter, Integer pageSize, Integer startIndex) {
	  if(downloadFilter.getAccountId()==null) {
		  throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.MISSING_ACCOUNT_ID, HttpStatus.BAD_REQUEST));
	  }
	  Integer accountId = downloadFilter.getAccountId();
	  GenericDownloadReportDTO genericDownloadReportDTO=new GenericDownloadReportDTO();
	  List<RobinUnansweredQuestionDTO> result=new ArrayList<RobinUnansweredQuestionDTO>();
	  Map<Integer, String> businessIdMap=new HashMap<Integer, String>();
	  List<Integer> businessIds=new ArrayList<Integer>();
	  BusinessDTO business = businessService.getBusinessLiteDTO(accountId);
	  if (Objects.isNull(business)) {
		  log.error("getRobinUnansweredQuestions: businessDTO returned null from core-service for accountId {}", accountId);
		  return genericDownloadReportDTO;
	  }
	  if(isEnterpriseAccount(business)) {
		  List<BusinessLite> businessLiteObjects = businessService.getBusinessLiteObjects(accountId);
		  if (CollectionUtils.isEmpty(businessLiteObjects)) {
			  log.info("getRobinUnansweredQuestions: User doesn't have access to any business location for account {}",
					  accountId);
			  return genericDownloadReportDTO;
		  }
		  for (BusinessLite businessLite : businessLiteObjects) {
			  businessIdMap.put(businessLite.getBusinessId(), businessLite.getTimeZoneId());
			  businessIds.add(businessLite.getBusinessId());
		  }
	  }else {
		  businessIdMap.put(business.getBusinessId(),business.getTimeZoneId());
	  }
	  ElasticData messageData = getRobinUnansweredQuestionsFromES(accountId,startIndex,pageSize,businessIds,Constants.Elastic.GET_ROBIN_UNANSWERED_FAQ);
	  List<MessageDocument> messageDocuments = messageData.getResults();
	  if (CollectionUtils.isNotEmpty(messageDocuments)) {
		  messageDocuments = messageDocuments.stream().map(messageDocument -> {
			  messageDocument.setMsg_body(MessengerUtil.decryptMessage(messageDocument));
			  return messageDocument;
		  }).collect(Collectors.toList());
		  List<RobinUnansweredQuestionDTO> data = messageDocuments.stream()
				  .map(md -> new RobinUnansweredQuestionDTO(md.getMsg_body(),
						  getUnansweredQuestionTime(md, businessIdMap.get(md.getB_id())), md.getM_id()))
				  .collect(Collectors.toList());
		  result.addAll(data);
	  }
	  genericDownloadReportDTO.setData(result);
	  genericDownloadReportDTO.setCount(messageData.getTotal().intValue());
	  return genericDownloadReportDTO;
  }
  
  @Override
  public void updateRobinUnansweredFaqDelete(@NotNull @Valid UnansweredFaqDeleteRequest updateRequest) {
	  if(updateRequest.getAccountId()==null) {
		  throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.MISSING_ACCOUNT_ID, HttpStatus.BAD_REQUEST));
	  }
	  //TODO
	  if (updateRequest.isDeleteAll()) {
		  log.info("Delete all request received : {}", updateRequest);
		  Map<String, Object> data = new HashMap<>();
		  data.put("size", 1);
		  data.put("accountId", updateRequest.getAccountId());
		  if (CollectionUtils.isNotEmpty(updateRequest.getDeselectedQuestionId())) {
			  data.put("deselectedQuestionId", ControllerUtil.toCommaSeparatedString(updateRequest.getDeselectedQuestionId()));
		  }

		  ESUpdateByQueryRequest.Builder builder = new ESUpdateByQueryRequest.Builder(new ESUpdateByQueryRequest());
		  builder.index(Constants.Elastic.MESSAGE_INDEX).queryTemplateFile(Constants.Elastic.BULK_DEL_ROBIN_UNANSWERED_FAQ)
		  .freeMarkerDataModel(data).routingId(updateRequest.getAccountId());

		  Map<String, Object> scriptData = new HashMap<>();
		  String script="ctx._source.unansweredFaqType=params.unansweredFaqType;ctx._source.lastUpdateDate=params.lastUpdateDate";

		  scriptData.put("inline",script);
		  builder.scriptParam(scriptData);

		  Map<String, Object> params = new HashMap<>();
		  params.put("unansweredFaqType", UnansweredFaqType.DELETED);
		  params.put("lastUpdateDate", (new Date()).getTime());
		  builder.params(params);

		  boolean updateByQueryResponse = elasticSearchService.updateByQueryWithRefresh(builder.build(), true);
		  if (!updateByQueryResponse){
			  log.info("unansweredFaqType updation failed {}, accountId:{}", UnansweredFaqType.DELETED.name(), updateRequest.getAccountId());
		  } else {
			  log.info("unansweredFaqType update successful {}, accountId:{}", UnansweredFaqType.DELETED.name(), updateRequest.getAccountId());
		  }
		  return;
	  }
	  updateFaqMessageDocumentOnES(updateRequest.getAccountId(), updateRequest.getSelectedQuestionId(), MessageDocument.UnansweredFaqType.DELETED);
  }

  @Override
  public void updateRobinUnansweredFaq(UnansweredFaqAnsweredRequest updateRequest) {
	  if(updateRequest.getAccountId()==null) {
		  throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.MISSING_ACCOUNT_ID, HttpStatus.BAD_REQUEST));
	  }
	  updateFaqMessageDocumentOnES(updateRequest.getAccountId(), updateRequest.getMessageId(), MessageDocument.UnansweredFaqType.ANSWERED);
  }

  private void updateFaqMessageDocumentOnES(Integer accountId, List<String> messageIds, UnansweredFaqType unansweredFaqType) {
	  Map<String, Object> data = new HashMap<>();
	  data.put("size", messageIds.size());
	  data.put("messageIds", ControllerUtil.toCommaSeparatedString(messageIds));

	  ESUpdateByQueryRequest.Builder builder = new ESUpdateByQueryRequest.Builder(new ESUpdateByQueryRequest());
	  builder.index(Constants.Elastic.MESSAGE_INDEX).queryTemplateFile(Constants.Elastic.GET_MESSAGES_BY_ID_FAQ_UPDATE)
	  .freeMarkerDataModel(data).routingId(accountId);

	  Map<String, Object> scriptData = new HashMap<>();
	  String script="ctx._source.unansweredFaqType=params.unansweredFaqType;ctx._source.lastUpdateDate=params.lastUpdateDate";

	  scriptData.put("inline",script);
	  builder.scriptParam(scriptData);

	  Map<String, Object> params = new HashMap<>();
	  params.put("unansweredFaqType", unansweredFaqType);
	  params.put("lastUpdateDate", (new Date()).getTime());
	  builder.params(params);

	  boolean updateByQueryResponse = elasticSearchService.updateByQueryWithRefresh(builder.build(), true);
	  if (!updateByQueryResponse){
		  log.info("unansweredFaqType updation failed {}:{}", unansweredFaqType.name(), messageIds);
	  } else {
		  log.info("unansweredFaqType update successful {}:{}", unansweredFaqType.name(), messageIds);
	  }
  }

  @Override
  public void migrateUnansweredQualifiedQuestions() {
	  ESRequest request = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
			  .addScroll("10m")
			  .addTemplateAndDataModel(Constants.Elastic.GET_DEFAULT_FALLBACK_MSGS_FOR_MIGRATION, null)
			  .build();

	  SearchResponse searchResponse = elasticSearchService.getSearchResult(request);
	  if (searchResponse.status().getStatus() != 200) {
		  log.info("migrateUnansweredQualifiedQuestions : Failed to retrieve data from Elasticsearch with status code: {}",searchResponse.status().getStatus());
		  throw new BusinessException(ErrorCode.INTERNAL_SERVER_ERROR);
	  }
	  String scrollId = searchResponse.getScrollId();
	  while (true) {
		  if (Objects.nonNull(searchResponse) && (searchResponse.getHits().getHits().length != 0)) {
			  if(Objects.nonNull(searchResponse)) {
				  SearchHit[] searchHit = searchResponse.getHits().getHits();
				  Set<String> msgIds = new HashSet<>();
				  if (searchHit.length > 0) {
					  Arrays.stream(searchHit).forEach(hit -> {
						  MessageDocument msgDoc = objectMapper.convertValue(hit.getSourceAsMap(), MessageDocument.class);
						  String msgBody = MessengerUtil.decryptMessage(msgDoc);
						  if (StringUtils.endsWith(msgBody, "?")
								  && MessengerUtil.isWordCountWithinRange(msgBody)) {
							  msgIds.add(hit.getId());
						  }
					  });
				  }
				  log.info("msgIds for updateQualifiedStatus: {}", msgIds);
				  updateQualifiedStatus(msgIds, UnansweredFaqType.UNANSWERED_QUALIFIED);
			  }
		  }
		  searchResponse = elasticSearchService.readMoreFromSearch(scrollId,"10m");
		  if (searchResponse.status().getStatus() != 200) {
			  log.info("migrateUnansweredQualifiedQuestions Failed to retrieve data from Elasticsearch with status code: {}",searchResponse.status().getStatus());
			  throw new BusinessException(ErrorCode.INTERNAL_SERVER_ERROR);
		  }
		  if (searchResponse.getHits().getHits().length == 0) {
			  elasticSearchService.clearScrollContext(scrollId);
			  break;
		  }
	  }
  }

  private void updateQualifiedStatus(Set<String> docIds, UnansweredFaqType unansweredFaqType) {
	  Map<String, Object> data = new HashMap<>();
	  data.put("size", docIds.size());
	  data.put("docIds", ControllerUtil.toCommaSeparatedString(docIds));

	  ESUpdateByQueryRequest.Builder builder = new ESUpdateByQueryRequest.Builder(new ESUpdateByQueryRequest());
	  builder.index(Constants.Elastic.MESSAGE_INDEX).queryTemplateFile(Constants.Elastic.GET_MSGS_BY_DOC_ID)
	  .freeMarkerDataModel(data);

	  Map<String, Object> scriptData = new HashMap<>();
	  String script="ctx._source.unansweredFaqType=params.unansweredFaqType;ctx._source.lastUpdateDate=params.lastUpdateDate";

	  scriptData.put("inline",script);
	  builder.scriptParam(scriptData);

	  Map<String, Object> params = new HashMap<>();
	  params.put("unansweredFaqType", unansweredFaqType);
	  params.put("lastUpdateDate", (new Date()).getTime());
	  builder.params(params);

	  boolean updateByQueryResponse = elasticSearchService.updateByQueryWithRefresh(builder.build(), false);
	  if (!updateByQueryResponse){
		  log.info("unansweredFaqType updation failed {}:{}", unansweredFaqType.name(), docIds);
	  } else {
		  log.info("unansweredFaqType update successful {}:{}", unansweredFaqType.name(), docIds);
	  }
  }
  
  @Async
  @Override
  public void updateReplyViaEmailStatus(String docId, boolean replyViaEmail) {
	  Map<String, Object> data = new HashMap<>();
	  data.put("size", 1);
	  data.put("_id", docId);

	  ESUpdateByQueryRequest.Builder builder = new ESUpdateByQueryRequest.Builder(new ESUpdateByQueryRequest());
	  builder.index(Constants.Elastic.MESSAGE_INDEX).queryTemplateFile(Constants.Elastic.MESSAGE_BY_ID_V2)
	  .freeMarkerDataModel(data);

	  Map<String, Object> scriptData = new HashMap<>();
	  String script="ctx._source.replyViaEmail=params.replyViaEmail;ctx._source.lastUpdateDate=params.lastUpdateDate";

	  scriptData.put("inline",script);
	  builder.scriptParam(scriptData);

	  Map<String, Object> params = new HashMap<>();
	  params.put("replyViaEmail", replyViaEmail);
	  params.put("lastUpdateDate", (new Date()).getTime());
	  builder.params(params);

	  boolean updateByQueryResponse = elasticSearchService.updateByQueryWithRefresh(builder.build(), true);
	  if (!updateByQueryResponse){
		  log.info("replyViaEmail updation failed {}:{}", replyViaEmail, docId);
	  } else {
		  log.info("replyViaEmail update successful {}:{}", replyViaEmail, docId);
	  }
  }
  
  private void setWhatsappFlags(BusinessDTO businessDTO, MessageResponse messageResponse, MessengerContact messengerContact) {
	  WARestrictedFlags waRestrictedFlags = whatsappMessageService.isWAFreeflowSendAvailable(messengerContact, businessDTO.getAccountId());
	  messageResponse.setReplyFromWAReceived(waRestrictedFlags.getReplyFromWAReceived());
	  messageResponse.setRestrictWAReply(waRestrictedFlags.getRestrictWAReply());
	// WA Integration account changed
	  messageResponse.setWAUserReachable(whatsappMessageService.isWAUserReachable(messengerContact, businessDTO.getRoutingId()));
	  
	  Optional<WhatsAppOnboardingStatus> waOnboardingStatus = whatsAppOnboardingStatusRepo.findByBusinessIdAndStatus(businessDTO.getBusinessId(), true);
	  if (waOnboardingStatus.isPresent()) {
		  messageResponse.setVerifiedBusiness(waOnboardingStatus.get().getBusinessVerified() != null && waOnboardingStatus.get().getBusinessVerified() == 1 ? true : false);
		  messageResponse.setMetaBusinessId(waOnboardingStatus.get().getMetaBusinessId());
		  String wabaStatus = waOnboardingStatus.get().getWabaStatus();
		  messageResponse.setWabaLimitReached("RESTRICTED".equals(wabaStatus));
	  }
  }
}
