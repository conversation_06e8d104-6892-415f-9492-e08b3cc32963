package com.birdeye.messenger.service.impl;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.constant.MessengerConstants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.robin.RobinAutoReplyConfig;
import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.RobinSessionDocument;
import com.birdeye.messenger.dto.facebook.FacebookMessageRequest;
import com.birdeye.messenger.dto.googleBusinessMessaging.GoogleUserMessage;
import com.birdeye.messenger.dto.instagram.InstagramMessageRequest;
import com.birdeye.messenger.dto.twitter.TwitterMessageRequest;
import com.birdeye.messenger.external.dto.ChatbotQueryResponse;
import com.birdeye.messenger.external.dto.ChatbotReplyDTO;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ChatbotService;
import com.birdeye.messenger.service.googleBusinessMessaging.AutoResponderService;
import com.birdeye.messenger.service.leadgenagent.LeadGenAgentTriggerService;
import com.birdeye.messenger.sro.BusinessTimingDTO;
import com.birdeye.messenger.util.BusinessHoursUtility;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.TextPreferencesUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.enums.RobinSessionEnum;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.MessengerEventHandlerService;
import com.birdeye.messenger.service.RobinAutoReplyConfigRepositoryService;
import com.birdeye.messenger.service.RobinService;
import com.birdeye.messenger.service.SmsService;


@Service
@Slf4j
@RequiredArgsConstructor
public class RobinServiceImpl implements RobinService {

    @Autowired
    public  BusinessService businessService;

    @Autowired
    private  ChatbotService chatbotService;

    @Autowired
    private  RobinAutoReplyConfigRepositoryService robinAutoReplyConfigRepositoryService;

    @Lazy
    @Autowired
    private MessengerEventHandlerService messengerEventHandlerService;

    @Autowired
    private MessengerContactService messengerContactService;

    @Autowired
    private SmsService smsService;

    @Autowired
    AutoResponderService autoResponderService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private LeadGenAgentTriggerService leadGenAgentTriggerService;

    @Override
    @Async
    public void sendRobinReply(MessageDTO messageDTO,String oldLmsgOn) throws Exception {
        // Add keyword auto-reply check before other validation
        if (handleUnsubKeywordAutoReply(messageDTO)) {
            // If keyword auto-reply was sent, we don't need to proceed with other auto-replies
            return;
        }
    	
        if(!("R").equals(messageDTO.getMsgTypeForResTimeCalc()) || !(Source.SMS.getSourceId().equals(messageDTO.getSource())
                || Source.INSTAGRAM.getSourceId().equals(messageDTO.getSource())
                || Source.FACEBOOK.getSourceId().equals(messageDTO.getSource())
                || Source.TWITTER.getSourceId().equals(messageDTO.getSource())
                || Source.CONTACT_US.getSourceId().equals(messageDTO.getSource())
                || Source.GOOGLE.getSourceId().equals(messageDTO.getSource())
                || Source.WHATSAPP.getSourceId().equals(messageDTO.getSource()))){
            return;
        }
        if(Objects.nonNull(messageDTO.getMessengerContact().getBlocked()) && BooleanUtils.isTrue(messageDTO.getMessengerContact().getBlocked())){
            log.info("contact is blocked robin/auto reply cannot be send for customerId {} and businessId {}",messageDTO.getMessengerContact().getId(),messageDTO.getBusinessDTO().getBusinessId());
            return ;
        }
        String receivedMessage = getMessage(messageDTO);
        Source source = Source.getValue(messageDTO.getSource());
        ContactDocument contactDocument = messengerContactService.getContactDocument(messageDTO.getBusinessDTO().getAccountId(),messageDTO.getMessengerContact().getId()).get();
        //Get effective RobinAutoReplyConfig
        if((Objects.isNull(messageDTO.getMessengerContact().getCurrentAssignee()) || messageDTO.getMessengerContact().getCurrentAssignee() == -10) && leadGenAgentTriggerService.isLeadGenAgentTriggerEnabledAllChannels(messageDTO, oldLmsgOn, receivedMessage)){
            log.info("Lead Gen Agent Trigger is enabled for businessId: {}, skipping Robin auto-reply", contactDocument.getB_id());
            return;
        }
        RobinAutoReplyConfig robinAutoReplyConfig = getRobinAutoReplyConfigForBusiness(contactDocument.getB_id(),contactDocument.getE_id(),source);
        if(Objects.nonNull(robinAutoReplyConfig) && Source.CONTACT_US.getSourceId().equals(messageDTO.getSource())){
            robinAutoReplyConfig.setEnableRobin(false);
        }
        if(robinAutoReplyConfig == null ){
            log.info("Robin auto-reply config not set for businessId {}", contactDocument.getB_id());
            if(Source.GOOGLE.equals(source)){
                triggerAutoResponseForGoogle(messageDTO,new Date(contactDocument.get_l_msg_on_epoch()));
            }
            return;
        }
        createOrUpdateRobinSessionDocument(contactDocument,source.name());
        BusinessTimingDTO businessTimingDTO = businessService.getBusinessTimings(contactDocument.getB_id(), true);
        Boolean isReceivedDuringBusinessHour = null;
        if (businessTimingDTO != null) {
            isReceivedDuringBusinessHour = BusinessHoursUtility.isReceivedDuringBusinessHr(businessTimingDTO,
                    new Date());
        }
        if(Boolean.TRUE.equals(robinAutoReplyConfig.isEnableRobin())) {
            if (contactDocument.getRobinSessionDocument() != null && RobinSessionEnum.AUTO.equals(contactDocument.getRobinSessionDocument().getRobinSession())) {
                Optional<ChatbotQueryResponse> chatbotQueryResponseOptional = Optional.empty();
                log.info("received message chatsessionstatus is AUTO for businessId:{}", contactDocument.getB_id());
                chatbotQueryResponseOptional = chatbotService
                        .getQueryResponse(contactDocument.getB_id(), contactDocument.getE_id(), receivedMessage, getQueryChannel(source),
                                contactDocument.getC_id(), contactDocument.getM_c_id(), isReceivedDuringBusinessHour,new LivechatAnonymousConversationDataDto(), null);
                sendIntentBasedChatbotReply(messageDTO, chatbotQueryResponseOptional,
                        isReceivedDuringBusinessHour, source,robinAutoReplyConfig, businessTimingDTO);
            } else if (!Objects.isNull(contactDocument.getRobinSessionDocument()) && RobinSessionEnum.MANUAL.equals(contactDocument.getRobinSessionDocument().getRobinSession())) {
                //handover to robin if no message is exchanged in last 24 hours
                if (ControllerUtil.getTimeDifferenceInHours(oldLmsgOn,
                        Constants.FORMAT_YYYY_MM_DD_HH_MM_SS) >= 24) {
                    changeSessionStatus(messageDTO, isReceivedDuringBusinessHour, receivedMessage, source,robinAutoReplyConfig, businessTimingDTO);
                }
            } else if(Objects.isNull(contactDocument.getRobinSessionDocument())){
                Optional<ChatbotQueryResponse> chatbotQueryResponseOptional = Optional.empty();
                log.info("received message chatsessionstatus is not found for businessId:{}, making it auto", contactDocument.getB_id());
                chatbotQueryResponseOptional = chatbotService
                        .getQueryResponse(contactDocument.getB_id(), contactDocument.getE_id(), receivedMessage, getQueryChannel(source),
                                contactDocument.getC_id(), contactDocument.getM_c_id(), isReceivedDuringBusinessHour,new LivechatAnonymousConversationDataDto(), null);
                sendIntentBasedChatbotReply(messageDTO, chatbotQueryResponseOptional,
                        isReceivedDuringBusinessHour, source,robinAutoReplyConfig, businessTimingDTO);
            } else {
                sendAutoReply(messageDTO,source,robinAutoReplyConfig,isReceivedDuringBusinessHour, businessTimingDTO);
            }
        } else if(isReceivedDuringBusinessHour != null && Boolean.TRUE.equals(isReceivedDuringBusinessHour) && Boolean.TRUE.equals(robinAutoReplyConfig.isEnableAutoReplyInsideBusinessHours())
                && !source.equals(Source.GOOGLE)){
            log.info("send auto reply inside business hours to customerId {} and businessId {}",contactDocument.getC_id(),contactDocument.getB_id());
            String message = robinAutoReplyConfig.getAutoReplyInsideBusinessHours();
            sendMessage(messageDTO, message, source, MessengerConstants.AUTO_REPLY_USER, false, businessTimingDTO);
        } else if(isReceivedDuringBusinessHour != null && Boolean.FALSE.equals(isReceivedDuringBusinessHour) && Boolean.TRUE.equals(robinAutoReplyConfig.isEnableAutoReplyOutsideBusinessHours())
                && !source.equals(Source.GOOGLE)){
            log.info("send auto reply outside business hours to customerId {} and businessId {}",contactDocument.getC_id(),contactDocument.getB_id());
            String message = robinAutoReplyConfig.getAutoReplyOutsideBusinessHours();
            sendMessage(messageDTO, message, source, MessengerConstants.AUTO_REPLY_USER, false, businessTimingDTO);
        } else if(source.equals(Source.GOOGLE)){
            triggerAutoResponseForGoogle(messageDTO,new Date(contactDocument.get_l_msg_on_epoch()));
        } else {
            log.info("No criteria met for Robin auto-reply for businessId {}", contactDocument.getB_id());
        }
    }

    private  void sendAutoReply(MessageDTO messageDTO, Source source,RobinAutoReplyConfig robinAutoReplyConfig,
                                Boolean isReceivedDuringBusinessHour,BusinessTimingDTO businessTimingDTO) throws Exception {
        ContactDocument contactDocument = messageDTO.getContactDocument();
        if(isReceivedDuringBusinessHour != null && Boolean.TRUE.equals(isReceivedDuringBusinessHour) && Boolean.TRUE.equals(robinAutoReplyConfig.isEnableAutoReplyInsideBusinessHours())){
            log.info("send auto reply inside business hours to customerId {} and businessId {}",contactDocument.getC_id(),contactDocument.getB_id());
            sendMessage(messageDTO,robinAutoReplyConfig.getAutoReplyInsideBusinessHours(),source,MessengerConstants.AUTO_REPLY_USER,false, businessTimingDTO);
        }else if(isReceivedDuringBusinessHour != null && Boolean.FALSE.equals(isReceivedDuringBusinessHour) && Boolean.TRUE.equals(robinAutoReplyConfig.isEnableAutoReplyOutsideBusinessHours())){
            log.info("send auto reply outside business hours to customerId {} and businessId {}",contactDocument.getC_id(),contactDocument.getB_id());
            sendMessage(messageDTO,robinAutoReplyConfig.getAutoReplyOutsideBusinessHours(),source,MessengerConstants.AUTO_REPLY_USER,false, businessTimingDTO);
        }else{
            log.info("auto reply config not enabled for customerId {} and businessId {}",contactDocument.getC_id(),contactDocument.getB_id());
        }
    }

    @Override
    public RobinAutoReplyConfig getRobinAutoReplyConfigForBusiness(Integer businessId,Integer enterpriseId,Source source) {
        return robinAutoReplyConfigRepositoryService.getRobinAutoReplyConfigForBusiness(businessId,enterpriseId,source);
    }

    private void sendIntentBasedChatbotReply(MessageDTO messageDTO, Optional<ChatbotQueryResponse> chatbotQueryResponseOpt,
                                             Boolean isReceivedDuringBusinessHour,Source source,RobinAutoReplyConfig robinAutoReplyConfig, BusinessTimingDTO businessTimingDTO) throws Exception {
        ContactDocument contactDocument = messageDTO.getContactDocument();
        MessageDocument messageDocument = messageDTO.getMessageDocument();
        MessageDocument.RobinResponseType robinResponseType=null;
        String intentType = chatbotQueryResponseOpt.isPresent() ? chatbotQueryResponseOpt.get().getIntentType() : null;
        if ((!chatbotQueryResponseOpt.isPresent()||(StringUtils.isBlank(intentType) || "DEFAULT_FALLBACK".equals(intentType)))&&(!source.equals(Source.GOOGLE))) {
            log.info("no response found for the given query/message from chat bot for mcId : {}, customerId : {}, businessId : {}",contactDocument.getM_c_id(),contactDocument.getC_id(),contactDocument.getB_id());
            sendAutoReply(messageDTO,source,robinAutoReplyConfig,isReceivedDuringBusinessHour,businessTimingDTO);
        }else if((!chatbotQueryResponseOpt.isPresent()||(StringUtils.isBlank(intentType) || "DEFAULT_FALLBACK".equals(intentType)))&&source.equals(Source.GOOGLE)){
            triggerAutoResponseForGoogle(messageDTO,new Date(contactDocument.get_l_msg_on_epoch()));
        } else if (chatbotQueryResponseOpt.get().getSuggestionHolder() == null) {
            log.info(" receive message chatsessionstatus is AUTO for businessId:{}",
                    contactDocument.getB_id());
            String plainTextRobinResponse = null;
                if (MapUtils.isNotEmpty(chatbotQueryResponseOpt.get().getData())) {
                    plainTextRobinResponse = (String) chatbotQueryResponseOpt.get().getData().get("response");
                    ChatbotReplyDTO chatbotReplyDTO = new ChatbotReplyDTO();
                    chatbotReplyDTO.setIntentType(chatbotQueryResponseOpt.get().getIntentType());
                    chatbotReplyDTO.setType("intent");
                    chatbotReplyDTO.setData(chatbotQueryResponseOpt.get().getData());
                    String stringifiedChatbotReply = JSONUtils.toJSON(chatbotReplyDTO);
                    robinResponseType = MessageDocument.RobinResponseType.INTENT;
                    if ("FAQ".equals(chatbotQueryResponseOpt.get().getIntentType())) {
                        robinResponseType = MessageDocument.RobinResponseType.FAQ;
                    }
                    if(StringUtils.isBlank(plainTextRobinResponse)){
                        plainTextRobinResponse = stringifiedChatbotReply;
                    }
                    sendMessage(messageDTO,plainTextRobinResponse,source,MessengerConstants.ROBIN_REPLY_USER,true, businessTimingDTO);
                }
        }
        if(robinResponseType!=null) {
            commonService.updateRobinResponseTypeInMessage(messageDocument,robinResponseType);
        }
    }
    private void sendMessage(MessageDTO messageDTO,String message,Source source,Integer user,boolean updateLastResponseAt, BusinessTimingDTO businessTimingDTO) throws Exception {
        SendMessageDTO sendMessageDTO = new SendMessageDTO();
        MessengerContact messengerContact = messageDTO.getMessengerContact();
        BusinessDTO businessDTO = messageDTO.getBusinessDTO();
        CustomerDTO customerDTO = messageDTO.getCustomerDTO();
        String businessName = StringUtils.isNotBlank(businessDTO.getBusinessAlias())
                ? businessDTO.getBusinessAlias()
                : businessDTO.getBusinessName();
        sendMessageDTO.setFromBusinessId(businessDTO.getBusinessId());
        sendMessageDTO.setToCustomerId(String.valueOf(messengerContact.getId()));
        sendMessageDTO.setBusinessIdentifierId(String.valueOf(businessDTO.getBusinessId()));
        sendMessageDTO.setSource(source.getSourceId());
        String phoneNumber = smsService.getFormattedBusinessNumber(businessDTO.getBusinessId(),
                businessDTO.getPhoneNumber());
        String textingNumber = smsService.getFormattedBusinessNumber(businessDTO.getBusinessId());
        
        String msgBody = replaceTextWithBusinessNamePrePopulated(message, businessName);
        PublicDataBusinessDTO publicBusinessDto = null;
        if (msgBody.contains(Constants.BUSINESS_SERVICES) || msgBody.contains(Constants.BUSINESS_PAYMENT_OPTIONS)
        		|| msgBody.contains(Constants.BUSINESS_LANGUAGES) || msgBody.contains(Constants.BUSINESS_SERVICES_CAP)
				|| msgBody.contains(Constants.BUSINESS_PAYMENT_OPTIONS_CAP) || msgBody.contains(Constants.BUSINESS_LANGUAGES_CAP)){
        	publicBusinessDto = businessService.getPublicBusinessData(businessDTO.getBusinessId());
        }
        BusinessLocationCustomFieldsTokensDto customFields = businessService.getBusinessCustomFieldsAndTokenByBusinessID(businessDTO.getBusinessId());
        BusinessProfileData businessProfileData = businessService.getBusinessProfileData(businessDTO.getBusinessId());
        String formattedBody = ControllerUtil.replaceTokens(businessDTO, customerDTO.getFirstName(),
        		msgBody, phoneNumber, textingNumber, businessTimingDTO, publicBusinessDto,businessProfileData,customFields);

        sendMessageDTO.setBody(formattedBody);

        UserDTO userDTO = new UserDTO();
        if(MessengerConstants.ROBIN_REPLY_USER.equals(user)) {
            sendMessageDTO.setUserId(MessengerConstants.ROBIN_REPLY_USER);
            userDTO.setId(MessengerConstants.ROBIN_REPLY_USER);
            sendMessageDTO.setPlainTextRobinResponse(message);
        }
        if(MessengerConstants.AUTO_REPLY_USER.equals(user)) {
            sendMessageDTO.setUserId(MessengerConstants.AUTO_REPLY_USER);
            userDTO.setId(MessengerConstants.AUTO_REPLY_USER);
        }
        sendMessageDTO.setBOT(true);
        sendMessageDTO.setUpdateLastMessage(false);
//      sendMessageDTO.setSentThrough(MessageDocument.SentThrough.WEB);
        sendMessageDTO.setUpdateLastResponseAt(updateLastResponseAt);
        messengerEventHandlerService.handleEvent(sendMessageDTO);
    }
    private void updateContactDocument(ContactDocument contactDocument,
                                       RobinSessionEnum robinSessionEnum) {
        ContactDocument doc = messengerContactService.getContact(contactDocument.getE_id(),
                contactDocument.getM_c_id());
        if (robinSessionEnum != null) {
            doc.getRobinSessionDocument().setRobinSession(robinSessionEnum);
        }
        messengerContactService.updateContactDocumentOnES(doc, doc.getId().toString(), doc.getE_id());
    }

    private void changeSessionStatus(MessageDTO messageDTO, Boolean isReceivedDuringBusinessHour,
                                     String receivedMessage,Source source,RobinAutoReplyConfig robinAutoReplyConfig, BusinessTimingDTO businessTimingDTO) throws Exception {
        ContactDocument contactDocument = messageDTO.getContactDocument();
        Optional<ChatbotQueryResponse> chatbotQueryResponseOptional = Optional.empty();
        chatbotQueryResponseOptional = chatbotService
                .getQueryResponse(contactDocument.getB_id(), contactDocument.getE_id(), receivedMessage, getQueryChannel(source),
                        contactDocument.getC_id(), contactDocument.getM_c_id(), isReceivedDuringBusinessHour,new LivechatAnonymousConversationDataDto(), null);
        RobinSessionEnum robinSessionEnum=RobinSessionEnum.AUTO;
        try {
            sendIntentBasedChatbotReply(messageDTO, chatbotQueryResponseOptional,
                    isReceivedDuringBusinessHour,source,robinAutoReplyConfig, businessTimingDTO);
        }catch (Exception e){
            robinSessionEnum=RobinSessionEnum.MANUAL;
            log.info("received message chatsessionstatus is Manual for more than 24 hours but send fail reverting it to manual for businessId:{}", contactDocument.getB_id());
        }
        log.info("received message chatsessionstatus is Manual for more than 24 hours converting it to auto for businessId:{}", contactDocument.getB_id());
        updateContactDocument(contactDocument,  robinSessionEnum);
    }

    private void createOrUpdateRobinSessionDocument(ContactDocument contactDocument,String channel) {
        RobinSessionDocument robinSessionDocument = contactDocument.getRobinSessionDocument() == null ? new RobinSessionDocument() : contactDocument.getRobinSessionDocument();
        if(contactDocument.getRobinSessionDocument()==null) {
            robinSessionDocument.setRobinSession(RobinSessionEnum.AUTO);
            robinSessionDocument.setCreatedAt(new Date().getTime());
        }
        if(Objects.nonNull(channel) && channel.equals(Source.CONTACT_US.name())){
            channel = Source.SMS.name();
        }
        robinSessionDocument.setChannel(channel);
        robinSessionDocument.setLastUpdatedAt(new Date().getTime());
        contactDocument.setRobinSessionDocument(robinSessionDocument);
        messengerContactService.updateContactDocumentOnES(contactDocument, contactDocument.getId().toString(), contactDocument.getE_id());
    }

    private void triggerAutoResponseForGoogle(MessageDTO messageDTO, Date lastMsgOn) {
        // ------------------ create partial payload for auto response and call send api
        // in async -----
        SendMessageDTO message = new SendMessageDTO();
        message.setUserId(-1);
        message.setFromBusinessId(messageDTO.getBusinessDTO().getBusinessId());
        message.setToCustomerId(messageDTO.getMessengerContact().getId().toString());
        message.setBusinessIdentifierId(message.getFromBusinessId().toString());
        message.setBOT(true);
        autoResponderService.sendAutoResponseForGoogleMessaging(message, lastMsgOn,messageDTO);
    }


    public String replaceTextWithBusinessNamePrePopulated(String replacetextValue, String businessName) {
        if (StringUtils.isBlank(replacetextValue)
                || !StringUtils.contains(replacetextValue, "[BIZ_NAME]") || StringUtils.isEmpty(businessName)) {
            return replacetextValue;
        }
        if (StringUtils.isBlank(businessName)) {
            businessName = StringUtils.EMPTY;
        }
        if (replacetextValue.contains("[BIZ_NAME]")) {
            replacetextValue = replacetextValue.replace("[BIZ_NAME]", businessName);
        }
        return replacetextValue;
    }

    public ChatbotService.QueryChannel getQueryChannel(Source source){
        switch (source) {
            case SMS:
            case CONTACT_US:
                 return ChatbotService.QueryChannel.SMS;
            case FACEBOOK:
                return ChatbotService.QueryChannel.FACEBOOK;
            case INSTAGRAM:
                return ChatbotService.QueryChannel.INSTAGRAM;
            case GOOGLE:
                return ChatbotService.QueryChannel.GOOGLE;
            case TWITTER:
                return ChatbotService.QueryChannel.TWITTER;
            default:
                throw new IllegalArgumentException("Invalid source: " + source.name());
        }
    }

    public String getMessage(MessageDTO messageDTO){
        if(messageDTO.getSource() == null){
            return "";
        }
        switch (Source.getValue(messageDTO.getSource())) {
            case SMS:
            case CONTACT_US:
                return ((SMSMessageDTO)messageDTO).getBody();
            case FACEBOOK:
                return ((FacebookMessageRequest)messageDTO).getEntry().get(0).getMessaging().get(0).getMessage().getText();
            case INSTAGRAM:
                return ((InstagramMessageRequest)messageDTO).getEntry().get(0).getMessaging().get(0).getMessage().getText();
            case GOOGLE:
                return ((GoogleUserMessage) messageDTO).getMessage().getText();
            case TWITTER:
                return ((TwitterMessageRequest) messageDTO).getDirect_message_events().get(0).getMessage_create().getMessage_data().getText();
            default:
                return "";
        }
    }

    @Async
    @Override
    public void updateChatSession(Integer accountId,Integer mcId,UserDTO userDTO,String channel){
        log.info("update chat session for accountId: {}, m_c_id: {}, user: {}",accountId,mcId,userDTO);
        if(userDTO!=null && userDTO.getId()!=MessengerConstants.ROBIN_REPLY_USER && userDTO.getId()!=MessengerConstants.AUTO_REPLY_USER) {
            ContactDocument contactDocument=messengerContactService.getContact(accountId, mcId);
            RobinSessionDocument robinSessionDocument = new RobinSessionDocument();
            if(contactDocument.getRobinSessionDocument()!=null) {
                robinSessionDocument = contactDocument.getRobinSessionDocument();
                robinSessionDocument.setRobinSession(RobinSessionEnum.MANUAL);
            }else{
                robinSessionDocument.setRobinSession(RobinSessionEnum.MANUAL);
                robinSessionDocument.setCreatedAt(new Date().getTime());
            }
            if(Objects.nonNull(channel) && channel.equals(Source.CONTACT_US.name())){
                channel = Source.SMS.name();
            }
            robinSessionDocument.setChannel(channel);
            robinSessionDocument.setLastUpdatedAt(new Date().getTime());
            contactDocument.setRobinSessionDocument(robinSessionDocument);
            messengerContactService.updateContactDocumentOnES(contactDocument, contactDocument.getId().toString(), contactDocument.getE_id());
        }
    }
    
    
	private boolean handleUnsubKeywordAutoReply(MessageDTO messageDTO) throws Exception {
		if (!(messageDTO instanceof SMSMessageDTO)) {
			return false;
		}

		SMSMessageDTO smsMessageDTO = (SMSMessageDTO) messageDTO;
		if (smsMessageDTO.getPendingAutoReply() == null) {
			return false;
		}

		SendMessageDTO autoReply = TextPreferencesUtil.buildTextPreferenceAutoReply(smsMessageDTO);
		if (autoReply != null) {
			try {
				messengerEventHandlerService.handleEvent(autoReply);
				return true;
			} catch (Exception e) {
				log.error("Exception while sending keyword auto-reply message: {}", e.getMessage());
			}
		}
		return false;
	}
}
