package com.birdeye.messenger.service.impl;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.logging.Handler;
import java.util.stream.Collectors;

import com.birdeye.messenger.dao.entity.Sms;
import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.es.sro.ESUpsertRequest;
import com.birdeye.messenger.exception.*;
import com.birdeye.messenger.util.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.support.WriteRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.BusinessChatWidgetConfig;
import com.birdeye.messenger.dao.entity.BusinessMoveAudit;
import com.birdeye.messenger.dao.entity.CustomerSentimentAudit;
import com.birdeye.messenger.dao.entity.LiveChatSessionToken;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.PulseSurveyAudit;
import com.birdeye.messenger.dao.entity.PulseSurveyContext;
import com.birdeye.messenger.dao.entity.WebchatInstallationAudit;
import com.birdeye.messenger.dao.repository.BusinessMoveAuditRepository;
import com.birdeye.messenger.dao.repository.CustomerSentimentAuditRepository;
import com.birdeye.messenger.dao.repository.WebchatInstallationAuditRepository;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.BusinessMoveAuditEnum;
import com.birdeye.messenger.enums.ChatSessionStatus;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.enums.PulseSurveyStatusEnum;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.enums.WebchatInstallationAuditEnum;
import com.birdeye.messenger.enums.WebchatInstallationCrawlerStatus;
import com.birdeye.messenger.es.sro.ESUpdateByQueryRequest;
import com.birdeye.messenger.event.ConversationTagChangeEvent;
import com.birdeye.messenger.event.ReplyOnUAConversationEvent;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.ComponentCodeEnum;
import com.birdeye.messenger.exception.ErrorMessageBuilder;

import com.birdeye.messenger.external.dto.SurveyResponse;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.NLPService;
import com.birdeye.messenger.external.service.SurveyService;
import com.birdeye.messenger.external.service.UserService;
import com.birdeye.messenger.service.AssignmentService;
import com.birdeye.messenger.service.BusinessChatWidgetConfigService;
import com.birdeye.messenger.service.BusinessWebchatEventService;
import com.birdeye.messenger.service.ConversationService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.EmailService;
import com.birdeye.messenger.service.FirebaseService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.LiveChatService;
import com.birdeye.messenger.service.LiveChatSessionService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.MessengerEventHandler;
import com.birdeye.messenger.service.MessengerEventHandlerService;
import com.birdeye.messenger.service.NexusService;
import com.birdeye.messenger.service.PulseSurveyAuditService;
import com.birdeye.messenger.service.PulseSurveyService;
import com.birdeye.messenger.service.RedisHandler;
import com.birdeye.messenger.service.RedisLockService;
import com.birdeye.messenger.service.RoundRobinAssignmentService;
import com.birdeye.messenger.service.SmsService;
import com.birdeye.messenger.service.WebchatService;
import com.birdeye.messenger.service.payment.PaymentService;
import com.birdeye.messenger.smart.inbox.filters.service.SmartInboxFilterService;
import com.birdeye.messenger.sro.DeleteTeamEvent;
import com.birdeye.messenger.sro.MessagesDeleteRequest;
import com.birdeye.messenger.sro.UnassignConversationEvent;
import com.birdeye.messenger.sro.WebchatInstallationCrawlerResponse;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class MessengerEventHandlerServiceImpl implements MessengerEventHandlerService {

	private final List<MessengerEventHandler> messengerEventHandlers;
	private final MessengerContactService messengerContactService;
	private final BusinessService businessService;
	private final UserService userService;
	private final SmsService smsService;
	private final ContactService contactService;
	private final AssignmentService assignmentService;
	private final LiveChatSessionService livechatSessionService;
	private final WebchatService webchatService;
	private final RedisHandler redisHandler;
	private final LiveChatService liveChatService;
	private final BusinessChatWidgetConfigService businessChatWidgetConfigService;
	private final BusinessWebchatEventService businessWebchatEventService;
	private final WebchatInstallationAuditRepository webchatInstallationAuditRepository;
	private final ConversationService conversationService;
	@Autowired
	protected EmailService emailService;
	@Autowired
	protected CommunicationHelperService communicationHelperService;
    private final SurveyService surveyService;
    private final FirebaseService fcmService;
    private final CustomerSentimentAuditRepository customerSentimentAuditRepository;
    private final BusinessMoveAuditRepository	businessMoveAuditRepository;
    private final NexusService nexusService;
    private final PulseSurveyAuditService pulseSurveyAuditService;
    private final PulseSurveyService pulseSurveyService;
    private final ElasticSearchExternalService elasticSearchService;
    private final PaymentService paymentService;
	private final RoundRobinAssignmentService roundRobinAssignmentService;
    private final RedisLockService redisLockService;
    
    private final SmartInboxFilterService smartInboxFilterService;
    
    @Autowired
    protected KafkaService kafkaService;
    
    @Autowired
    protected NLPService nlpService;

    @Override
    public MessageResponse handleEvent(MessageDTO messageDTO) throws Exception { // MessageEvent event
    	handleProfaneMessage(messageDTO);
		setPaymentLinkIfApplicable(messageDTO);
    	if (messageDTO.getCreatePaymentLinkResponse()!=null && messageDTO.getCreatePaymentLinkResponse().isCreatedViaTrigger()) {
    		//return if payment is already created via trigger.
    		return null;
    	}
        MessengerEventHandler handler = resolveHandler(messageDTO);
        log.debug("handleEvent: called {} handler {} ", handler.getEvent(), handler.getClass().getName());
		MessageResponse response = handler.handle(messageDTO);
		log.debug("handleEvent: response {}", response);
		if(Objects.nonNull(response) && Objects.nonNull(response.getMessages()) && Objects.nonNull(response.getMessages().first())
			&& !Source.EMAIL.getSourceId().equals(response.getMessages().first().getSource()) &&
				StringUtils.isNotBlank(messageDTO.getRequestSource()) && "MOBILE".equalsIgnoreCase(messageDTO.getRequestSource()) && Objects.nonNull(response) && Objects.isNull(messageDTO.getEvent()) && BooleanUtils.isFalse(messageDTO.getIsAppCompatible())){
			modifyResponseForIncompatibleMobileVersion(response,messageDTO);
		}
		if (Objects.nonNull(response) && Objects.nonNull(response.getMessages()) && Objects.nonNull(response.getMessages().first())
				&& CommunicationDirection.SEND.equals(response.getMessages().first().getDirection())) {
			if (BooleanUtils.isTrue(messageDTO.getSendAndClose())){
				log.info("SendAndClose: accId : {}, mcId : {} by user : {}", messageDTO.getAccountId(), response.getMcId(), messageDTO.getUserId());
				ConversationStatusRequest updateStatusRequest = new ConversationStatusRequest();
				updateStatusRequest.setOpen(false);
				conversationService.updateStatus(messageDTO.getAccountId(), messageDTO.getUserId(), response.getMcId(), updateStatusRequest, false);
			}
		}
		return response;
	}
    //Filter and reject outbound messages containing profanity.
	private void handleProfaneMessage(MessageDTO messageDTO) {
		if(messageDTO.getUserId()!=null && !messageDTO.isBOT()) {
    		SendMessageDTO sendMessageDTO=(SendMessageDTO)messageDTO;
    		if(StringUtils.isNotBlank(sendMessageDTO.getBody()) && nlpService.isTextProfane(sendMessageDTO.getBody())) {
    			log.info("Message cannot be send becuase it is classified as profane by nlp service: request body: {}", sendMessageDTO);
    			throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.MESSAGE_SEND_REJECTED, HttpStatus.BAD_REQUEST));
    		}
    	}
	}

	@Override
    public MessengerEventHandler resolveHandler(MessageDTO messageDTO) {
		MessengerEventHandler handler = null;
		if (Objects.nonNull(messageDTO.getEvent())) {
			handler = getHandler(messageDTO.getEvent());
			log.info("#### resolveHanlder by event : {}", messageDTO.getEvent());
		} else {
			Source source = findSource(messageDTO);
			log.info("#### resolveHanlder by last message : {}", source);
			messageDTO.setSource(source.getSourceId());
			SendMessageDTO dto = (SendMessageDTO) messageDTO;
			MessengerContact messengerContact = Objects.nonNull(dto.getMessengerContact()) ? dto.getMessengerContact() :
					messengerContactService.findById(Integer.parseInt(dto.getToCustomerId()));
			if(messengerContact == null) return null;
			List<Integer> userLocIds = dto.getBusinessIds();
			if (CollectionUtils.isNotEmpty(userLocIds) && !userLocIds.contains(messengerContact.getBusinessId())) {
				log.info("sendMessage: User {} doesn't have access to the location for mcId {}", dto.getUserId(), messengerContact.getId());
				throw new NotAuthorizedException(ErrorCode.USR_HAS_NO_LOC_ACCESS, ErrorCode.USR_HAS_NO_LOC_ACCESS.getErrorMessage());
			}
			switch (source) {
				case SMS:
				case WEB_CHAT:
				case WEB_CHAT_BIRDEYE_PROFILE:
				case MOBILE:
				case CONTACT_US:
				case APPOINTMENT:
					handler = getHandler(MessengerEvent.SMS_SEND);
					break;
				case LIVE_CHAT_RECEIVE:
				case LIVE_CHAT_BIRDEYE_PROFILE_RECEIVE:
				case LIVE_CHAT_SEND:
				case LIVE_CHAT_BIRDEYE_PROFILE_SEND:
					handler = decideHandlerForLiveChat(messageDTO);
					break;
				case FACEBOOK:
					handler = getHandler(MessengerEvent.FACEBOOK_SEND);
					break;
				case INSTAGRAM:
					handler = getHandler(MessengerEvent.INSTAGRAM_SEND);
					break;
				case EMAIL:
					handler = getHandler(MessengerEvent.EMAIL_SEND);
					break;
				case GOOGLE:
					handler = getHandler(MessengerEvent.GOOGLE_SEND);
					break;
				case APPLE:
					handler = getHandler(MessengerEvent.APPLE_SEND);	
					break;
                case SECURE_MESSAGE:
                    handler = getHandler(MessengerEvent.SECURE_MESSAGE_SEND);
                    break;
				case TWITTER:
					handler = getHandler(MessengerEvent.TWITTER_SEND);
					break;
				case WHATSAPP:
					handler = getHandler(MessengerEvent.WHATSAPP_SEND);
					break;
				case CUSTOM:
					handler = getHandler(MessengerEvent.CUSTOM_CHANNEL_SEND);
					break;
				default:
					handler = getHandler(MessengerEvent.SMS_SEND);
					break;
			}
		}
		return handler;
	}

	private MessengerEventHandler decideHandlerForLiveChat(MessageDTO messageDTO) {
		MessengerEventHandler handler;
		Integer businessId = ((SendMessageDTO) messageDTO).getFromBusinessId();
		Integer mcid = ((SendMessageDTO) messageDTO).getMessengerContact().getId();
		Optional<LiveChatSessionToken> session = livechatSessionService.getExistingSession(businessId, mcid);
		
		boolean customerOnline = isLiveChatSessionActive(session);
		boolean emailMandatory=session.isPresent() && BooleanUtils.isTrue(session.get().getEmailMandatory());
		messageDTO.setEmailMandatory(emailMandatory);
		// when user is online on live chat
		// send message to live chat widget
		if (customerOnline) {
			log.info("#### decideHandlerForLiveChat online : {}", session);
			handler = getHandler(MessengerEvent.LIVECHAT_SEND);
			handleSessionStateTransition(session);
		} else {
			log.info("#### decideHandlerForLiveChat offline : {}", session);
			// fall back to sms/email, when session isn't active anymore
			if(emailMandatory) {
				handler = getHandler(MessengerEvent.EMAIL_SEND);
			}else {
				handler = getHandler(MessengerEvent.SMS_SEND);
			}
		}
		return handler;
	}


	/**
	 * transition chat session to manual state Always setting status=manual as we
	 * anyway need to update the session (updated)latest activity date
	 */
	private void handleSessionStateTransition(Optional<LiveChatSessionToken> session) {
		// TODO: also update previous status when added
		session.get().setStatus(ChatSessionStatus.MANUAL);
		//BIRDEYE-80770
		if (session.get().getAutoReplySent() == null){
			session.get().setAutoReplySent(true);
		}
		session.get().setUpdated(new Date());
		session.get().setBusinessUserReplied(true);
		log.info("Biz user joined livechat-session:{} (no email notifications will be triggered further)", session.get().getSessionId());
		livechatSessionService.save(session.get());
	}

	/**
	 * Check if customer is currently online
	 * i.e. has a active live chat session
	 */
	public boolean isLiveChatSessionActive(Optional<LiveChatSessionToken> sessionOpt) {
		if (sessionOpt.isPresent() && sessionOpt.get().getStatus() != ChatSessionStatus.TERMINATED) {
			LiveChatSessionToken session = sessionOpt.get();
			String redisKey = "LIVECHAT_END_EVENT:" + session.getMcid();
			if (redisHandler.getKeyValueFromRedis(redisKey) != null) {
				liveChatService.sessionTerminated(session.getSessionId(), Source.getByName(session.getChannel()).getSourceId(), ActivityType.LIVECHAT_END);
				redisHandler.deleteKey(redisKey);
				// Taking pause to add delay between activity and message
				try {
					Thread.sleep(1000);
				} catch (Exception e) {
					log.error("Getting exception while taking pause isLiveChatSessionActive method ");
				}
				return false;
			}
			return true;
		} else {
			return false;
		}
	}

	private MessengerEventHandler getHandler(MessengerEvent event) {
		return messengerEventHandlers.stream().
				filter(messengerEventHandler -> messengerEventHandler.getEvent().equals(event))
				.findFirst().orElseThrow(() -> new MessengerException("Could not find any matching event handler"));
	}

	private Source findSource(MessageDTO messageDTO) {
		SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
		messageDTO.setBusinessDTO(
				businessService.getBusinessLiteDTO(Integer.parseInt(sendMessageDTO.getBusinessIdentifierId())));
		messageDTO.setMessengerContact(messengerContactService.getOrCreateContact(messageDTO.getBusinessDTO().getBusinessId(), Integer.parseInt(sendMessageDTO.getToCustomerId()), messageDTO.getBusinessDTO().getAccountId()));
		messageDTO.setSendEmailNotification(false);
		Source source = null;
		Integer lastMessageSourceId = null;
		log.info("#### findSource sendMessageDTO.source: {}", sendMessageDTO.getSource());
		if (sendMessageDTO.getSource() != null && sendMessageDTO.getSource() == 111) {
			lastMessageSourceId = sendMessageDTO.getSource();
			source = Source.getValue(lastMessageSourceId);
			return source;
		}
		if (sendMessageDTO.getSource() != null && sendMessageDTO.getSource() == 1) {
			lastMessageSourceId = sendMessageDTO.getSource();
		} else if (sendMessageDTO.getSource() != null && sendMessageDTO.getSource() == 3) {
			lastMessageSourceId = getLastMessageSourceId(messageDTO);
			if (lastMessageSourceId != null && lastMessageSourceId == 110) {
				lastMessageSourceId = 110;
			} else {
				lastMessageSourceId = 3;
			}
        } else if (sendMessageDTO.getSource() != null && sendMessageDTO.getSource() == 17) {
            lastMessageSourceId = sendMessageDTO.getSource();
            source = Source.getValue(lastMessageSourceId);
            return source;
        }  else if (sendMessageDTO.getSource() != null && sendMessageDTO.getSource() == 20) {
            lastMessageSourceId = sendMessageDTO.getSource();
            source = Source.getValue(lastMessageSourceId);
            return source;
        }
        else if (sendMessageDTO.getSource() != null && sendMessageDTO.getSource() == 21) {
            lastMessageSourceId = sendMessageDTO.getSource();
            source = Source.getValue(lastMessageSourceId);
            return source;
        }
        else {
			lastMessageSourceId = getLastMessageSourceId(messageDTO);
			
			// Custom channel - Ios mobile doesn't send us source in send if there are custom channel messages in that conversation
			if (lastMessageSourceId == 21 && messageDTO.getRequestSource() != null
					&& "MOBILE".equalsIgnoreCase(messageDTO.getRequestSource())) {
				ContactDocument contactDoc = messengerContactService.getContact(
						messageDTO.getBusinessDTO().getAccountId(), messageDTO.getMessengerContact().getId());
				if (contactDoc.getC_phone() == null && contactDoc.getC_email() != null) {
					lastMessageSourceId = 111;
				} else {
					lastMessageSourceId = 1;
				}

			}
			// set as a default value
			if (lastMessageSourceId == null) {
				lastMessageSourceId = 1;
			}
		}
		source = Source.getValue(lastMessageSourceId);
		return source;
	}

	private Integer getLastMessageSourceId(MessageDTO messageDTO) {
		Integer lastMessageSourceId;
		lastMessageSourceId = messengerContactService.getLastMessageSource(
				messageDTO.getMessengerContact().getId(), messageDTO.getBusinessDTO().getRoutingId());
		log.info("#### findSource lastMessageSourceId for messengerContact: {} is {}",
				messageDTO.getMessengerContact().getId(), lastMessageSourceId);

		if (lastMessageSourceId == null || lastMessageSourceId == 0) {
			lastMessageSourceId = messengerContactService.getLastReceivedMessageSource(
					messageDTO.getMessengerContact(),
					messageDTO.getBusinessDTO().getEnterpriseId() != null
							? messageDTO.getBusinessDTO().getEnterpriseId()
							: messageDTO.getBusinessDTO().getBusinessId());
			log.info("#### findSource lastReceivedSourceId from messengerContact: {}", lastMessageSourceId);
			if (lastMessageSourceId == 0 || lastMessageSourceId == 11) {
				lastMessageSourceId = messengerContactService.getLastMessageSource(
						messageDTO.getMessengerContact().getId(), messageDTO.getBusinessDTO().getRoutingId());
				log.info("#### findSource lastMessageSourceId from messengerContact: {}", lastMessageSourceId);
			}
			// set as a default value
			if (lastMessageSourceId == null) {
				lastMessageSourceId = 1;
			}
			log.info("#### findSource lastReceivedSourceId for messengerContact: {} is {}",
					messageDTO.getMessengerContact().getId(), lastMessageSourceId);
		}
		return lastMessageSourceId;
	}


	@Override
	public void processConversationTagChangeEvent(ConversationTagChangeEvent event) {
		if ((event.getAssignee() == null || event.getAssignee().getId() == null || event.getAssignee().getId() <= 0)
				&& event.getNewTag() == MessageTag.ARCHIVED.getCode() && event.getTriggeredBy() != null) {
			log.info("ConversationTagChangeEvent - {}", event);
			ConversationAssignmentRequestDTO request = new ConversationAssignmentRequestDTO();
			IdentityDTO from = event.getAssignee();
			UserDTO user = userService.getUserDTO(event.getTriggeredBy());
			IdentityDTO assignee = new IdentityDTO(user.getId(), user.getName(), "U", user.getEmailId());
			request.setDoNotNotify(true);
			request.setFrom(from);
			request.setMcId(event.getConversationId());
			request.setTo(assignee);
			request.setBulkAction(event.isBulkAction());
			assignmentService.assignConversation(request, null, event.getAccountId());
		}
	}

	@Override
	public void handleReplyOnUnassignedConversation(ReplyOnUAConversationEvent event) {
		log.info("[Event] [ReplyOnUAConversationEvent] : {}", event.toString());
		if (event.getMcId() != null && event.getMcId() != 0 && event.getReplier() != null) {
			ConversationAssignmentRequestDTO request = new ConversationAssignmentRequestDTO();
			IdentityDTO replier = new IdentityDTO(event.getReplier().getId(), event.getReplier().getName(), "U", event.getReplier().getEmailId());
			request.setDoNotNotify(true);
			request.setFrom(null);
			request.setMcId(event.getMcId());
			request.setTo(replier);
			assignmentService.assignConversation(request, null, event.getAccountId());
			return;
		}
		log.error("[Event] [ReplyOnUAConversationEvent] Bad event {}", event.toString());
	}

	@Override
	public void processDeleteTeamEvent(DeleteTeamEvent deleteTeamEvent) {
		//Deleting Team from Webchat configs
		webchatService.deleteTeamFromWebchat(deleteTeamEvent);
		//Unassign conversation assigned to the teams
		assignmentService.getConversationsAssignedToTeam(deleteTeamEvent);
		//Delete RoundRobin configuration corresponding to the team
		roundRobinAssignmentService.deleteTeam(deleteTeamEvent);
	}

	@Override
	public void unassignConversation(UnassignConversationEvent unassignConversationEvent) {
		assignmentService.unassignConversation(unassignConversationEvent);
	}

	@Override
	public void messengerAccessRevoked(UserEvent userEvent) {
		assignmentService.messengerAccessRevoked(userEvent);
	}

	@Override
	public void checkWebchatInstallationStatus(Integer widgetId) {
		//get the Websites beyond 24 hours from event table
		long startTime = System.currentTimeMillis();
		List<String> websites = businessWebchatEventService.findWebsitesByWidgetId(widgetId);
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("findWebsitesByWidgetId", startTime, endTime);
		//if websites not found mark as not installed
		if (CollectionUtils.isEmpty(websites)) {
			startTime = System.currentTimeMillis();
			BusinessChatWidgetConfig businessChatWidgetConfig = businessChatWidgetConfigService.getWebChatConfigByWidgetId(widgetId);
			endTime = System.currentTimeMillis();
			LogUtil.logExecutionTime("getWebChatConfigByWidgetId", startTime, endTime);
			updateBusinessChatConfigInstallationStatus(businessChatWidgetConfig, null, false);
			return;
		}
		//call crawler to check the status of websites
		webchatService.requestCrawlerToCheckInstallationStatus(widgetId, websites, Constants.CrawlerEvent.CRON, KafkaTopicEnum.WEBCHAT_INSTALLATION_REQUEST_CRON);
	}


	@Override
	public void updateWebchatInstallationStatus(WebchatInstallationCrawlerResponse response) {
		String websitesStr = null;
		List<String> websites = new ArrayList<>();
		Boolean installed = false;
		BusinessChatWidgetConfig businessChatWidgetConfig = businessChatWidgetConfigService.getWebChatConfigByWidgetId(response.getWidgetId());
		if (businessChatWidgetConfig != null) {
			if (CollectionUtils.isNotEmpty(response.getWebsites())) {
				websites = response.getWebsites().stream().filter(r -> r.getStatus().equals(WebchatInstallationCrawlerStatus.INSTALLED.getValue())).map(website -> website.getWebsite()).collect(Collectors.toList());
				websitesStr = websites.stream().collect(Collectors.joining(","));
				if (CollectionUtils.isNotEmpty(websites)) {
					installed = true;
				}
			}
			updateBusinessChatConfigInstallationStatus(businessChatWidgetConfig, websitesStr, installed);
			if (response.getRequestType().equalsIgnoreCase(Constants.CrawlerEvent.API)) {
				FirebaseWebchatInstallationResponse response1 = new FirebaseWebchatInstallationResponse(installed, websites, new Date());
				fcmService.pushInstallationStatusToFirebase(response.getWidgetId(), response1);
			}
			updateAuditTable(installed, response.getRequestId());
		}
	}

	private void updateBusinessChatConfigInstallationStatus(BusinessChatWidgetConfig businessChatWidgetConfig, String websites, Boolean installed) {
		businessChatWidgetConfig.setWebsites(websites);
		businessChatWidgetConfig.setInstalled(installed ? 1 : 0);
		businessChatWidgetConfig.setStatusUpdatedOn(new Date());
		businessChatWidgetConfigService.updateBusinessChatWidgetConfig(businessChatWidgetConfig);
	}

	private void updateAuditTable(Boolean installed, Integer requestId) {
		Optional<WebchatInstallationAudit> webchatInstallationAuditOptional = webchatInstallationAuditRepository.findById(requestId);
		if (webchatInstallationAuditOptional.isPresent()) {
			WebchatInstallationAudit webchatInstallationAudit = webchatInstallationAuditOptional.get();
			webchatInstallationAudit.setStatus(installed ? WebchatInstallationAuditEnum.SUCCESS.getValue() : WebchatInstallationAuditEnum.FAILED.getValue());
			webchatInstallationAudit.setUpdated(new Date());
			webchatInstallationAuditRepository.save(webchatInstallationAudit);
		}
	}

	@Override
    public void deleteConversations(MessagesDeleteRequest messagesDeleteRequest) {
		Map<String, Object> dataModel = new HashMap<>();
		dataModel.put("businessId", String.valueOf(messagesDeleteRequest.getBusinessId()));
		dataModel.put("enterpriseId", String.valueOf(messagesDeleteRequest.getRoutingId()));
		Optional<BusinessMoveAudit> businessMoveAuditOptional = businessMoveAuditRepository.findById(messagesDeleteRequest.getAuditId());
		List<MessengerContact> messengerContacts = messengerContactService.findMessengerContactByBusinessId(messagesDeleteRequest.getBusinessId());
		boolean contactDocumentStatus = deleteContactDocument(dataModel, messagesDeleteRequest);
		if (contactDocumentStatus) {
			log.info("Contact documents deleted successfully for routing id:{} , business id:{}", messagesDeleteRequest.getRoutingId(), messagesDeleteRequest.getBusinessId());
		}
		boolean messageDocStatus = deleteMessageDocument(dataModel, messagesDeleteRequest, messengerContacts);
		if (messageDocStatus) {
			log.info("Message documents deleted successfully for routing id:{} , business id:{}", messagesDeleteRequest.getRoutingId(), messagesDeleteRequest.getBusinessId());
		}

		if (contactDocumentStatus && messageDocStatus) {
			BusinessMoveAudit businessMoveAudit = businessMoveAuditOptional.get();
			businessMoveAudit.setStatus(BusinessMoveAuditEnum.SUCCESS.getValue());
			businessMoveAudit.setDescription("Messages deleted successfully!!");
		} else {
			BusinessMoveAudit businessMoveAudit = businessMoveAuditOptional.get();
			businessMoveAudit.setStatus(BusinessMoveAuditEnum.FAILED.getValue());
			businessMoveAudit.setDescription("Messages deletion Failed!!");
		}
	}

	private boolean deleteContactDocument(Map<String, Object> dataModelMap, MessagesDeleteRequest messagesDeleteRequest) {
		dataModelMap.put("size", "10000");
		ESRequest esRequest = new ESRequest.Builder(new ESRequest())
				.addRoutingId(messagesDeleteRequest.getRoutingId())
				.addTemplateAndDataModel(Constants.Elastic.DELETE_CONTACT_BY_QUERY_BY_BUSINESS_ID, dataModelMap)
				.addIndex(Constants.Elastic.CONTACT_INDEX)
				.build();
		return elasticSearchService.deleteByQuery(esRequest);
	}

	//need to delete in bulk
	private boolean deleteMessageDocument(Map<String, Object> dataModelMap, MessagesDeleteRequest messagesDeleteRequest, List<MessengerContact> messengerContacts) {
		for (MessengerContact mc : messengerContacts) {
			dataModelMap.put("cId", String.valueOf(mc.getId()));
			dataModelMap.put("size", "10000");
			ESRequest esRequest = new ESRequest.Builder(new ESRequest())
					.addRoutingId(messagesDeleteRequest.getRoutingId())
					.addTemplateAndDataModel(Constants.Elastic.DELETE_MESSAGES_BY_C_ID, dataModelMap)
					.addIndex(Constants.Elastic.MESSAGE_INDEX)
					.build();
			if (!elasticSearchService.deleteByQuery(esRequest)) {
				log.info("Messages  deletion failed for conversation id:{}", mc.getId());
				return false;
			}
		}
		return true;
	}

	@Override
	public void processCustomerSentimentUpdateEvent(CustomerSentimentUpdateEvent customerSentimentUpdateEvent) throws Exception {
		if (customerSentimentUpdateEvent.getSentimentRemoved() == null && customerSentimentUpdateEvent.getSentimentScore() == null){
			throw new  InputValidationException(new ErrorMessageBuilder(ErrorCode.INVALID_REQUEST, ComponentCodeEnum.AUDIT));
		}
		BusinessDTO businessDTO = businessService.getBusinessDTO(customerSentimentUpdateEvent.getBusinessId());
		Integer accountId = Objects.nonNull(businessDTO) ? businessDTO.getAccountId() : null;

		updateCustomerSentimentsInES(customerSentimentUpdateEvent, accountId);

		Long timeStamp = new Date().getTime();
		
		FirebaseDto firebaseDto = new FirebaseDto();
		firebaseDto.setAccountId(accountId);
		firebaseDto.setBusinessId(customerSentimentUpdateEvent.getBusinessId());
		firebaseDto.setTimestamp(timeStamp);
		fcmService.mirrorOnWeb(firebaseDto);
	}

	private void updateCustomerSentimentsInES(CustomerSentimentUpdateEvent customerSentimentUpdateEvent, Integer accountId) {
		log.info("updating sentiments in contact document for customer Ids: {}", customerSentimentUpdateEvent.getCids());
        Map<String, Object> data = new HashMap<>();
		data.put("cids", ControllerUtil.toCommaSeparatedString(customerSentimentUpdateEvent.getCids()));
		data.put("count", customerSentimentUpdateEvent.getCids().size());
		ESUpdateByQueryRequest.Builder builder = new ESUpdateByQueryRequest.Builder(new ESUpdateByQueryRequest());
		builder.index(Constants.Elastic.CONTACT_INDEX).queryTemplateFile(Constants.Elastic.GET_CONTACT_BY_CUSTOMER_IDS).freeMarkerDataModel(data).routingId(accountId);
		Map<String, Object> scriptData = new HashMap<>();
		String script="ctx._source.sentimentScore=params.sentimentScore;ctx._source.sentiment=params.sentiment;ctx._source.lastUpdateDate=params.lastUpdateDate";
        if ("survey".equalsIgnoreCase(customerSentimentUpdateEvent.getEventSource())
                || "survey".equals(customerSentimentUpdateEvent.getSource())) {
            script += ";ctx._source.latestSurveyScore=params.sentimentScore;ctx._source.updatedAt=params.updatedAt";
        }
		scriptData.put("inline",script);
		builder.scriptParam(scriptData);

		DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
		String updatedAt = df.format(new Date());

		Map<String, Object> params = new HashMap<>();
		params.put("sentimentScore", customerSentimentUpdateEvent.getSentimentScore());
		params.put("sentiment", customerSentimentUpdateEvent.getSentiment());
		params.put("lastUpdateDate", (new Date()).getTime());
		params.put("updatedAt", updatedAt);
		builder.params(params);

		boolean updateByQueryResponse = elasticSearchService.updateByQueryWithRefresh(builder.build(), true);
		if (!updateByQueryResponse){
			log.info("Customer sentiment updation failed for cids:{}",customerSentimentUpdateEvent.getCids());
			CustomerSentimentAudit customerSentimentAudit = new CustomerSentimentAudit(customerSentimentUpdateEvent.getCids().toString(),
					customerSentimentUpdateEvent.getEcid(), Constants.FAILED, new Date());
			customerSentimentAuditRepository.save(customerSentimentAudit);
			return;

		}
		log.info("Customer sentiment updation success for cids:{}", customerSentimentUpdateEvent.getCids());
		updateCustomerSentimentsInESMessages(customerSentimentUpdateEvent,accountId);
	}

	@Override
	public void handlePulseSurveyQuestionEvent(PulseSurveyQuestionRequest request) throws Exception {
		log.info("Send Pulse Survey SMS & Update Context {}", request);
		Optional<Lock> lockOpt = Optional.empty();
		try {
			String lockKey = Constants.CUSTOMER_ID_PREFIX+request.getCustomerId();
			lockOpt = redisLockService.tryLock(lockKey, 1,TimeUnit.SECONDS);
			if (!lockOpt.isPresent()) {
				log.info("Lock is already acquired for the key:{}", lockKey);
				kafkaService.publishToKafkaAsync(KafkaTopicEnum.PULSE_SURVEY_NEXT_DELAYED_QUEUE,
						request);
				return;
			}
			BusinessDTO businessDto = businessService.getBusinessLiteDTO(request.getFromBusinessId());
			PulseSurveyStatusEnum status = PulseSurveyStatusEnum.getPulseSurveyStatus(request.getStatus());
			String responseStatus = null;
			switch (status) {
			case IN_PROGRESS:
			case COMPLETED:
				//Update Context Status Change and Send SMS
				request.setStatus(status.getName());
				responseStatus = "valid";
				PulseSurveyContext updatedContext = pulseSurveyService.updatePulseSurveyContextForSurveyEvent(request, businessDto, responseStatus);
				if (updatedContext != null && request.getBody() != null)
					pulseSurveySendSms(updatedContext, request);
				break;
			case FAILED:
				//Update Context Status and Don't Send SMS for first Q invalid response else send thank you
				request.setStatus(status.getName());
				responseStatus = "invalid";
				PulseSurveyContext updatedFailedContext = pulseSurveyService.updatePulseSurveyContextForSurveyEvent(request, businessDto, responseStatus);
				if (updatedFailedContext != null) {
					if (request.getBody() != null)
						pulseSurveySendSms(updatedFailedContext, request);
					//Re-process the previous response as Regular SMS
					PulseSurveyAudit audit = pulseSurveyAuditService.getPulseSurveyAudit(request.getAuditId());
					if (audit != null) {
						SMSMessageDTO messageDto = JSONUtils.fromJSON(audit.getPayload(), SMSMessageDTO.class);
						messageDto.setEvent(MessengerEvent.SMS_RECEIVE);
						messageDto.setSource(1);
						handleEvent(messageDto);
					}
				}
				break;
			default :
				log.warn("Send Pulse Survey SMS & Update Context Failed - Invalid Status");
			}
			CustomerDTO customerDTO = contactService.findById(request.getCustomerId());
			MessengerContact messengerContact = messengerContactService.getOrCreateContactForExistingCustomer(businessDto.getBusinessId(), request.getCustomerId(), businessDto.getAccountId());
			log.info("SurveyEvent - Pulse Survey Status for mcId {} is {}", messengerContact.getId(), PulseSurveyContext.isOngoingPulseSurvey(status.getName()));
			customerDTO.setOngoingPulseSurvey(PulseSurveyContext.isOngoingPulseSurvey(status.getName()));
			ContactDocument contactDocument=messengerContactService.updateContactOnES(messengerContact, customerDTO, businessDto,MessageTag.INBOX, null);
			fcmService.pushToFireBase(contactDocument, null, false);
		} finally {
			if (lockOpt.isPresent()) {
				redisLockService.unlock(lockOpt.get());
			}
		}
	}

	@Override
	public void handlePulseSurveySamayEvent(SamayPayload payload) {
		log.info("Pulse Event from Samay {}", payload);
		PulseSurveyStatusEnum status = PulseSurveyStatusEnum.getPulseSurveyStatus(payload.getStatus());
		PulseSurveyContext existingContext = pulseSurveyService.getPulseSurveyContextById(payload.getContextId());
		boolean isTerminated = false;
		if (existingContext != null) {
			Optional<Lock> lockOpt = Optional.empty();
			try {
				String lockKey = Constants.CUSTOMER_ID_PREFIX+existingContext.getCustomerId();
				lockOpt = redisLockService.tryLock(lockKey, 1,TimeUnit.SECONDS);
				if (!lockOpt.isPresent()) {
					log.info("Lock is already acquired for the key:{}", lockKey);
					kafkaService.publishToKafkaAsync(KafkaTopicEnum.PULSE_SURVEY_SAMAY_DELAYED_QUEUE,
							payload);
					return;
				}

				BusinessDTO businessDto = businessService.getBusinessLiteDTO(existingContext.getBusinessId());
				switch (status) {
				case ACTIVE:
					if (status.getName().equalsIgnoreCase(existingContext.getStatus())) {
						log.info("Pulse Event from Samay, Terminating Active Context {}", payload);
						pulseSurveyService.updatePulseSurveyContextStatus(existingContext, PulseSurveyStatusEnum.TERMINATED.getName());
						isTerminated = true;
					}
					break;
				case IN_PROGRESS:
					if (status.getName().equalsIgnoreCase(existingContext.getStatus())) {
						//Check time difference between expiry and last_updated_date,
						//if last_updated_date falls after expiry
						Date expiryDate = new Date(payload.getExpiry());
						Date lastUpdatedDate = existingContext.getLastUpdatedDate();
						long difference_In_Time = expiryDate.getTime() - lastUpdatedDate.getTime();
						long difference_In_Minutes = (difference_In_Time/(1000 * 60)) % 60;
						Long timeOut = Long.valueOf(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("pulse_survey_timeout_2_minutes", 5));
						if (difference_In_Minutes > 0 && difference_In_Minutes >= timeOut) {
							log.info("Pulse Event from Samay, Terminating In_Progress Context {}", payload);
							pulseSurveyService.updatePulseSurveyContextStatus(existingContext, PulseSurveyStatusEnum.TERMINATED.getName());
							isTerminated = true;
							SurveyResponse surveyResponse = surveyService.getSurveyById(existingContext.getSurveyId());
							if (surveyResponse != null && surveyResponse.getThankYouMessage() != null) {
								PulseSurveyQuestionRequest req = new PulseSurveyQuestionRequest(existingContext.getBusinessId(),
										existingContext.getCustomerId(), surveyResponse.getThankYouMessage(), null, null, -7, null);
								try {
									pulseSurveySendSms(existingContext, req);
								} catch (Exception e) {
									log.error("Getting Exception while Sending ThankYou Message pulseSurveySendSms", e);
								}
							}
						}
					}
					break;
				default:
					log.warn("Handle Pulse Survey Samay Event Failed - Invalid Status");
				}
				if (isTerminated) {
					log.info("Context Terminated in DB - Terminating in ES for customerId {} ", existingContext.getCustomerId());
					MessengerContact messengerContact = messengerContactService.findByCustomerId(existingContext.getCustomerId());
					DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
					ContactDocument contactDocument = new ContactDocument();
					contactDocument.setUpdatedAt(df.format(new Date()));
					contactDocument.setOngoingPulseSurvey(false);
					messengerContactService.updateContactOnES(messengerContact.getId(), contactDocument, businessDto.getAccountId());
					//Firebase Sync
					contactDocument.setB_id(existingContext.getBusinessId());
					fcmService.pushToFireBase(contactDocument, null, false);
				}
			}  finally {
				if (lockOpt.isPresent()) {
					redisLockService.unlock(lockOpt.get());
				}
			}
		}
	}

	private void pulseSurveySendSms(PulseSurveyContext context, PulseSurveyQuestionRequest request) throws Exception {
		BusinessDTO businessDto = businessService.getBusinessLiteDTO(request.getFromBusinessId());

//		MessengerContact messengerContact = messengerContactService.getOrCreateContactForExistingCustomer(
//				request.getFromBusinessId(), request.getCustomerId(), businessDto.getAccountId());
		
		CustomerDTO customerDTO = contactService.findById(request.getCustomerId());

		SendMessageDTO messageDTO = new SendMessageDTO();
		messageDTO.setBody(request.getBody());
		messageDTO.setFromBusinessId(request.getFromBusinessId());
		messageDTO.setCustomerId(request.getCustomerId());
		messageDTO.setBusinessIdentifierId(String.valueOf(request.getFromBusinessId()));
		messageDTO.setToCustomerId(String.valueOf(request.getCustomerId()));
		messageDTO.setUserId(request.getUserId());
		messageDTO.setSurveyType("nps");
		messageDTO.setToPhone(customerDTO.getPhoneE164());

		String fromPhone = smsService.getFormattedBusinessNumber(businessDto.getBusinessId());
		messageDTO.setFromPhone(fromPhone);

		Sms sms = smsService.saveSMS(messageDTO);

		//Audit & Send to Nexus
		pulseSurveyAuditService.auditPulseSurveyResponse(context.getId(), messageDTO);

		ConversationWrapperMessage conversation = DtoToEntityConverter.directNexusRequest(messageDTO, sms.getSmsId(),businessDto.getAccountId());
		nexusService.sendSMS(conversation);

	}

	@Override
	public void handleRemoveInstagramMappingEvent(List<UpdateInstagramMappingEvent> payload) {
		if(CollectionUtils.isNotEmpty(payload)) {
			payload.forEach(event->{
				if(event!=null && event.getLocationId()!=null) {
					redisHandler.evictInstagramStatusCache(event.getLocationId());
				}
			});
		}


	}

	@Override
	public void handleAddInstagramMappingEvent(UpdateInstagramMappingEvent event) {
		if(event!=null && event.getLocationId()!=null) {
			redisHandler.evictInstagramStatusCache(event.getLocationId());
		}
	}

	private void setPaymentLinkIfApplicable(MessageDTO messageDTO) {
		if(Objects.nonNull(messageDTO.getPaymentRequest())) {
			SendMessageDTO dto = (SendMessageDTO) messageDTO;
			BusinessDTO businessDTO = businessService.getBusinessDTO(dto.getFromBusinessId());
			messageDTO.setBusinessDTO(businessDTO);
			MessengerContact mc =Objects.nonNull(dto.getMessengerContact()) ? dto.getMessengerContact() : 
				messengerContactService.findById(Integer.parseInt(dto.getToCustomerId()));

			messageDTO.setMessengerContact(mc);
			
			CustomerDTO customerDTO = Objects.nonNull(dto.getCustomerDTO()) ? dto.getCustomerDTO() :
				contactService.findByIdWithCustomFields(mc.getCustomerId());
			messageDTO.setCustomerDTO(customerDTO);
			
			log.info(new ErrorMessageBuilder(ErrorCode.BLANK, ComponentCodeEnum.AUDIT)
					.message("customer - {}, business - {}, M_C_ID - {}", customerDTO.getId(), 
							businessDTO.getBusinessId(), mc.getId()).build());
			paymentService.decorateWithPaymentLink(messageDTO);
		}
	}
	
	@Override
	public void handleUpdateInvoiceNumberEvent(UpdateInvoiceNumberEvent payload) {
		log.info("updating invoice number in conversation for payment Id: {}", payload.getPaymentId());
		
		Map<String, Object> data = new HashMap<>();
        data.put("paymentId", payload.getPaymentId());
        data.put("m_c_id", payload.getConversationId());
        Map<String, Object> scriptData = new HashMap<>();
        scriptData.put("inline", "ctx._source.paymentInfo.invoiceNumber=params.invoiceNumber");
        Map<String, Object> params = new HashMap<>();
        params.put("invoiceNumber", payload.getInvoiceNumber());
         
         ESUpdateByQueryRequest.Builder builder = new ESUpdateByQueryRequest.Builder(new ESUpdateByQueryRequest())
                .index(Constants.Elastic.MESSAGE_INDEX)
                .queryTemplateFile(Constants.Elastic.GET_MESSAGE_BY_PAYMENT_ID)
                .routingId(payload.getAccountId())
                .freeMarkerDataModel(data)
                .scriptParam(scriptData)
                .params(params);
         
         boolean updateByQueryResponse=false;
         try {
        	 updateByQueryResponse = elasticSearchService.updateByQuery(builder.build());
         } catch(Exception e) {
        	 log.error("Invoice Number updation failled got exception : {}",e.toString());
        	 
         }
		if (!updateByQueryResponse){
			log.error("Invoice Number updation failed for paymentId :{}",payload.getPaymentId());
			
			return;

		}
		log.info("Invoice Number updation success for paymentId:{}", payload.getPaymentId());
	}

	@Override
	public void deleteConversationsForBusinessMigration(MessagesDeleteRequest messagesDeleteRequest) {
		if(CollectionUtils.isNotEmpty(messagesDeleteRequest.getMessengerContactIdList())) {
			Map<String, Object> dataModel = new HashMap<>();
			dataModel.put("businessId", String.valueOf(messagesDeleteRequest.getBusinessId()));
			dataModel.put("enterpriseId", String.valueOf(messagesDeleteRequest.getRoutingId()));
			dataModel.put("m_c_id", ControllerUtil.toCommaSeparatedString(messagesDeleteRequest.getMessengerContactIdList()));
			Optional<BusinessMoveAudit> businessMoveAuditOptional = businessMoveAuditRepository.findById(messagesDeleteRequest.getAuditId());
			boolean contactDocumentStatus = deleteContactDocumentForBusinessMigration(dataModel, messagesDeleteRequest);
			if (contactDocumentStatus) {
				log.info("Contact documents deleted successfully for routing id:{} , business id:{}", messagesDeleteRequest.getRoutingId(), messagesDeleteRequest.getBusinessId());
			}
			boolean messageDocStatus = deleteMessageDocumentForBusinessMigration(dataModel, messagesDeleteRequest);
			if (messageDocStatus) {
				log.info("Message documents deleted successfully for routing id:{} , business id:{}", messagesDeleteRequest.getRoutingId(), messagesDeleteRequest.getBusinessId());
			}

			if (businessMoveAuditOptional.isPresent()) {
				BusinessMoveAudit businessMoveAudit = businessMoveAuditOptional.get();
				if (contactDocumentStatus && messageDocStatus) {
					businessMoveAudit.setStatus(BusinessMoveAuditEnum.SUCCESS.getValue());
					businessMoveAudit.setDescription("Messages deleted successfully!!");
				} else {
					businessMoveAudit.setStatus(BusinessMoveAuditEnum.FAILED.getValue());
					businessMoveAudit.setDescription("Messages deletion Failed!!");
				}
				businessMoveAuditRepository.save(businessMoveAudit);
			}
		}
	}
	
	
	

	private boolean deleteContactDocumentForBusinessMigration(Map<String, Object> dataModelMap, MessagesDeleteRequest messagesDeleteRequest) {
		dataModelMap.put("size", "10000");
		ESRequest esRequest = new ESRequest.Builder(new ESRequest())
				.addRoutingId(messagesDeleteRequest.getRoutingId())
				.addTemplateAndDataModel(Constants.Elastic.DELETE_CONTACT_BY_QUERY_BY_MC_ID, dataModelMap)
				.addIndex(Constants.Elastic.CONTACT_INDEX)
				.build();
		return elasticSearchService.deleteByQuery(esRequest);
	}
	private boolean deleteMessageDocumentForBusinessMigration(Map<String, Object> dataModelMap, MessagesDeleteRequest messagesDeleteRequest) {
			dataModelMap.put("size", "10000");
			ESRequest esRequest = new ESRequest.Builder(new ESRequest())
					.addRoutingId(messagesDeleteRequest.getRoutingId())
					.addTemplateAndDataModel(Constants.Elastic.DELETE_MESSAGES_BY_MC_ID, dataModelMap)
					.addIndex(Constants.Elastic.MESSAGE_INDEX)
					.build();
		return elasticSearchService.deleteByQuery(esRequest);
	}
	
    private void updateCustomerSentimentsInESMessages(CustomerSentimentUpdateEvent customerSentimentUpdateEvent,
            Integer accountId) {
        log.info("updating sentiments in message document for customer Ids: {}",
                customerSentimentUpdateEvent.getCids());
        List<Integer> mcIds = messengerContactService
                .getContactIdsForCustomerIds(customerSentimentUpdateEvent.getCids()).stream()
                .map(MessengerContact::getId).collect(Collectors.toList());
        log.info("updating sentiments in message document for mcIds: {}",
                mcIds);
        Map<String, Object> filterData = new HashMap<>();
        filterData.put("excludedSources",
                ControllerUtil.toCommaSeparatedString(Constants.EXCLUDED_SOURCE_IN_UPDATE_FILTERS));
        filterData.put("excludedMessageTypes", JSONUtils.toJSON(Constants.EXCLUDED_MESSAGE_TYPE_IN_UPDATE_FILTERS));
        Map<String, Object> data = new HashMap<>();
        mcIds.stream().forEach(mcId -> {
            filterData.put("mcId", mcId);
            data.put("data", filterData);
            ESUpdateByQueryRequest.Builder builder = new ESUpdateByQueryRequest.Builder(new ESUpdateByQueryRequest());
            builder.index(Constants.Elastic.MESSAGE_INDEX)
                    .queryTemplateFile(Constants.Elastic.GET_MESSAGES_FOR_UPDATING_CUSTOMER_FILTERS)
                    .freeMarkerDataModel(data).routingId(accountId);
            Map<String, Object> scriptData = new HashMap<>();
            String script = "ctx._source.experienceScore=params.experienceScore;ctx._source.lastUpdateDate=params.lastUpdateDate;ctx._source.u_time=params.lastUpdateDate";

            if ("survey".equalsIgnoreCase(customerSentimentUpdateEvent.getEventSource())
                    || "survey".equals(customerSentimentUpdateEvent.getSource())) {
                script += ";ctx._source.surveyScore=params.experienceScore";
            }
            scriptData.put("inline", script);

            builder.scriptParam(scriptData);
            Map<String, Object> params = new HashMap<>();
            params.put("experienceScore", customerSentimentUpdateEvent.getSentimentScore());
            params.put("lastUpdateDate", (new Date()).getTime());

            builder.params(params);

            boolean updateByQueryResponse = elasticSearchService.updateByQueryWithRefresh(builder.build(), true);
            if (!updateByQueryResponse) {
                log.info("Customer sentiment messages updation failed for mcId:{}", mcId);
            }
            log.info("Customer sentiment messages updation success for mcId:{}", mcId);
        });
    }

	@Override
	public void handleFiltersForDeleteTeamEvent(DeleteTeamEvent event) {
		smartInboxFilterService.handleDeleteTeamEvent(event);	
	}

	@Override
	public void handleFiltersForUpdateTeamUserEvent(UpdateTeamUserEvent event) {
		smartInboxFilterService.handlerUpdateTeamUserEvent(event);
	}

	@Override
	public void handleFiltersForUserEvent(UserEvent event) {
		smartInboxFilterService.handleUserEventType(event);
	}
	
	@Override
	public void updateSocialChannelMediaUrl(UpdateBirdeyeCdnSocialAttachmentsDto event){
		log.info("Request received to update social channel attachment url to birdeye cdn Url {}",event);
		if(Objects.nonNull(event.getBusinessId()) && Objects.nonNull(event.getMessageId()) && StringUtils.isNotEmpty(event.getChannel())
				&& CollectionUtils.isNotEmpty(event.getAttachments())){
			BusinessDTO businessDTO=businessService.getBusinessDTO(event.getBusinessId());
			Integer routeId=businessDTO.getAccountId();
			MessageDocument messageDocument=getMessageById(event.getMessageId(),routeId);
			List<MessageDocument.MediaFile> currentAttachments=messageDocument.getMediaFiles();
			if(CollectionUtils.isNotEmpty(currentAttachments)){
				for(MediaFileDto eventAttachment: event.getAttachments()){
					String eventAUrl=eventAttachment.getA_url();
					String eventCdnUrl=eventAttachment.getCdn_url();
					for(MessageDocument.MediaFile currentAttachment: currentAttachments){
						if(Objects.equals(eventAUrl,currentAttachment.getA_url())){
							currentAttachment.setA_url(eventCdnUrl);
						}
					}
				}
				ESUpsertRequest<MessageDocument> esUpsertRequest=ESUpsertRequest.<MessageDocument>builder()
						.document(messageDocument).id(event.getMessageId()).refreshPolicy(WriteRequest.RefreshPolicy.NONE).index(Constants.Elastic.MESSAGE_INDEX)
						.routingId(String.valueOf(routeId)).upsert(true).build();
				elasticSearchService.upsertESDocument(esUpsertRequest);
			}
		}
			
	}

	private MessageDocument getMessageById(String messageId,Integer accountId){
		Map<String,Object> dataModel=new HashMap<>();
		dataModel.put("_id",messageId);
		ESRequest esRequest=new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
				.addRoutingId(accountId)
				.addTemplateAndDataModel(Constants.Elastic.MESSAGE_BY_ID_V2,dataModel)
				.build();
		List<MessageDocument> messageDocumentFromEs = elasticSearchService.searchByQuery(esRequest,MessageDocument.class);
		if(CollectionUtils.isNotEmpty(messageDocumentFromEs)){
			return messageDocumentFromEs.get(0);
		}
		return null;
	}
	
	private MessageResponse modifyResponseForIncompatibleMobileVersion(MessageResponse response,MessageDTO messageDTO) throws CloneNotSupportedException{
		SortedSet<MessageResponse.Message> removeMessages = new TreeSet<>();
		SortedSet<MessageResponse.Message> splitMessages = new TreeSet<>();
		for(MessageResponse.Message message : response.getMessages()){
			if(MessageDocument.CommunicationDirection.SEND.equals(message.getDirection()) && CollectionUtils.isNotEmpty(message.getMediaFiles())){
				int mediaFileCounter = 1;
				List<MessageDocument.MediaFile> mediaToRemove = new ArrayList<>();
				for (MessageDocument.MediaFile mediaFile : message.getMediaFiles()) {
					if(MessageDocument.CommunicationDirection.SEND.equals(message.getDirection())
							&& StringUtils.isNotBlank(mediaFile.getA_ext()) && Constants.SUPPORTED_VIDEO_FORMAT.contains(mediaFile.getA_ext())){
						mediaToRemove.add(mediaFile);
					}
				}
				message.getMediaFiles().removeAll(mediaToRemove);
				if(filterVideoMessagesForMobile(message)){
					removeMessages.add(message);
				}
				for (MessageDocument.MediaFile mediaFile : message.getMediaFiles()) {
					MessageResponse.Message tempMessage = message.clone();
					tempMessage.setId(tempMessage.getId().concat("_M" + mediaFileCounter));
					tempMessage.setMediaFiles(Collections.singletonList(mediaFile));
					tempMessage.setStatus(message.getStatus());
					if (mediaFileCounter<message.getMediaFiles().size()) {
						tempMessage.setContent("");
					}
					splitMessages.add(tempMessage);
					removeMessages.add(message);
					mediaFileCounter++;
				}
			}
		}
		response.getMessages().addAll(splitMessages);
		response.getMessages().removeAll(removeMessages);
		return response;
	}

	private boolean filterVideoMessagesForMobile(MessageResponse.Message message){
		if(MessageDocument.MessageType.CHAT.equals(message.getMessageType()) && CollectionUtils.isEmpty(message.getMediaFiles())
				&& StringUtils.isBlank(message.getContent())){
			return true;
		}
		return false;
	}
}
