package com.birdeye.messenger.service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.birdeye.messenger.dao.entity.BusinessChatEnabledLocation;
import com.birdeye.messenger.dao.entity.BusinessChatLocationHierarchy;
import com.birdeye.messenger.dao.entity.BusinessChatWidgetConfig;
import com.birdeye.messenger.dao.entity.BusinessChatWidgetUserProfile;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.BusinessWebchatWidgetDetails;
import com.birdeye.messenger.sro.BusinessLocationInfo;
import com.birdeye.messenger.sro.BusinessWebChatConfigurationRequest;


public interface BusinessChatWidgetConfigService {

    Optional<BusinessChatWidgetConfig> saveOrUpdateBusinessChatWidgetConfig(BusinessWebChatConfigurationRequest businessWebChatConfigurationRequest);

    String getWidgetName(String widgetName,Long businessId,Integer enterpriseId,Integer id);

    BusinessChatWidgetConfig getDefaultBusinessChatWidgetConfig();

    List<BusinessChatWidgetConfig> findwidgetConfigsByEnterpriseId(Integer enterpriseId);

    boolean deleteById(Integer id);

    boolean enableorDisableBusinessChatWidgetConfig(Integer id, Integer value);
    boolean enableorDisableBusinessChatWidgetConfigs(List<Integer> ids, Integer value);

    BusinessChatWidgetConfig getBusinessChatWidgetConfig(BusinessDTO business);

    BusinessChatWidgetConfig getWebChatConfigByWidgetId(Integer widgetId);

	BusinessWebchatWidgetDetails getDefaultChatWidgetDetails();

	List<BusinessChatWidgetUserProfile> getUserProfileByChatWidgetId(Integer widgetId);

	List<BusinessLocationInfo> getBusinessLocationsData(Integer chatConfigId, BusinessDTO businessDTO);

	List<BusinessChatWidgetConfig> getWebChatConfigByBusinessNumber(Long businessNumber);

    List<BusinessChatEnabledLocation> findByBusinessNumber(Long businessNumber);

	void deleteBusinessChatenabledLocationsById(Integer id);

   List<BusinessDTO> getAllBusinessesInHierarchy(BusinessDTO businessDTO);

    BusinessChatWidgetConfig saveDefaultWidget(BusinessChatWidgetConfig businessChatWidgetConfig);

    BusinessChatWidgetConfig getWebchatConfigBusinessWebsite(Long businessId);
    BusinessChatWidgetConfig getWebchatConfigMicrosite(BusinessDTO businessDTO);

    List<Integer> getTeamIdsByWidgetConfigId(Integer widgetConfigId);
    List<Integer> getTeamIdsByWidgetConfigIdOrdered(Integer widgetConfigId);

    List<Integer> deleteTeamByTeamId(Integer teamId);

    List<Integer> getEnabledWidgetIds();

    void  updateBusinessChatWidgetConfig(BusinessChatWidgetConfig businessChatWidgetConfig);

    List<Integer> findWidgetsByStatusUpdatedOn();

	BusinessChatWidgetConfig getBusinessChatWidgetConfig(Integer widgetId);
	List<BusinessChatLocationHierarchy> getLocationHierarchy(Integer widgetId);

	List<BusinessChatWidgetConfig> getAllWidgets();

    List<BusinessLocationInfo> getBusinessLocationsDataInternal(Integer chatConfigId,List<Long> chatEnabledLocationIds, List<BusinessDTO> childBusinesses);
    Map<Integer,List<Long>> getChatEnabledLocationIds(List<Integer> chatConfigIds);
    boolean  disableBusinessChatWidgetConfigsAndLocations(List<Integer> widgetIds);
    List<BusinessChatWidgetConfig> getWebChatConfigByWidgetIds(List<Integer> widgetIds);

    Map<Integer, BusinessChatWidgetConfig> getWebChatConfigMap(List<Integer> widgetIds);

    void saveWebChatConfig(List<BusinessChatWidgetConfig> businessChatWidgetConfigs);

}
