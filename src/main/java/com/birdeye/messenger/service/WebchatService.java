package com.birdeye.messenger.service;

import java.util.List;

import com.birdeye.messenger.dto.*;
import org.springframework.data.domain.Pageable;

import com.birdeye.messenger.dao.entity.BusinessChatWidgetConfig;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.sro.BusinessDeactivationRequest;
import com.birdeye.messenger.sro.BusinessWebChatConfigurationRequest;
import com.birdeye.messenger.sro.CreateDefaultConfigRequest;
import com.birdeye.messenger.sro.DeleteTeamEvent;
import com.birdeye.messenger.sro.GetLocationsResponse;
import com.birdeye.messenger.sro.MultiLocationWidgetRequest;
import com.birdeye.messenger.sro.RefreshInstallationStatusResponse;
import com.birdeye.messenger.sro.SaveWebchatResponse;
import com.birdeye.messenger.sro.UpdateEmailMandatoryRequest;
import com.birdeye.messenger.sro.WebChatConfigurationGenericRequest;
import com.birdeye.messenger.sro.WebChatWidgetDefaultConfiguration;
import com.birdeye.messenger.sro.WebchatConfigFilter;
import com.birdeye.messenger.sro.WebchatCustomLocationNameRequest;
import com.birdeye.messenger.sro.WebchatWidgetTeamsOrderingRequest;
import com.birdeye.messenger.sro.WidgetCustomFieldRequest;

public interface WebchatService {

	/**
	 * 
	 * @param request
	 * @throws MessengerException
	 */
	public Boolean sendWebchatSMS(WebchatMessageDTO request) throws MessengerException;
	List<GetLocationsResponse> getLocationsByEnterpriseId(Integer enterpriseId);

	WebChatWidgetDefaultConfiguration getDefaultConfig(Integer locationId);

	SaveWebchatResponse saveWidgetConfig(BusinessWebChatConfigurationRequest businessWebChatConfigurationRequest);

	public BusinessWebchatWidgetList getWebchatWidgetList(Integer page, Integer size, Integer sortBy, Integer sortOrder,
			String searchText, UserLoginMessage userMessage);

	boolean webChatConfigOperation(WebChatConfigurationGenericRequest request);
	
	WebChatWidgetDefaultConfiguration getBusinessWebChatConfig(WebchatConfigFilter configFilter);
	
	WebChatWidgetDefaultConfiguration getWebChatConfigForWidgetId(Long businessId, Integer widgetId, BusinessDTO businessDTO);


	void 	webChatConfigAction(BusinessDeactivationRequest request);
	boolean createDefaultConfig(CreateDefaultConfigRequest request);
	void addLocationLevelWidgetRequests (LocationLevelRequest request);
	boolean checkIfAlreadyCreated(Long businessNumber);
	String replaceTextWithBusinessNamePrePopulated(String replacetextValue, BusinessDTO business);
	void deleteTeamFromWebchat(DeleteTeamEvent deleteTeamEvent);
	TeamDto validateTeamId(Integer teamId);
	void assignConversationToTeam(TeamDto teamDto,Integer mcId,BusinessDTO businessDTO);
	WebChatWidgetDefaultConfiguration getWebsiteWebchatConfig(Long externalId, boolean isMicrosite);

    void updateInstallationStatus(Pageable pageable);
	void requestCrawlerToCheckInstallationStatus(Integer widgetId, List<String> websites, String type, KafkaTopicEnum kafkaTopicEnum);
	RefreshInstallationStatusResponse refreshWidgetStatus(Integer widgetId);
	
	public void sendAutoReplyToCustomerForWebchatSMS(Integer widgetConfigId, BusinessDTO businessDto,
			CustomerDTO customer, boolean sendLivechatTextMessage);

	BusinessWebchatWidgetDetails saveWidgetConfig(MultiLocationWidgetRequest multiLocationWidgetRequest);
	public void updateWidgetEmailMandatory(UpdateEmailMandatoryRequest emailMandatoryRequest, boolean emailMandatory);
	public void createWebchatCustomLocationName(WebchatCustomLocationNameRequest request);
	void addWebchatCustomFields(WidgetCustomFieldRequest request);
	Boolean isCustomFieldExist(Integer fieldId);

	List<BusinessChatWidgetConfig> getBusinessChatWidgetConfigForBizApp(BizAppChatWidgetConfigRequest request);

	WebChatConfigForExternalIdResponse getBusinessChatWidgetConfigByExternalId(Long externalId);

    void getAndUpdateWidgetInstallationStatus();
	public void webChatWidgetTeamsOrdering(WebchatWidgetTeamsOrderingRequest request);
	
    void assignConversationToTeamWithDBAndESCall(TeamDto teamDto, Integer mcId, BusinessDTO businessDTO,
            boolean callDBAndES,MessengerContact messengerContact);

	BusinessWebChatWidgetConfiguration getWebsiteWebchatConfigNew(String businessId, String apiKey,boolean isMicroSite,String activationStatus, String version) throws Exception;
	
	Boolean isReceivedDuringBusinessHours(Long businessNumber);
}
