package com.birdeye.messenger.service.leadgenagent.impl;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.CustomChannel;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.agent.AgentRule;
import com.birdeye.messenger.dao.entity.agent.RuleAction;
import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.dto.leadgenagent.GetPromptBasedReplyRequestDTO;
import com.birdeye.messenger.dto.leadgenagent.ResponseGeneratorToolRequestDTO;
import com.birdeye.messenger.dto.leadgenagent.ResponseGeneratorToolResponseDTO;
import com.birdeye.messenger.enums.ConditionField;
import com.birdeye.messenger.enums.ResponseGenerationActionsTypeEnum;
import com.birdeye.messenger.enums.RuleActionType;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ChatbotService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.NLPService;
import com.birdeye.messenger.service.AgentRuleService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.SmsService;
import com.birdeye.messenger.service.leadgenagent.ResponseGeneratorToolService;
import com.birdeye.messenger.service.leadgenagent.ResponseOptimiserToolService;
import com.birdeye.messenger.sro.BusinessTimingDTO;
import com.birdeye.messenger.util.BusinessHoursUtility;
import com.birdeye.messenger.util.ControllerUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mvel2.MVEL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class ResponseGeneratorToolServiceImpl implements ResponseGeneratorToolService {

    @Autowired
    private AgentRuleService agentRuleService;

    @Autowired
    private ChatbotService chatbotService;

    @Autowired
    private MessengerContactService messengerContactService;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private NLPService nlpService;

    @Autowired
    private ResponseOptimiserToolService responseOptimiserToolService;

    @Autowired
    private SmsService smsService;

    @Autowired
    private ContactService contactService;

    @Override
    public ResponseGeneratorToolResponseDTO generateResponseBasedOnIntentAndSentiment(ResponseGeneratorToolRequestDTO request) {
        log.info("Generating response for agentId: {}, intent: {}, sentiment: {}, metadata: {}",
                request.getAgentId(), request.getIntent(), request.getSentiment(), request.getMetaData());

        List<AgentRule> rules = agentRuleService.getRuleExpressionByAgentId(request.getAgentId());
        ResponseGeneratorToolResponseDTO response = new ResponseGeneratorToolResponseDTO();

        if (CollectionUtils.isNotEmpty(rules)) {
            // Create a fact map for MVEL to evaluate
            Map<String, Object> facts = new HashMap<>();
            if(StringUtils.isNotBlank(request.getIntent())){
                facts.put(ConditionField.INTENT.getValue(), request.getIntent().toLowerCase());
            }
            if(StringUtils.isNotBlank(request.getSentiment())){
                facts.put(ConditionField.SENTIMENT.getValue(), request.getSentiment().toLowerCase());
            }

            // Also allow action to modify the response DTO directly
            facts.put("response", response);

            for (AgentRule rule : rules) {
                try {
                    // Evaluate the rule condition
                    Boolean result = (Boolean) MVEL.eval(rule.getRuleExpression(), facts);

                    if (Boolean.TRUE.equals(result)) {
                        log.info("Rule matched: {}", rule.getRuleDescription());

                        // Apply all actions for the rule
                        for (RuleAction action : rule.getActions()) {
                            response = resolveRuleActions(action, request);
                        }
                        break;
                    }

                } catch (Exception e) {
                    log.error("Error evaluating rule with ID: {} - {}", rule.getId(), e.getMessage(), e);
                }
            }
        }

        return response;
    }

    private ResponseGeneratorToolResponseDTO resolveRuleActions(RuleAction ruleAction,ResponseGeneratorToolRequestDTO request){
        ResponseGeneratorToolResponseDTO response = new ResponseGeneratorToolResponseDTO();
        if(Objects.nonNull(request.getMetaData()) && Objects.nonNull(request.getMetaData().getAccountId())){
            response.setConfig(responseOptimiserToolService.getResponseOptimizationChannelBased(request.getAgentId(), request.getChannel(), request.getMetaData().getAccountId()));
        }
        if(Objects.nonNull(request.getMetaData())&& Objects.nonNull(request.getMetaData().getMcId())) {
            response.setMcId(request.getMetaData().getMcId());
        }
        BusinessTimingDTO businessTimingDTO = businessService.getBusinessTimings(request.getMetaData().getBusinessId(), true);
        BusinessDTO businessDTO = businessService.getBusinessDTO(request.getMetaData().getBusinessId());
        CustomerDTO customerDTO = null;
        if(Objects.nonNull(request.getMetaData())&& Objects.nonNull(request.getMetaData().getCustomerId())) {
            customerDTO = contactService.findById(request.getMetaData().getCustomerId());
        }
        RuleActionType actionType = ruleAction.getType();
        switch (actionType){
            case CUSTOM_MESSAGE:
                response.setMessageBody(replaceTokens(businessDTO,ruleAction.getMessage(),customerDTO,businessTimingDTO));
                response.setResponseType(ResponseGenerationActionsTypeEnum.CUSTOM_MESSAGE.getValue());
                break;
            case AI_PROMPT:
                response.setMessageBody(replaceTokens(businessDTO,getMessageBasedOnAIPrompt(request,ruleAction),customerDTO,businessTimingDTO));
                response.setResponseType(ResponseGenerationActionsTypeEnum.AI_PROMPT.getValue());
                break;
            case KNOWLEDGE_BASE:
                response.setMessageBody(replaceTokens(businessDTO,getMessageBasedOnKnowledgeBase(request,businessTimingDTO),customerDTO,businessTimingDTO));
                response.setResponseType(ResponseGenerationActionsTypeEnum.AI_ANSWER_USING_KNOWLEDGE_BASE.getValue());
                break;
        }
        return response;
    }

    private String getMessageBasedOnAIPrompt(ResponseGeneratorToolRequestDTO request, RuleAction ruleAction) {
        ResponseGeneratorToolRequestDTO.MetaData metaData = request.getMetaData();
        MessengerContact messengerContact = messengerContactService.findById(request.getMetaData().getMcId());
        GetPromptBasedReplyRequestDTO getPromptBasedReplyRequestDTO = new GetPromptBasedReplyRequestDTO();
        getPromptBasedReplyRequestDTO.setIntent(request.getIntent());
        getPromptBasedReplyRequestDTO.setSentiment(request.getSentiment());
        getPromptBasedReplyRequestDTO.setMessage(request.getMessage_body());
        List<Map<String, String>> chatHistory = chatbotService.getHistoryMessageForAI(metaData.getBusinessId(), metaData.getAccountId(), messengerContact.getCustomerId(), metaData.getMcId());
        getPromptBasedReplyRequestDTO.setChatHistory(chatHistory);
        getPromptBasedReplyRequestDTO.setActions(Collections.singletonList(
                new GetPromptBasedReplyRequestDTO.Action("Reply with","AI prompt " , ruleAction.getMessage())
        ));

        BusinessDTO businessDTO = businessService.getBusinessDTO(metaData.getBusinessId());
        getPromptBasedReplyRequestDTO.setMeta(new GetPromptBasedReplyRequestDTO.Metadata(
                metaData.getBusinessId(), metaData.getAccountId(), metaData.getUserId(), metaData.getMcId(),
                metaData.getMsgId(), metaData.getRequestId(), businessDTO.getEnterpriseName(), businessDTO.getBusinessName(), businessDTO.getSmsPhoneNumber(), businessDTO.getBirdEyeEmailId()
        ));
        String promptBasedReply = nlpService.getAIReplyUsingPrompt(getPromptBasedReplyRequestDTO);

        return promptBasedReply;
    }

    private String getMessageBasedOnKnowledgeBase(ResponseGeneratorToolRequestDTO request, BusinessTimingDTO businessTimingDTO) {
        ResponseGeneratorToolRequestDTO.MetaData metaData = request.getMetaData();
        MessengerContact messengerContact = messengerContactService.findById(request.getMetaData().getMcId());
        Boolean isReceivedDuringBusinessHour = null;
        if (businessTimingDTO != null) {
            isReceivedDuringBusinessHour = BusinessHoursUtility.isReceivedDuringBusinessHr(businessTimingDTO,
                    new Date());
        }
        if(Objects.nonNull(messengerContact)){
            return chatbotService.getAIReplyUsingKnowledgeBase(metaData.getBusinessId(), metaData.getAccountId(), request.getMessage_body(), getQueryChannel(request.getChannel()), messengerContact.getCustomerId(), metaData.getMcId(), isReceivedDuringBusinessHour, new LivechatAnonymousConversationDataDto());
        }
        return null;
    }

    private static ChatbotService.QueryChannel getQueryChannel(Integer sourceId) {
            Source source = Source.getValue(sourceId);
            switch (source) {
                case LIVE_CHAT_RECEIVE:
                    return ChatbotService.QueryChannel.LIVECHAT;
                case SMS:
                    return ChatbotService.QueryChannel.SMS;
                case CONTACT_US:
                    return ChatbotService.QueryChannel.CONTACT_US;
                case FACEBOOK:
                    return ChatbotService.QueryChannel.FACEBOOK;
                case INSTAGRAM:
                    return ChatbotService.QueryChannel.INSTAGRAM;
                case GOOGLE:
                    return ChatbotService.QueryChannel.GOOGLE;
                case TWITTER:
                    return ChatbotService.QueryChannel.TWITTER;
                case EMAIL:
                    return ChatbotService.QueryChannel.EMAIL;
                case APPLE:
                    return ChatbotService.QueryChannel.APPLE;
                case WHATSAPP:
                    return ChatbotService.QueryChannel.WHATSAPP;
                case VOICE_CALL:
                    return ChatbotService.QueryChannel.VOICE_CALL;
                default:
                    break;
            }
        // Set default channel
        return ChatbotService.QueryChannel.LIVECHAT;
    }


    @Override
    public String customGreetingMessageFromResponseGeneratorConfig(String agentId) {
        ResponseGeneratorToolResponseDTO responseDTO = generateResponseBasedOnIntentAndSentiment(ResponseGeneratorToolRequestDTO.builder()
                .agentId(agentId)
                .intent("Conversation starters")
                .build());
        if (responseDTO != null && responseDTO.getMessageBody() != null) {
            return responseDTO.getMessageBody();
        }
        return null;
    }

    private String replaceTokens(BusinessDTO businessDTO,String message, CustomerDTO customerDTO, BusinessTimingDTO businessTimingDTO){
        String businessName = StringUtils.isNotBlank(businessDTO.getBusinessAlias())
                ? businessDTO.getBusinessAlias()
                : businessDTO.getBusinessName();
        String phoneNumber = smsService.getFormattedBusinessNumber(businessDTO.getBusinessId(),
                businessDTO.getPhoneNumber());
        String textingNumber = smsService.getFormattedBusinessNumber(businessDTO.getBusinessId());

        String msgBody = replaceTextWithBusinessNamePrePopulated(message, businessName);
        PublicDataBusinessDTO publicBusinessDto = null;
        if (msgBody.contains(Constants.BUSINESS_SERVICES) || msgBody.contains(Constants.BUSINESS_PAYMENT_OPTIONS)
                || msgBody.contains(Constants.BUSINESS_LANGUAGES) || msgBody.contains(Constants.BUSINESS_SERVICES_CAP)
                || msgBody.contains(Constants.BUSINESS_PAYMENT_OPTIONS_CAP) || msgBody.contains(Constants.BUSINESS_LANGUAGES_CAP)){
            publicBusinessDto = businessService.getPublicBusinessData(businessDTO.getBusinessId());
        }
        BusinessLocationCustomFieldsTokensDto customFields = businessService.getBusinessCustomFieldsAndTokenByBusinessID(businessDTO.getBusinessId());
        BusinessProfileData businessProfileData = businessService.getBusinessProfileData(businessDTO.getBusinessId());
        String formattedBody = ControllerUtil.replaceTokens(businessDTO, customerDTO.getFirstName(),
                msgBody, phoneNumber, textingNumber, businessTimingDTO, publicBusinessDto,businessProfileData,customFields);
        return formattedBody;
    }

    public String replaceTextWithBusinessNamePrePopulated(String replacetextValue, String businessName) {
        if (StringUtils.isBlank(replacetextValue)
                || !StringUtils.contains(replacetextValue, "[BIZ_NAME]") || StringUtils.isEmpty(businessName)) {
            return replacetextValue;
        }
        if (StringUtils.isBlank(businessName)) {
            businessName = StringUtils.EMPTY;
        }
        if (replacetextValue.contains("[BIZ_NAME]")) {
            replacetextValue = replacetextValue.replace("[BIZ_NAME]", businessName);
        }
        return replacetextValue;
    }

}
