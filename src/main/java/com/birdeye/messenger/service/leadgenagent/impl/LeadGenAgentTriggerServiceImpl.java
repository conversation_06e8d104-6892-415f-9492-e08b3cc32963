package com.birdeye.messenger.service.leadgenagent.impl;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.LiveChatFetchMessageRequest;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.enums.AgentAdditionalConditionsEnum;
import com.birdeye.messenger.enums.AgentSectionEnum;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.external.dto.leadgenagent.AgentDTO;
import com.birdeye.messenger.external.dto.leadgenagent.AgentTriggerDTO;
import com.birdeye.messenger.external.dto.leadgenagent.TriggerAgentRequestDTO;
import com.birdeye.messenger.external.service.AgentArcService;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ChatbotService;
import com.birdeye.messenger.service.MessageService;
import com.birdeye.messenger.service.leadgenagent.LeadGenAgentTriggerService;
import com.birdeye.messenger.sro.BusinessTimingDTO;
import com.birdeye.messenger.util.BusinessHoursUtility;
import io.micrometer.common.util.StringUtils;
import jakarta.persistence.criteria.CriteriaBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class LeadGenAgentTriggerServiceImpl implements LeadGenAgentTriggerService {

    @Autowired
    AgentArcService agentArcService;

    @Autowired
    public BusinessService businessService;

    @Autowired
    public ChatbotService chatbotService;

    @Autowired
    public MessageService messageService;

    @Override
    public AgentTriggerDTO getAgentTriggerByAgentId(String agentId, Integer accountId) {
        log.debug("getAgentTriggerByAgentId called with agentId: {}", agentId);
        List<String> requiredSections = List.of(AgentSectionEnum.TRIGGERS.getValue());
        AgentDTO agentDTO = agentArcService.getAgentByAgentId(agentId,requiredSections, accountId);
        if(Objects.nonNull(agentDTO)&&Objects.nonNull(agentDTO.getTrigger())){
            return agentDTO.getTrigger();
        }
        return null;
    }

    @Override
    public AgentTriggerDTO getAgentTriggerByAccountId(Integer accountId) {
        log.debug("getAgentTriggerByAccountId called with accountId: {}", accountId);
        // Implementation logic to fetch AgentTriggerDTO by accountId
        return null; // Replace with actual implementation
    }

    @Override
    public Boolean isLeadGenAgentTriggerEnabledWeb(Integer businessId, String messageBody, ContactDocument contactDocument, MessageDocument messageDocument, String agentId, boolean hidden) {
        BusinessDTO business = businessService.getBusinessDTO(businessId);
        return checkConditionsAndTriggerAgent(business, Source.LIVE_CHAT_RECEIVE.getSourceId(), messageBody, messageDocument.getM_id(), contactDocument.getM_c_id(), contactDocument.getC_id(), agentId, hidden);
    }


    @Override
    public Boolean isLeadGenAgentTriggerEnabledAllChannels(MessageDTO messageDTO, String oldLmsgOn, String messageBody) {
        log.debug("isLeadGenAgentTriggerEnabledAllChannels called with messageDTO: {}, oldLmsgOn: {}", messageDTO, oldLmsgOn);
        BusinessDTO business =  messageDTO.getBusinessDTO();
        return checkConditionsAndTriggerAgent(business,messageDTO.getSource(), messageBody, messageDTO.getMessageDocument().getM_id(), messageDTO.getMessengerContact().getId(), messageDTO.getCustomerDTO().getId(), null, false);
    }


    private Boolean checkConditionsAndTriggerAgent(BusinessDTO business, Integer sourceID, String messageBody, String messageId, Integer mcId, Integer customerId, String agentId, boolean hidden){
        //check through core api:
        String customMsgPropertyName="leadgen_enabled_account"+business.getAccountId();
        String tempAgentId = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(customMsgPropertyName, "6891e3be67e2618bbfc4fb2a");
        if(StringUtils.isBlank(agentId)){
            agentId = tempAgentId;
        }
        BusinessTimingDTO businessTimingDTO = businessService.getBusinessTimings(business.getBusinessId(), true);
        Boolean isReceivedDuringBusinessHour = null;
        if (businessTimingDTO != null) {
            isReceivedDuringBusinessHour = BusinessHoursUtility.isReceivedDuringBusinessHr(businessTimingDTO,
                    new Date());
        }
//        List<Map<String, String>> chatHistory = messageService.getLiveChatMessages(new LiveChatFetchMessageRequest(mcId,business.getBusinessNumber(),1,3, MessageDocument.CommunicationDirection.RECEIVE.name(), "agent"));
        List<Map<String, String>> chatHistory = chatbotService.getHistoryMessageForAI(business.getBusinessId(), business.getAccountId(), customerId, mcId);
        TriggerAgentRequestDTO triggerAgentRequestDTO = new TriggerAgentRequestDTO(agentId, business.getAccountId(), business.getBusinessId(), messageBody, isReceivedDuringBusinessHour, messageId, customerId, mcId, messageId, sourceID, chatHistory, hidden,null);
        agentArcService.triggerAgentByAgentId(agentId, business.getAccountId(), triggerAgentRequestDTO);
//        if(StringUtils.isNotBlank(agentId)){
//            AgentTriggerDTO agentTriggerDTO = getAgentTriggerByAgentId(agentId, business.getAccountId());
//            if (Objects.nonNull(agentTriggerDTO) ) {
//                List<Integer> channelIds = agentTriggerDTO.getValue();
//                List<AgentTriggerDTO.ConditionDTO> additionalConditions = agentTriggerDTO.getAdditionalConditions();
//                if(CollectionUtils.isNotEmpty(channelIds) && channelIds.contains(sourceID) && CollectionUtils.isNotEmpty(additionalConditions)){
//                    AgentTriggerDTO.ConditionDTO conditionDTO = additionalConditions.get(0);
//                    BusinessTimingDTO businessTimingDTO = businessService.getBusinessTimings(business.getBusinessId(), true);
//                    Boolean isReceivedDuringBusinessHour = null;
//                    if (businessTimingDTO != null) {
//                        isReceivedDuringBusinessHour = BusinessHoursUtility.isReceivedDuringBusinessHr(businessTimingDTO,
//                                new Date());
//                    }
//                    List<Map<String, String>> chatHistory = chatbotService.getHistoryMessageForAI(business.getBusinessId(), business.getAccountId(), customerId, mcId);
//                    TriggerAgentRequestDTO triggerAgentRequestDTO = new TriggerAgentRequestDTO(agentId, business.getAccountId(), business.getBusinessId(), messageBody, isReceivedDuringBusinessHour, messageId, customerId, mcId, messageId, sourceID, chatHistory);
//                    if(AgentAdditionalConditionsEnum.ANY.getValue().equals(conditionDTO.getValue())){
//                        agentArcService.triggerAgentByAgentId(agentId, business.getAccountId(), triggerAgentRequestDTO);
//                    }else{
//                        if( AgentAdditionalConditionsEnum.INSIDE_BUSINESS_HOURS.getValue().equals(conditionDTO.getValue())
//                                && Boolean.TRUE.equals(isReceivedDuringBusinessHour)) {
//                            agentArcService.triggerAgentByAgentId(agentId, business.getAccountId(), triggerAgentRequestDTO);
//                        } else if (AgentAdditionalConditionsEnum.OUTSIDE_BUSINESS_HOURS.getValue().equals(conditionDTO.getValue())
//                                && Boolean.FALSE.equals(isReceivedDuringBusinessHour)) {
//                            agentArcService.triggerAgentByAgentId(agentId, business.getAccountId(), triggerAgentRequestDTO);
//                        } else {
//                            log.debug("No matching additional condition found for agent trigger: {}", agentTriggerDTO);
//                        }
//                    }
//                    return true;
//                }
//            }
//        } else {
//            log.debug("No LeadGen Agent Trigger found for business accountId: {}", business.getAccountId());
//        }
        return true;
    }
}
