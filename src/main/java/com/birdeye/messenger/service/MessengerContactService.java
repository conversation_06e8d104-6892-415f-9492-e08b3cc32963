package com.birdeye.messenger.service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;

import com.birdeye.messenger.dto.*;
import org.elasticsearch.action.search.SearchResponse;

import com.birdeye.messenger.dao.entity.Email;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.ResponseTimeOutBox;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.ContactDocument.Review;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.MediaFile;
import com.birdeye.messenger.dto.elastic.MessageDocumentTemp;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.sro.ReviewEvent;
import com.birdeye.messenger.sro.SurveyEvent;
import org.springframework.transaction.annotation.Transactional;

public interface MessengerContactService {

        public static final Integer VOICEMAIL_USER = -2;

        public static final String ATTCH_MESSAGE = "you received an attachment";

        MessengerContact findById(Integer id);

        // TODO: Move to MessengerContactHandler
        /**
         * Update messenger contact for voice call Auto reply.
         */
        MessengerData processMessengerContactForVoiceCall(BusinessDTO business, CustomerDTO customer, SmsDTO sms);

        /**
         * 
         * 1. Insert/Update messenger Contact with last message changes
         * 2. Update ES collections for Conversation & Messages
         * 
         * @param business              - Location where message is sent
         * @param customer              - Customer reference
         * @param liveChatMessageObject - SMS reference
         * @return MessageData Documents pushed to ES (conversation & message)
         */
        MessengerData processMessengerContactForWebChat(BusinessDTO business, CustomerDTO customer,
                        LiveChatMessageObject liveChatMessageObject,WebchatMessageDTO request);

        MessengerData processMessengerContactForCampaignSms(BusinessDTO business, UserDTO campaignUser,
                        CustomerDTO customer, SmsDTO sms);

        MessengerContact getOrCreateContactForExistingCustomer(Integer businessId, Integer customerId,
                        Integer accountId);

        MessengerContact getOrCreateContact(Integer businessId, Integer contactId, Integer accountId);

        MessengerContact saveOrUpdateMessengerContact(MessengerContact contact);

        List<MessageDocument> getMessagesFromES(MessangerBaseFilter messengerQueryFilter);

        MessengerData saveOrUpdateMessengerContact(MessengerContact contact, ResponseTimeOutBox log);

        List<ContactDocument> getContactFromES(MessangerBaseFilter messengerQueryFilter);

        ContactDocument getContact(MessengerGlobalFilter filter);

        ContactDocument updateContactOnES(MessengerContact messengerContact, CustomerDTO customerDTO,
                        BusinessDTO businessDTO, MessageTag messageTag, UserDTO userDTO);

        boolean updateContactOnES(Integer messageContactId, ContactDocument contactDocument, Integer routingId,
                        Integer enterpriseId, Integer businessId);

        Integer getLastReceivedMessageSource(Integer contactId, Integer routeId);

        Integer getLastReceivedMessageSource(MessengerContact messengerContact, Integer routeId);

        List<MessengerContact> findAllByUpdatedAtAfter(Date updatedAt);

        MessengerContact findByCustomerId(Integer customerId);

        boolean deleteMessengerContact(Integer id);

        boolean updateContactOnES(Integer messengerContactId, ContactDocument contactFromES, Integer routingId);

        List<MessengerContact> getContactIdsForCustomerIds(List<Integer> customerIds);

        ElasticData getMessageData(MessangerBaseFilter messengerQueryFilter);

        boolean updateContactDocumentOnES(ContactDocument contactDocument, String documentId, Integer routingId);

        MessageDocument andNewMessageOnEs(MessageDocumentDTO messageDocumentDTO,
                        MessengerMediaFileDTO messengerMediaFileDTO, UserDTO userDTO, BusinessDTO businessDTO,
                        MessengerEvent event);

        ElasticData getConversationData(ContactFreeMarkerData data, Integer accountId);

        MessageDocument addNewMessageDocOnES(MessageDocument document, String documentId);

        MessengerData upsertMessengerContact(BusinessDTO business, Integer tag, CustomerDTO customer, String device);

        Entry<MessageDocument, ContactDocument> postLiveChatMessageReceiveActivity(BusinessDTO business, Integer mcId,
                        LiveChatMessageObject liveChatMessageObject,
                        MessengerEvent messengerEvent, String cPhone, MessageTag messageTag, Integer userId,
                        CustomerDTO customerDTO, Boolean updateLastRespondedAt);

        MessengerContact updateLastMessage(MessengerContactDto messengerContactDto);

        boolean updateMessageDocument(MessageDocument messageDocument, String documentId, Integer accountId);

        List<MessengerContact> findMessengerContactByBusinessId(Integer businessId);

        ContactDocument contactDocumentBuilder(MessengerContact messengerContact, CustomerDTO customerDTO,
                        BusinessDTO businessDTO, MessageTag messageTag, UserDTO userDTO);

        MessengerContact getOrCreateMessengerContact(MessengerContactDto messengerContactDto);

        ElasticData getConversationDataForExport(MessengerFilterForExportInbox messengerQueryFilter,
                        boolean isFilterCampaign);

        ElasticData getMessageDataForExport(MessengerFilterForExportInbox messengerFilter);

        MessengerContact findOrCreate(BusinessDTO business, Integer tag, CustomerDTO customer);

        MessageDocument getMessageDocument(MessageDocumentDTO messageDocumentDTO,
                        MessengerMediaFileDTO messengerMediaFileDTO, UserDTO userDTO, BusinessDTO businessDTO,
                        MessengerEvent event);

        boolean upsertContactDocumentOnES(ContactDocument contactDocument, String documentId, Integer routingId);

        MessageDocument addMessageOnES(MessageDocumentDTO messageDocumentDTO,
                        MessengerMediaFileDTO messengerMediaFileDTO, UserDTO userDTO, BusinessDTO businessDTO,
                        MessengerEvent event, List<MediaFile> mediaList);

        List<GetMessengerContactDTO> findByIdIn(List<Integer> ids);

        ElasticData getConversationIdsFromES(ContactFreeMarkerData data, Integer accountId);

        void updateViewedByRead(Integer userId, List<Integer> conversationIds);

        void updateConversationTag(Integer tag, List<Integer> conversationIds);

        void updateViewedByUnread(Integer userId, List<Integer> conversationIds);

        void updateViewedByUnreadForBusiness(Integer businessId, String userId);

        MessengerData processMessengerContactForCampaignEmail(BusinessDTO business, UserDTO campaignUser,
                        CustomerDTO customer, Email email, ConversationDTO conversationDTO);

        public Integer getLastMessageSource(Integer contactId, Integer routeId);

        List<GetMessengerContactDTO> findViewedByByIdIn(List<Integer> conversationIds);

        List<MessageDocumentTemp> getMessagesFromESForBidMigration(MessangerBaseFilter messengerQueryFilter);

        List<Integer> getByDateWhereContactStateIsNull(Date startDate);

        List<Object[]> findFacebookIdsByMcIds(List<Integer> mcIds);

        ElasticData<ContactDocument> getConversationDataWithInnerHits(ContactFreeMarkerData data, Integer accountId);

        ContactDocument getContact(Integer accountId, Integer mcId);

        List<MessengerContact> findByCustomerIdAndBusinessId(Integer customerId, Integer businessId);

        MessageDocument addReviewToMessages(ReviewEvent reviewEvent, Integer routingId, Integer businessId,
                        Integer mcId);

        MessengerContact findByReviewerIdAndBusinessId(Integer reviewerId, Integer businessId);

        MessengerContact createMessengerContact(ReviewEvent reviewEvent);

        MessengerContact createMessengerContactAndMessengerMessage(ReviewEvent reviewEvent);

        List<ContactDocument> getConversationFromES(MessangerBaseFilter messengerQueryFilter);

        void updateMessageOnES(MessageDocument messageDocument, Integer routingId);

        MessengerContact createMessengerContact(MessengerContact oldMessengerContact, Review review);

        Map<Integer, MessengerContact> findByIdsIn(List<Integer> ids);

        MessengerContact saveOrUpdateMessengerContactWithExistingTransaction(MessengerContact contact);

        Optional<ContactDocument> getContactDocument(Integer accountId, Integer mcId);

        MessageDocument addSurveyResponseToMessages(SurveyEvent.After surveyEvent, Integer routingId,
                        Integer businessId, Integer mcId, String surveyName);

        MessengerContact createMessengerContactAndMessengerMessage(SurveyEvent.After surveyEvent, Integer accountId,
                        CustomerDTO customerDTO);

        MessengerContact createContact(Integer customerId, Integer businessId);

        MessengerContact getOrCreateMessengerContactForGoogleContact(String googleConversationId, Integer businessId,
                        Integer customerId);

        MessengerContact getOrCreateMessengerContactForAppleContact(String appleConversationId, Integer businessId,
                        Integer customerId);

        Optional<MessengerContact> getByGoogleConversationIdAndSubaccountId(String gcId, Integer businessId);

        Optional<MessengerContact> getByAppleConversationIdAndSubaccountId(String acId, Integer businessId);

        Integer getLastMessageSource(ContactDocument conversation);

        String getLastMsgCustomChannel(ContactDocument conversation);

        SearchResponse getAllMessageDataForExport(MessengerFilterForExportInbox messengerQueryFilter);

        SearchResponse getAllContactsDataForExport(MessengerFilterForExportInbox messengerQueryFilter);

        MessageDocument andNewMessageOnEs(MessageDocumentDTO messageDocumentDTO,
                        MessengerMediaFileDTO messengerMediaFileDTO, UserDTO userDTO, BusinessDTO businessDTO,
                        MessengerEvent event,
                        boolean isSpam);

        MessengerContact findByGoogleConversationId(String googleConvId);

        MessengerContact createMessengerConversation(Integer businessId);

        void markInActivePreviousAppointmentCards(Integer accountId, Integer messengerContactId, Long appointmentId,
                        Long parentId);

        List<MessengerContact> findMessengerContactsByMcIds(List<Integer> messengerContactIds);

        void saveBulkMessengerContact(List<MessengerContact> messengerContact);

        MessengerContactResponse getFacebookIdsByCustomerId(Integer customerId);

        MessengerContactResponse findMessengerContactResponseByCustomerId(Integer customerId);

        MessengerContact handleMissedCall(MessengerContactDto messengerContactDto);

        MessengerContact findMessengerContactByMcIdAndCustomerId(Integer id, Integer customerId);

        boolean encryptLastMessage(BusinessDTO businessDTO, SmsDTO sms, MessengerContact messengerContact,
                        CustomerDTO customer);

        ContactDocument updateContactOnESWithRefresh(MessengerContact messengerContact, CustomerDTO customerDTO,
                        BusinessDTO businessDTO, MessageTag messageTag, UserDTO userDTO, boolean refresh);

        MessageDocument addNewMessageOnEsWithRefresh(MessageDocumentDTO messageDocumentDTO,
                        MessengerMediaFileDTO messengerMediaFileDTO, UserDTO userDTO, BusinessDTO businessDTO,
                        MessengerEvent event, boolean refresh);

        MessageDocument addNewMessageOnEsWithRefresh(MessageDocumentDTO messageDocumentDTO,
                        MessengerMediaFileDTO messengerMediaFileDTO, UserDTO userDTO, BusinessDTO businessDTO,
                        MessengerEvent event,
                        boolean isSpam, boolean refresh);

        boolean upsertContactDocumentOnESWithRefresh(ContactDocument contactDocument, String documentId,
                        Integer routingId,
                        boolean refresh);

    @Transactional
    MessengerContact createContact(Integer customerId,ConversationBySocialId request);
}