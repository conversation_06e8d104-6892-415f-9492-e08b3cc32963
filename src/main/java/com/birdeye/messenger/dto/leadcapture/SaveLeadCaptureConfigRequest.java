package com.birdeye.messenger.dto.leadcapture;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class SaveLeadCaptureConfigRequest {
    
    private String agentId;
    private Integer accountId;
    private Integer channel;
    private Integer captureLeads;
    private String captureTiming;
    private List<LeadCaptureFieldDto> fields;
}
