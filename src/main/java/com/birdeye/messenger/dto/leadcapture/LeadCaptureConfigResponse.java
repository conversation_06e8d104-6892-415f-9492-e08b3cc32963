package com.birdeye.messenger.dto.leadcapture;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LeadCaptureConfigResponse {
    
    private String agentId;
    private Integer accountId;
    private Integer channel;
    private Integer captureLeads;
    private String captureTiming;
    private List<LeadCaptureFieldDto> fields;
}
