package com.birdeye.messenger.dto.leadcapture;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LeadCaptureFieldDto {
    
    private String name;
    private String label;
    private String type;
    private Integer mandatory;
    private Integer order; // Field order for UI reordering
}
