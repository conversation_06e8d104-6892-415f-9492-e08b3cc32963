package com.birdeye.messenger.dto;

import java.io.Serializable;

import lombok.Data;

/**
 * <AUTHOR>
 *
 */
@Data
public class Record implements Serializable{
	private static final long serialVersionUID = 1L;
	
	public String body;
	public String channel;
	public String channel_identifier;
	public String contact_name;
	public String date;
	public String delivery_status;
	public Integer id;
	public Integer message_uuid;
	public String inbound_or_outbound;
	public Object inbox_name;
	public String location_name;
	public String sender_name;

}