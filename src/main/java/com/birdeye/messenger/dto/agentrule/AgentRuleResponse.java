package com.birdeye.messenger.dto.agentrule;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AgentRuleResponse {
    private Long id;
    private String agentId;
    private Integer accountId;
    private String ruleDescription;
    private List<ConditionDTO> conditions;
    private List<ActionDTO> actions;
    private String conditionLogic;
    private Boolean isDefaultRule;
}
