package com.birdeye.messenger.dto.agentrule;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class SaveAgentRuleRequest {
	
	private Long id;	//for update

	private String agentId;

	private Integer accountId;

	private String ruleDescription;

	private List<ConditionDTO> conditions;

	private String conditionLogic;

	private List<ActionDTO> actions;
}