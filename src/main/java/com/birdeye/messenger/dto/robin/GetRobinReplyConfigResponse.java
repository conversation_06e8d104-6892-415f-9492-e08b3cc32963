package com.birdeye.messenger.dto.robin;

import com.birdeye.messenger.convertor.RobinAutoReplyConfigSerializer;
import com.birdeye.messenger.dao.entity.robin.RobinAutoReplyConfig;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetRobinReplyConfigResponse {

    private Integer businessId;

    @JsonSerialize(contentUsing = RobinAutoReplyConfigSerializer.class) // Use contentUsing to specify the serializer for the list elements
    private List<RobinAutoReplyConfig> robinAutoReplyConfig;
}
