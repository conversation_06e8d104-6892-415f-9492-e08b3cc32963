package com.birdeye.messenger.dto.robin;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RobinAutoReplySetupRequest {
    private List<Integer> businessId;
    private List<String> channel;
    private boolean enableRobin;
    private boolean enableAutoReplyInsideBusinessHours;
    private String autoReplyInsideBusinessHours;
    private boolean enableAutoReplyOutsideBusinessHours;
    private String autoReplyOutsideBusinessHours;
    private boolean allConfig;
}
