package com.birdeye.messenger.dto;

import com.birdeye.messenger.enums.BusinessAccountTypeEnum;
import com.birdeye.messenger.sro.WebChatWidgetDefaultConfiguration;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessWebChatWidgetConfiguration extends WebChatWidgetDefaultConfiguration implements Serializable{

    private static final long serialVersionUID = 6774661247680640389L;

    private Integer accountType = BusinessAccountTypeEnum.DIRECT.getValue();;

    private Integer livechatTimeout = 60; // default to 60s

    private boolean businessOpen;

    private String locale;
    
    
}
