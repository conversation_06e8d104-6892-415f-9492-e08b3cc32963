package com.birdeye.messenger.dto.escalation;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EscalationConfigResponse {
    
    private String agentId;
    private Integer accountId;
    private Integer channel;
    private EscalationConfig duringBusinessHoursConfig;
    private EscalationConfig afterHoursConfig;
}
