package com.birdeye.messenger.dto.escalation;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EscalationStep {
    
    private Integer order;
    private String type; 
    private String message;
    private String title;
    private Integer state;
    private Integer editable;
    private Integer durationSeconds;
}
