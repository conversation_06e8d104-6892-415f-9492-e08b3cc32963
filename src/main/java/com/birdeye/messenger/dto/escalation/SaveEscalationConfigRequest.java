package com.birdeye.messenger.dto.escalation;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class SaveEscalationConfigRequest {
    
    private String agentId;
    private Integer accountId;
    private Integer channel;
    private EscalationConfig duringBusinessHoursConfig;
    private EscalationConfig afterHoursConfig;
}
