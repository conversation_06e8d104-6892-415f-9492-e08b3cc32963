package com.birdeye.messenger.dto;

import java.io.Serializable;

import jakarta.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.checkerframework.checker.units.qual.A;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LiveChatFetchMessageRequest implements Serializable {

	private static final long serialVersionUID = -4344571055901704311L;

	@NotNull(message = "MessengerContactId cannot be empty.")
	Integer messengerContactId;
	@NotNull(message = "Business Number cannot be null.")
	Long businessId; // business Number
	Integer startIndex = 0;
	Integer count = 25;

}
