package com.birdeye.messenger.dto.payment;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Builder;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class PaymentRequestConfig implements Serializable {

    private static final long serialVersionUID = 1L;
    PaymentMethod paymentMethods;
    PartialPaymentConfig partialPayment; 
    Boolean saveCardOnFile;
    
    @Data
    @Builder
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PaymentMethod implements Serializable{
    	private static final long serialVersionUID = 1L;
        PaymentMethodConfig card; 
        PaymentMethodConfig ach;
    }

    @Data
    @Builder
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PaymentMethodConfig implements Serializable{
        private static final long serialVersionUID = 1L;
		Boolean passFee;
        Type feeType; // COMPLETE or FIXED or PERCENTAGE
        Long fixedFee; // in cents
        String percentageFee;
    }

    @Data
    @Builder
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PartialPaymentConfig implements Serializable{
        private static final long serialVersionUID = 1L;
		Type amountType; 
        Long fixedAmount;
        String percentageAmount;
    }

    public enum Type{
        FIXED,
        PERCENTAGE,
        COMPLETE;
    }
}
