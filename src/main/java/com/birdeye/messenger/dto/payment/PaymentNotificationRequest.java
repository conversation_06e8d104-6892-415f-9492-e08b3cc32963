package com.birdeye.messenger.dto.payment;

import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@Setter
@Getter
public class PaymentNotificationRequest extends PaymentEvent implements Serializable {

    Integer customerId;
    @NotNull
    Integer businessId;

    public static PaymentNotificationRequest fromPaymentEvent(PaymentEvent paymentEvent, Integer customerId, Integer businessId) {
        PaymentNotificationRequest req = new PaymentNotificationRequest();
        req.setCustomerId(customerId);
        req.setBusinessId(businessId);
        req.setAmount(paymentEvent.getAmount());
        req.setConversationId(paymentEvent.getConversationId());
        req.setCustomerEmailId(paymentEvent.getCustomerEmailId());
        req.setEventName(paymentEvent.getEventName());
        req.setId(paymentEvent.getId());
        req.setInvoiceNumber(paymentEvent.getInvoiceNumber());
        req.setItemDetail(paymentEvent.getItemDetail());
        req.setParentId(paymentEvent.getParentId());
        req.setReceiptUrl(paymentEvent.getReceiptUrl());
        req.setRefundAmount(paymentEvent.getRefundAmount());
        req.setRefundNote(paymentEvent.getRefundNote());
        req.setTimestamp(paymentEvent.getTimestamp());
        req.setTransactionId(paymentEvent.getTransactionId());
        req.setUserId(paymentEvent.getUserId());
        req.setSubscriptionEventDetails(paymentEvent.getSubscriptionEventDetails());
        req.setMemo(paymentEvent.getMemo());
        return req;
    }

    public static PaymentNotificationRequest fromPaymentEvent(PaymentUiRequest request, Integer customerId, Integer businessId,PaymentEvent.PaymentEventName eventName,Integer mcId,String customerEmailId,CreatePaymentLinkResponse response,Integer userId) {
        PaymentNotificationRequest req = new PaymentNotificationRequest();
        req.setCustomerId(customerId);
        req.setBusinessId(businessId);
        req.setAmount(request.getItems().get(0).amount);
        req.setConversationId(mcId);
        req.setCustomerEmailId(customerEmailId);
        req.setEventName(eventName);
        req.setId(response.getPaymentRequestId());
        req.setInvoiceNumber(request.getInvoiceNumber());
        req.setItemDetail(request.getItems().get(0).itemDesc);
        req.setTimestamp(new Date().getTime());
        req.setTransactionId(response.getTransactionId());
        req.setUserId(userId);
        req.setCurrencyCode(response.getCurrency());
        return req;
    }


    @Override
    public String toString() {
        return "PaymentNotificationRequest{" +
                "customerId=" + customerId +
                ", businessId=" + businessId +
                '}' + "is a " + super.toString();
    }
}
