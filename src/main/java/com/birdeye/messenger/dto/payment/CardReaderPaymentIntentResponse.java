package com.birdeye.messenger.dto.payment;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CardReaderPaymentIntentResponse {

    private String clientSecret;
    private String paymentRequestId;
    private String status;
    private String cardReaderId;
    private String cardReaderLabel;
    private String transactionId;
    private String receiptURL;
    private CurrencyConstant currency;
}
