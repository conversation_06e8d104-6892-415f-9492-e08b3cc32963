package com.birdeye.messenger.dto.payment;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

/**
 * <AUTHOR>
 *
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CRMRefundDetails implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Long refundAmount;
	private String reasonForRefund;
	private String refundDate;
	private String otherReason;
	private String refundType;
	
	
	public CRMRefundDetails() {
	}
	
	public CRMRefundDetails(Long refundAmount, String reasonForRefund, String refundDate, String otherReason,
			String refundType) {
		super();
		this.refundAmount = refundAmount;
		this.reasonForRefund = reasonForRefund;
		this.refundDate = refundDate;
		this.otherReason = otherReason;
		this.refundType = refundType;
	}
}
