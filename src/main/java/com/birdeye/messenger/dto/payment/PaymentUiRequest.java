package com.birdeye.messenger.dto.payment;

import com.birdeye.messenger.enums.PaymentDeviceType;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

import lombok.Data;

@Data
public class PaymentUiRequest {

    // Payment request message body will be part of sendMessageDto
    private List<PaymentItem> items;
    private String invoiceNumber = "N/A"; // default
    private boolean resend = false;
    private String method; // TERMINAL, ONLINE
    private String cardReaderId;
    private String cardReaderLabel;
    
    private String crmName;
	private String crmDocId;
	private Integer crmCustomerId;
	private String extTrackingId;
	private String invoiceDueDate;
	private String tags;
	private Map<String, Object> customFields;
	private String memo;
    private PaymentDeviceType deviceType; // enum
    
    private SubscriptionUiRequest subscriptionRequest;
    private PaymentRequestConfig paymentRequestConfig;
    
	private String paymentMethodId;
}

@Builder
@Data
class SubscriptionUiRequest {

    private SubscriptionInterval interval; //DAY,WEEK,MONTH,YEAR
    private Long intervalFrequency;
    private SubscriptionEnds ends;    //WHEN_CANCELLED,ON,AFTER
    private Long endsOn;
    private Integer endsAfter;

}