package com.birdeye.messenger.dto.payment;

import com.birdeye.messenger.util.SecureString;
import com.birdeye.messenger.util.SecureStringUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import jakarta.validation.constraints.NotNull;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class PaymentEvent {

    //----------------------- common fields for all events --------------------
    @NotNull(message = "timestamp cannot be empty")
    private Long timestamp;
    @NotNull(message = "eventName cannot be empty")
    private PaymentEventName eventName;
    @NotNull(message = "id cannot be empty")
    private String id; // original payment request
    @NotNull(message = "conversationId cannot empty")
    private Integer conversationId;
    //------------------------------------------------------------------------

    private Integer customerId; // not mandatory
    private Double amount;
    
    private String invoiceNumber;
    private String receiptUrl;
    @SecureString
    private String customerEmailId;

    private String parentId; // refund request
    private Double refundAmount;
    private Integer userId;
    private String refundNote;
    private String otherReason;
    private String itemDetail = "N/A";
    private String transactionId = "N/A";
    
    private String resendUrl;
    private String paymentMethod;
    
    private SubscriptionEventDetails subscriptionEventDetails;

    private String memo;
    private String paymentType; // Text to Pay, Card entry, Card reader, Website
    private Double amountPending;
    private Double amountRequested;
    private Double transactionFeePaidByCustomer;

    private String paymentVia;
    private String last4;
    private CurrencyConstant currencyCode;


    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @Builder
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class  SubscriptionEventDetails{
    	private String id;
        private Long dueDate;
        private String paymentVia;
        private String last4;
        private String updatePaymentMethodLink;
        private String intervalMode;
        private Long cancelledDate;
        private Long nextRetryDate; // For Unsuccessful payment
        private Boolean cyclicPayment= Boolean.FALSE;
        private Integer after;
        private Long endDate;
        
    }

    public enum PaymentEventName {
        PAYMENT_COMPLETED, // amount, invoiceNumber, receiptUrl, customerEmailId + common fields
        PAYMENT_INCOMPLETE,
        PAYMENT_FAILED,
        PAYMENT_REQUEST_CANCELLED, // userId + common fields
        PAYMENT_LINK_EXPIRED,

        PAYMENT_PARTIAL_REFUND_COMPLETED, // common fields
        PAYMENT_FULL_REFUND_COMPLETED, // common fields
        PAYMENT_PARTIAL_REFUND_PENDING, // refundAmount, invoiceNumber, receiptUrl, userId, refundNote, destPaymentRequestId + common fields
        PAYMENT_FULL_REFUND_PENDING, // refundAmount, invoiceNumber, receiptUrl, userId, refundNote, destPaymentRequestId + common fields
        PAYMENT_REFUND_FAILED,
        PAYMENT_REFUND_CANCELED,
        PAYMENT_MARK_AS_PAID,
        PAYMENT_REQUEST_CREATED,
        PAYMENT_INITIATED,
        PAYMENT_PROCESSING,
        SUBSCRIPTION_CANCELLED,
        UPCOMING_INVOICE,
        UPDATE_PAYMENT_METHOD,
        INVOICE_FAILED

    }

    @Override
    public String toString() {
        return SecureStringUtil.buildSecureToString(this);
    }
}
