package com.birdeye.messenger.dto.payment;

import java.io.Serializable;

import com.birdeye.messenger.util.SecureString;
import com.birdeye.messenger.util.SecureStringUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 *
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CRMInvoiceMessage implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String crmName;
	private String crmDocId;
	private Integer crmCustomerId;
	private String extTrackingId;
	private Integer businessId;

	private CRMCustomerDetails customerInfo;
	private CRMInvoiceDetails invoiceDetails;
	private CRMRefundDetails refundDetails;
	private CRMCommunicationPreference communicationPreference;
	private String automationUser;

	@Data
	@JsonInclude(JsonInclude.Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	@ToString
	public class CRMCustomerDetails implements Serializable {
		/**
		 * 
		 */
		private static final long serialVersionUID = 1L;
		private String firstName;
		private String lastName;
		@SecureString
		private String email;
		@SecureString
		private String phone;

		public CRMCustomerDetails() {
		}

		public CRMCustomerDetails(String firstName, String lastName, String email, String phone) {
			super();
			this.firstName = firstName;
			this.lastName = lastName;
			this.email = email;
			this.phone = phone;
		}

		@Override
		public String toString() {
			return SecureStringUtil.buildSecureToString(this);
		}

	}
}
