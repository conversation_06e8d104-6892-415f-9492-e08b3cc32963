package com.birdeye.messenger.dto.payment;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreatePaymentLinkResponse {
        String paymentLink;
        String paymentRequestId;
        String transactionId;
        Long linkCreatedAt;
        boolean createdViaTrigger;
        CurrencyConstant currency;
        
        String paymentVia;
    	String lastFourDigits;
    	String status;
    	String receiptUrl;
}
