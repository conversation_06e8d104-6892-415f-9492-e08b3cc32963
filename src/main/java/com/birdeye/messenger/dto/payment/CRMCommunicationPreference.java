package com.birdeye.messenger.dto.payment;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

/**
 * <AUTHOR>
 *
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CRMCommunicationPreference implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private boolean sendEmail;
	private String emailTemplate;
	private boolean sendText;
	private String textTemplate;
	private boolean sendImmediately;
	private int delayInHours;

	public CRMCommunicationPreference() {
	}

	public CRMCommunicationPreference(boolean sendEmail, String emailTemplate, boolean sendText, String textTemplate,
			boolean sendImmediately, int delayInHours) {
		super();
		this.sendEmail = sendEmail;
		this.emailTemplate = emailTemplate;
		this.sendText = sendText;
		this.textTemplate = textTemplate;
		this.sendImmediately = sendImmediately;
		this.delayInHours = delayInHours;
	}
	
}
