package com.birdeye.messenger.dto.payment;

import java.io.Serializable;

import com.birdeye.messenger.util.SecureString;
import com.birdeye.messenger.util.SecureStringUtil;
import jakarta.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PaymentAttributionRequest implements Serializable {


	private static final long serialVersionUID = -3338224042153869433L;

	@NotNull
	private Integer businessId;

	private Integer mcId;

	private Integer customerId;

	private String customerName;
	@SecureString
	private String customerEmail;
	@SecureString
	private String customerPhone;
	private String countryCode;

	@Override
	public String toString() {
		return SecureStringUtil.buildSecureToString(this);
	}
}
