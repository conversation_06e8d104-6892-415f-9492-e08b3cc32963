package com.birdeye.messenger.dto.payment;

import java.io.Serializable;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

/**
 * <AUTHOR>
 *
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CRMInvoiceDetails implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String invoiceNumber;
	private String invoiceItemDesc;
	private Double totalAmount;
	private String invoiceCreatedDate;
	private String invoiceDueDate;
	private String tags;
	private Map<String, Object> customFields;
	private String mediaUrl;

	public CRMInvoiceDetails() {
	}

	public CRMInvoiceDetails(String invoiceNumber, String invoiceItemDesc, Double totalAmount,
			String invoiceCreatedDate, String invoiceDueDate) {
		super();
		this.invoiceNumber = invoiceNumber;
		this.invoiceItemDesc = invoiceItemDesc;
		this.totalAmount = totalAmount;
		this.invoiceCreatedDate = invoiceCreatedDate;
		this.invoiceDueDate = invoiceDueDate;
	}

}
