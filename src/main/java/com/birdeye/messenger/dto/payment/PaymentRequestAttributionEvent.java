package com.birdeye.messenger.dto.payment;

import java.io.Serializable;
import java.util.List;

import com.birdeye.messenger.util.SecureString;
import com.birdeye.messenger.util.SecureStringUtil;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class PaymentRequestAttributionEvent implements Serializable {

	private static final long serialVersionUID = -718270140497863674L;
	
	private List<String> paymentIds;
	private Integer customerId;
	private String customerName;
	@SecureString
	private String customerEmail;
	@SecureString
	private String customerPhone;
	private Integer mcId;
	private Integer userId;
	private String eventType; 
	private Long updatedAt;

	@Override
	public String toString() {
		return SecureStringUtil.buildSecureToString(this);
	}
}
