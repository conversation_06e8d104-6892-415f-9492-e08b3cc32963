package com.birdeye.messenger.dto.payment;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import com.birdeye.messenger.util.SecureString;
import com.birdeye.messenger.util.SecureStringUtil;
import org.springframework.util.CollectionUtils;

import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CustomFieldDto;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.util.MessengerUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Builder;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreatePaymentLinkRequest {

    private Integer accountIdM;

    private Integer businessIdM;

    private Integer contactIdM;

    private String customerName;
    @SecureString
    private String customerPhone;
    @SecureString
    private String customerEmail;
    private String stripeCustomerId;


    private Integer conversationIdM;

    //private Integer messageIdM;

    private String itemDetailsM;

    private Integer amountInMinorUnitM;

    private String currencyCode="USD";

    private String invoiceNumber;

    private Integer initiatedByUserM;
    
    private String crmName;
	private String crmDocId;
	private Integer crmCustomerId;
	private String extTrackingId;
	private String invoiceDueDate;
	private String memo;
	private String tags;
    
	private Map<String, Object> customFields;

    private PaymentRequestType paymentRequestType;
    
    private SubscriptionLinkRequest subscriptionRequest;
    
    private PaymentRequestConfig paymentRequestConfig;
    
    private String paymentMethodId;


    public CreatePaymentLinkRequest(MessageDTO messageDTO,PaymentRequestType paymentRequestType) {
        PaymentUiRequest paymentRequest = messageDTO.getPaymentRequest();
        BusinessDTO businessDTO = messageDTO.getBusinessDTO();
        SendMessageDTO dto = (SendMessageDTO) messageDTO;
        
        this.conversationIdM = messageDTO.getMessengerContact().getId();
        this.invoiceNumber = paymentRequest.getInvoiceNumber();
        this.amountInMinorUnitM = paymentRequest.getItems().get(0).getAmount().intValue();
        this.itemDetailsM = paymentRequest.getItems().get(0).getItemDesc();
        
        this.initiatedByUserM = messageDTO.getUserId();
        this.accountIdM = businessDTO.getAccountId();
        this.businessIdM = businessDTO.getBusinessId();
        this.memo = paymentRequest.getMemo();
        if(dto.getCustomerDTO() != null) {
	        this.contactIdM = messageDTO.getCustomerDTO().getId();
	        this.customerName = MessengerUtil.buildCustomerName(messageDTO.getCustomerDTO());
	        this.customerPhone = messageDTO.getCustomerDTO().getPhone();
	        this.customerEmail = messageDTO.getCustomerDTO().getEmailId();
	        if(!CollectionUtils.isEmpty(messageDTO.getCustomerDTO().getCustomFieldValues())) {
		        Optional<String> stripeCustomerId = messageDTO.getCustomerDTO().getCustomFieldValues().stream()
								        		.filter(cField -> "Payments Customer VID".equals(cField.getFieldName()))
								        		.map(CustomFieldDto::getFieldValue).findAny();
		        if(stripeCustomerId.isPresent()) {
		        	this.stripeCustomerId = stripeCustomerId.get();
		        }
	        }
        }
        if(messageDTO.getPaymentRequest().getCrmName()!=null)
        	this.crmName = messageDTO.getPaymentRequest().getCrmName();
        if(messageDTO.getPaymentRequest().getCrmDocId()!=null)
        	this.crmDocId = messageDTO.getPaymentRequest().getCrmDocId();
        if(messageDTO.getPaymentRequest().getCrmCustomerId()!=null)
        	this.crmCustomerId = messageDTO.getPaymentRequest().getCrmCustomerId();
        if(messageDTO.getPaymentRequest().getExtTrackingId()!=null)
        	this.extTrackingId = messageDTO.getPaymentRequest().getExtTrackingId();
        if(messageDTO.getPaymentRequest().getInvoiceDueDate()!=null)
        	this.invoiceDueDate = messageDTO.getPaymentRequest().getInvoiceDueDate();
        if(messageDTO.getPaymentRequest().getTags()!=null)
        	this.tags = messageDTO.getPaymentRequest().getTags();
        if(messageDTO.getPaymentRequest().getCustomFields()!=null)
        	this.customFields = messageDTO.getPaymentRequest().getCustomFields();
        this.paymentRequestType = paymentRequestType;
        
        SubscriptionUiRequest subscriptionUiRequest=paymentRequest.getSubscriptionRequest();
        if(Objects.nonNull(subscriptionUiRequest)) {
        this.subscriptionRequest=SubscriptionLinkRequest.builder()
        		.interval(subscriptionUiRequest.getInterval())
        		.intervalFrequency(subscriptionUiRequest.getIntervalFrequency())
        		.ends(subscriptionUiRequest.getEnds())
        		.endsOn(subscriptionUiRequest.getEndsOn())
        		.endsAfter(subscriptionUiRequest.getEndsAfter()).build();
        }
              
        this.paymentRequestConfig = paymentRequest.getPaymentRequestConfig();
        
        this.paymentMethodId = paymentRequest.getPaymentMethodId();
    }

    public enum  PaymentRequestType{
        CARD_READER,CARD_ENTRY,TEXT_TO_PAY,CARD_ON_FILE;
    }

    @Override
    public String toString() {
        return SecureStringUtil.buildSecureToString(this);
    }
}

@Data
@Builder
class SubscriptionLinkRequest {

    private SubscriptionInterval interval; //DAY,WEEK,MONTH,YEAR
    private Long intervalFrequency;
    private SubscriptionEnds ends;    //WHEN_CANCELLED,ON,AFTER
    private Long endsOn;
    private Integer endsAfter;
}
