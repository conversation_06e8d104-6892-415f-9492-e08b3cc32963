package com.birdeye.messenger.dto.payment;

import com.birdeye.messenger.enums.PaymentDeviceType;
import com.birdeye.messenger.enums.PaymentStatus;

import lombok.Builder;
import lombok.Data;

@Data
public class PaymentInfo {
	String link;
	String id;
	PaymentStatus status; // INITIATED, COMPLETED, PROCESSING, FAILED, CANCELLED
	Double amount;
	Double amountPending;
	Double refundAmount;
	String refundNote;
	String otherReason;
	String receiptUrl;
	String parentId;
	String invoiceNumber;
	String transactionId; // alias for id - BIRDEYE-93433

	Integer reqCreatedById;
	String reqCreatedByName;
	Long reqCreatedAt;

	Integer reqCancelledById;
	String reqCancelledByName;
	Long reqCancelledAt;
	Boolean resent; // true if same payment request is resent else null.

	String clientSecret;
	String cardReaderId;
	String cardReaderLabel;
	String method;
	String itemDesc;

	PaymentDeviceType deviceType;
	Boolean subscription;

	SubscriptionInfo subscriptionInfo;
	String paymentType; // Text to Pay, Card entry, Card reader, Webforms
	String memo;
	

	private String paymentVia;
    private String lastFourDigit;
    
    private Double transactionFeePaidByCustomer;
    private Double amountRequested;
    private CurrencyConstant currency;
    


	@Data
	@Builder
	public static class SubscriptionInfo {
		private String id;
		private String nextRetryDate;
		private String dueDate;
		private String paymentVia;
		private String lastFourDigit;
	}
}
