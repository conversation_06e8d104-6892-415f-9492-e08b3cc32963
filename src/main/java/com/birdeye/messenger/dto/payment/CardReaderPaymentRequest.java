package com.birdeye.messenger.dto.payment;

import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.enums.PaymentDeviceType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
public class CardReaderPaymentRequest extends CreatePaymentLinkRequest {

    private String cardReaderId;
    private String cardReaderLabel;
    private String memo;
    private PaymentDeviceType deviceType;

    public CardReaderPaymentRequest(MessageDTO messageDTO) {
        super(messageDTO,PaymentRequestType.CARD_READER);
        PaymentUiRequest paymentRequest = messageDTO.getPaymentRequest();
        this.cardReaderId = paymentRequest.getCardReaderId();
        this.cardReaderLabel = paymentRequest.getCardReaderLabel();
        this.memo = paymentRequest.getMemo();
        this.deviceType = messageDTO.getPaymentRequest().getDeviceType();
    }
}
