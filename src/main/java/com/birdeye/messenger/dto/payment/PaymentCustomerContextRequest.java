package com.birdeye.messenger.dto.payment;

import java.util.Map;

import com.birdeye.messenger.util.SecureString;
import com.birdeye.messenger.util.SecureStringUtil;
import lombok.Data;

@Data
public class PaymentCustomerContextRequest {

	private Integer accountId;

	private Integer businessId;
	
	private String customerName;
    @SecureString
    private String customerPhone;
    @SecureString
    private String customerEmail;
    private Map<String, String> additionalParams;
    private String countryCode;

    @Override
    public String toString() {
        return SecureStringUtil.buildSecureToString(this);
    }
}
