package com.birdeye.messenger.dto;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.birdeye.messenger.enums.ContactInfoSourceEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import com.birdeye.messenger.dao.entity.AppleMessage;
import com.birdeye.messenger.dao.entity.Email;
import com.birdeye.messenger.dao.entity.GoogleMessage;
import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dao.entity.MessengerMessage;
import com.birdeye.messenger.dao.entity.secure.messaging.SecureMessage;
import com.birdeye.messenger.dto.apple.chat.RichLinkData;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.Channel;
import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.dto.elastic.MessageDocument.MediaFile;
import com.birdeye.messenger.dto.elastic.MessageDocument.MessageType;
import com.birdeye.messenger.dto.elastic.MessageDocument.NoteState;
import com.birdeye.messenger.dto.elastic.MessageDocument.NoteType;
import com.birdeye.messenger.dto.elastic.MessageDocument.SentThrough;
import com.birdeye.messenger.dto.elastic.MessageDocument.UserDetail;
import com.birdeye.messenger.dto.payment.PaymentInfo;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.ContactState;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.MessengerUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = JsonInclude.Include.NON_EMPTY)
public class MessageDocumentDTO {

	public static final String DATE_FORMAT="yyyy-MM-dd HH:mm:ss";
    private String m_id;
    private String from;
    private String to;
    private String cr_date;
    private String msg_status;
    private String msg_Body;
    private String msg_subject;
    private Integer isEncrypted;
    private Integer mcid;
    private Integer source;
    
    private String updatedAt;
    private MessageType messageType;
    private UserDetail createdBy;
    private CommunicationDirection communicationDirection;
    private SentThrough sentThrough;
    private List<MediaFile> mediaFiles;
    private UserDetail updatedBy;
    private NoteState noteState;
    private NoteType noteType;
    private ActivityType activityType;
    private List<UserDetail> triggeredFor;
    private Channel channel;
    private String customChannel;
    private String suffix="";
	private String extRefUid;
	private Long cr_time;
	private Integer templateId;
    private String pageUrlTrackedByGoogleAnalytics;
    private String clientIp;

	private MessageDocument.CustomerInfo referrer;
	private MessageDocument.CustomerInfo referredLead;

	private PaymentInfo paymentInfo;
	private boolean secureFaq;
	private String version;
	private String locale;

    private Long eventProcessingTimeStamp;
    private Long timeElapsedInProcessing;
	private RichLinkData richLinkData;

    private Boolean spam;
    
    private ContactState customerType;
    private Integer experienceScore;
    private Integer surveyScore;

    private String storyReplyUrl;
    private String storyMentionUrl;
    private Boolean answerFlag;
    
    private List<String> citations;
    private boolean faqFlag;
    private boolean fileFlag;

    private CustomerContactInfo customerContactInfo;
    private ContactInfoSourceEnum customerContactInfoSource;
    
	private String waMsgHeader;
	private String waMsgFooter;



    //TODO: we should have encrypted data available already in SMS.
    public MessageDocumentDTO(SmsDTO smsDTO,Integer messengerContactId) {
        DateFormat df = new SimpleDateFormat(DATE_FORMAT);
        this.m_id = smsDTO.getSmsId().toString();
        this.from = smsDTO.getFromNumber();
        this.to = smsDTO.getToNumber();
        if (!(Objects.isNull(smsDTO.getSentOn()) && Objects.isNull(smsDTO.getCreateDate()))) {
            this.cr_date = df.format((smsDTO.getSentOn() != null && smsDTO.getSentOn().after(smsDTO.getCreateDate()))
                    ? smsDTO.getSentOn()
                    : smsDTO.getCreateDate());
            this.cr_time = smsDTO.getSentOn() != null && smsDTO.getSentOn().after(smsDTO.getCreateDate())
                    ? smsDTO.getSentOn().getTime()
                    : smsDTO.getCreateDate().getTime();
        }
        this.msg_status = StringUtils.isEmpty(smsDTO.getFailureReason()) ? "success" : smsDTO.getFailureReason();
        if(Objects.nonNull(smsDTO.getMessageBodyEncrypted())){
            this.msg_Body = smsDTO.getMessageBodyEncrypted();
            this.setIsEncrypted(smsDTO.getEncrypted());
        }
        else {
            this.msg_Body = smsDTO.getMessageBodyUnencrypted();
            boolean isEncrypted = EncryptionUtil.encryptMessage(this, smsDTO.getEncrypted());
            this.setIsEncrypted(isEncrypted ? 1 : 0);
        }
        this.setSource(smsDTO.getSource());
        this.mcid=messengerContactId;
        this.channel=smsDTO.getChannel();
        this.sentThrough=smsDTO.getSentThrough();
        this.messageType=smsDTO.getMessageType();
        this.communicationDirection=smsDTO.getCommunicationDirection();
        this.pageUrlTrackedByGoogleAnalytics = smsDTO.getPageUrlTrackedByGoogleAnalytics();
        this.spam = smsDTO.getSpam();
    }
    public MessageDocumentDTO(LiveChatMessageObject liveChatMessageDTO,Integer messengerContactId) {
        DateFormat df = new SimpleDateFormat(DATE_FORMAT);
        this.m_id = liveChatMessageDTO.getId().toString();
        this.from = liveChatMessageDTO.getSender();
        this.to = liveChatMessageDTO.getRecepient();
        this.cr_date = df.format((liveChatMessageDTO.getSentOn() != null && liveChatMessageDTO.getSentOn().after(liveChatMessageDTO.getCreateDate())) ? liveChatMessageDTO.getSentOn() : liveChatMessageDTO.getCreateDate());
        this.cr_time =liveChatMessageDTO.getSentOn() != null && liveChatMessageDTO.getSentOn().after(liveChatMessageDTO.getCreateDate()) ? liveChatMessageDTO.getSentOn().getTime() : liveChatMessageDTO.getCreateDate().getTime();
        if(Objects.nonNull(liveChatMessageDTO.getMessageBodyEncrypted())){
            this.msg_Body = liveChatMessageDTO.getMessageBodyEncrypted();
            this.setIsEncrypted(liveChatMessageDTO.getEncrypted());
        }
        else {
            this.msg_Body = liveChatMessageDTO.getMessageBodyUnencrypted();
            boolean isEncrypted = EncryptionUtil.encryptMessage(this, liveChatMessageDTO.getEncrypted());
            this.setIsEncrypted(isEncrypted ? 1 : 0);
        }
        this.msg_status = StringUtils.isEmpty(liveChatMessageDTO.getFailureReason()) ? "success" : liveChatMessageDTO.getFailureReason();
        this.setSource(liveChatMessageDTO.getSource());
        this.mcid=messengerContactId;
        this.channel=liveChatMessageDTO.getChannel();
        this.sentThrough=liveChatMessageDTO.getSentThrough();
        this.messageType=liveChatMessageDTO.getMessageType();
        this.communicationDirection=liveChatMessageDTO.getCommunicationDirection();
        this.pageUrlTrackedByGoogleAnalytics = liveChatMessageDTO.getPageUrlTrackedByGoogleAnalytics();
        this.suffix=MessengerUtil.getMessageTypeSuffix(MessageType.CHAT, liveChatMessageDTO.getSource());
        if(Objects.nonNull(liveChatMessageDTO.getClientIp())) {
        	this.clientIp=liveChatMessageDTO.getClientIp();
        }
        this.spam = liveChatMessageDTO.getSpam();
        this.answerFlag = liveChatMessageDTO.getAnswerFlag();
        if(CollectionUtils.isNotEmpty(liveChatMessageDTO.getCitations())){
            this.citations = liveChatMessageDTO.getCitations();
        }
        if(Objects.nonNull(liveChatMessageDTO.getCustomerContactInfo())){
            this.customerContactInfo = liveChatMessageDTO.getCustomerContactInfo();
        }
        if(Objects.nonNull(liveChatMessageDTO.getCustomerContactInfoSource())){
            this.customerContactInfoSource = liveChatMessageDTO.getCustomerContactInfoSource();
        }
        this.faqFlag = liveChatMessageDTO.isFaqFlag();
        this.fileFlag = liveChatMessageDTO.isFileFlag();
    }
    

    public MessageDocumentDTO(ConversationDTO conversationDTO,Integer customerId) {
        DateFormat df = new SimpleDateFormat(DATE_FORMAT);
		this.m_id = String.valueOf(conversationDTO.getId());
        this.from = conversationDTO.getSender();
        this.to = conversationDTO.getRecipient();
        this.cr_date = df.format((conversationDTO.getSentOn() != null && conversationDTO.getSentOn().after(conversationDTO.getCreateDate())) ? conversationDTO.getSentOn() : conversationDTO.getCreateDate());
		this.cr_time = conversationDTO.getSentOn() != null && conversationDTO.getSentOn().after(conversationDTO.getCreateDate()) ? conversationDTO.getSentOn().getTime() : conversationDTO.getCreateDate().getTime();
        this.msg_status = StringUtils.isEmpty(conversationDTO.getFailureReason()) ? "success" : conversationDTO.getFailureReason();
        this.msg_Body = conversationDTO.getBody();
        this.msg_subject = conversationDTO.getSubject();
		//boolean isEncrypted = EncryptionUtil.encryptMessage(this, conversationDTO.getEncrypted());
        this.setIsEncrypted(conversationDTO.getEncrypted());
        this.mcid=customerId;
        this.noteState=conversationDTO.getNoteState();
        this.setSource(conversationDTO.getSource());
        this.noteType=conversationDTO.getNoteType();
        this.messageType=conversationDTO.getMessageType();
		this.sentThrough=conversationDTO.getSentThrough();
		this.channel=conversationDTO.getChannel();
		this.customChannel=conversationDTO.getCustomChannel();
		this.communicationDirection=conversationDTO.getCommunicationDirection();
		this.suffix = MessengerUtil.getMessageTypeSuffix(conversationDTO.getMessageType(), conversationDTO.getSource());
		this.extRefUid=conversationDTO.getExtRefUid();
    }

	public MessageDocumentDTO(VoiceCallDto voiceCall, Integer messengerContactId) {
		DateFormat df = new SimpleDateFormat(DATE_FORMAT);
		this.m_id = voiceCall.getId().toString();
		this.from = voiceCall.getFromNumber();
		this.to = voiceCall.getToNumber();
		this.cr_date = df.format(voiceCall.getCreateDate());
		this.cr_time = voiceCall.getCreateDate().getTime();
		this.msg_status = "success";
		this.msg_Body = voiceCall.getTranscription();
		if(voiceCall.getEncrypted() == 1 ){
			this.msg_Body = voiceCall.getTranscription();
			boolean isEncrypted = EncryptionUtil.encryptMessage(this, 1);
			this.setIsEncrypted(isEncrypted ? 1 : 0);
		}
		this.setIsEncrypted(voiceCall.getEncrypted());
		this.setSource(voiceCall.getSource());
		this.mcid = messengerContactId;
		this.channel = Channel.VOICE_CALL;
		this.sentThrough = SentThrough.WEB;
		// TODO: Revisit if change in message type needs to be made audio
		this.messageType = MessageType.CHAT;
		this.communicationDirection = CommunicationDirection.RECEIVE;
		this.suffix = MessengerUtil.getMessageTypeSuffix(MessageType.CHAT, voiceCall.getSource());
	}

	public MessageDocumentDTO(ActivityDto activityDto) {
		Assert.notNull(activityDto.getId(),"activityId cannot be null");
		DateFormat df = new SimpleDateFormat(DATE_FORMAT);
		this.m_id = activityDto.getId().toString();
		this.suffix="_a";
		this.activityType = activityDto.getActivityType();
		this.cr_date = df.format(activityDto.getCreated());
		this.cr_time = activityDto.getCreated().getTime();
		this.mcid = activityDto.getMcId();
		this.messageType = MessageType.ACTIVITY;
	}

	public MessageDocumentDTO(String emailId, ConversationDTO conversationDTO, Integer messengerContactId,
			Email email) {
		DateFormat df = new SimpleDateFormat(DATE_FORMAT);
		this.m_id = emailId;
		this.msg_status = StringUtils.isEmpty(email.getFailureReason()) ? "success" : email.getFailureReason();
		this.setSource(Source.EMAIL.getSourceId());
		this.channel = conversationDTO.getChannel();
		this.sentThrough = conversationDTO.getSentThrough();
		this.messageType = conversationDTO.getMessageType();
		this.communicationDirection = conversationDTO.getCommunicationDirection();
		this.mcid = messengerContactId;
		this.suffix = MessengerUtil.getMessageTypeSuffix(conversationDTO.getMessageType(), conversationDTO.getSource());
		this.cr_date = df.format(new Date());
		this.cr_time = new Date().getTime();
	}

	public MessageDocumentDTO(Email email, Integer messengerContactId) {
		DateFormat df = new SimpleDateFormat(DATE_FORMAT);
		this.m_id = email.getId().toString();
		this.from = email.getFromEmailId();
		this.msg_status = StringUtils.isEmpty(email.getFailureReason()) ? "success" : email.getFailureReason();
		this.msg_Body = email.getMessageBody();
		this.setIsEncrypted(0);
		this.setSource(Source.EMAIL.getSourceId());
		this.mcid = messengerContactId;
		if (!(Objects.isNull(email.getSentOn()) && Objects.isNull(email.getCreateDate()))) {
			this.cr_date = df.format((email.getSentOn() != null && email.getSentOn().after(email.getCreateDate()))
					? email.getSentOn()
							: email.getCreateDate());
			this.cr_time = email.getSentOn() != null && email.getSentOn().after(email.getCreateDate())
					? email.getSentOn().getTime()
							: email.getCreateDate().getTime();
		}
	}

    public MessageDocumentDTO(GoogleMessage gMsg, MessengerMessage msg) {
        DateFormat df = new SimpleDateFormat(DATE_FORMAT);
        this.m_id = String.valueOf(gMsg.getId());
        this.mcid = msg.getMessengerContactId();
        this.from = gMsg.getSenderId();
        this.to = gMsg.getRecipientId();
        this.cr_date = df.format(gMsg.getCreateDate());
        this.cr_time = gMsg.getCreateDate().getTime();
        this.msg_status = StringUtils.isEmpty(gMsg.getFailureReason()) ? "success" : gMsg.getFailureReason();
        this.msg_Body = gMsg.getMessageBody();
        this.suffix = MessengerUtil.getMessageTypeSuffix(MessageType.CHAT, Source.GOOGLE.getSourceId());
        this.setIsEncrypted(gMsg.getEncrypted());
        this.setSource(Source.GOOGLE.getSourceId());
        this.channel=MessageDocument.Channel.valueOf(msg.getChannel());
        this.messageType=MessageDocument.MessageType.valueOf(msg.getMessageType());
        this.communicationDirection=MessageDocument.CommunicationDirection.valueOf(msg.getCommunicationDirection());
    }
    public MessageDocumentDTO(AppleMessage aMsg, MessengerMessage msg) {
        DateFormat df = new SimpleDateFormat(DATE_FORMAT);
        this.m_id = String.valueOf(aMsg.getId());
        this.mcid = msg.getMessengerContactId();
        this.from = aMsg.getSenderId();
        this.to = aMsg.getRecipientId();
        this.cr_date = df.format(aMsg.getCreateDate());
        this.cr_time = aMsg.getCreateDate().getTime();
        this.msg_status = StringUtils.isEmpty(aMsg.getFailureReason()) ? "success" : aMsg.getFailureReason();
        this.msg_Body = aMsg.getMessageBody();
        this.suffix = MessengerUtil.getMessageTypeSuffix(MessageType.CHAT, Source.APPLE.getSourceId());
        this.setIsEncrypted(aMsg.getEncrypted());
        this.setSource(Source.APPLE.getSourceId());
        this.channel=MessageDocument.Channel.valueOf(msg.getChannel());
        this.messageType=MessageDocument.MessageType.valueOf(msg.getMessageType());
        this.communicationDirection=MessageDocument.CommunicationDirection.valueOf(msg.getCommunicationDirection());
        this.version=aMsg.getVersion();
        this.locale=aMsg.getLocale();
    }

    public MessageDocumentDTO(SecureMessage secureMessage, MessengerMessage msg, List<MessengerMediaFile> mediaFiles) {
        DateFormat df = new SimpleDateFormat(DATE_FORMAT);
        this.m_id = String.valueOf(secureMessage.getId());
        this.mcid = msg.getMessengerContactId();
        this.from = String.valueOf(secureMessage.getCustomerId());
        this.to = String.valueOf(secureMessage.getBusinessId());
        this.cr_date = df.format(secureMessage.getSentOn());
        this.cr_time = secureMessage.getSentOn().getTime();
        this.msg_status = StringUtils.isEmpty(secureMessage.getFailureReason()) ? "success"
                : secureMessage.getFailureReason();
        this.msg_Body = secureMessage.getMessageBody();
        this.suffix = MessengerUtil.getMessageTypeSuffix(MessageType.CHAT, Source.SECURE_MESSAGE.getSourceId());
        this.setIsEncrypted(secureMessage.getEncrypted());
        this.setSource(Source.SECURE_MESSAGE.getSourceId());
        this.channel = MessageDocument.Channel.valueOf(msg.getChannel());
        this.messageType = MessageDocument.MessageType.valueOf(msg.getMessageType());
        this.communicationDirection = MessageDocument.CommunicationDirection.valueOf(msg.getCommunicationDirection());
        this.mediaFiles = mediaFiles.stream().map(MediaFile::new).collect(Collectors.toList());
    }
}
