package com.birdeye.messenger.dto.facebook;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class GetUserDetailsMessage implements Serializable{

	private static final long serialVersionUID = 1L;
	
	private String pageId;
	private String userId;
	
	public GetUserDetailsMessage() {}
	
	public GetUserDetailsMessage(String pageId,String userId) {
		this.pageId=pageId;
		this.userId=userId;
	}
	
}
