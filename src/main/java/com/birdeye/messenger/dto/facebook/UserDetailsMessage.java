package com.birdeye.messenger.dto.facebook;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserDetailsMessage implements Serializable{

	private static final long serialVersionUID = 1L;
	
	private String first_name;
	private String last_name;
	private String profile_pic;
	private String id;
	
}
