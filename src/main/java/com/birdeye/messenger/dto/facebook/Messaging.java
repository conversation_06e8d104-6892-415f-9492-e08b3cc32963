package com.birdeye.messenger.dto.facebook;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.ToString;

@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class Messaging implements Serializable{
    private static final long serialVersionUID = 1L;
    private Long timestamp;
    private Message message;
    private User sender; 
    private User recipient; 
    private Status read;
    private Status delivery;

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public Message getMessage() {
        return message;
    }

    public void setMessage(Message message) {
        this.message = message;
    }

    public User getSender() {
        return sender;
    }

    public void setSender(User sender) {
        this.sender = sender;
    }

    public User getRecipient() {
        return recipient;
    }

    public void setRecipient(User recipient) {
        this.recipient = recipient;
    }

    public Status getRead() {
        return read;
    }

    public void setRead(Status read) {
        this.read = read;
    }

    public Status getDelivery() {
        return delivery;
    }

    public void setDelivery(Status delivery) {
        this.delivery = delivery;
    }
    
}
