package com.birdeye.messenger.dto.facebook;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.ToString;
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class Status implements Serializable{
    private static final long serialVersionUID = 1L;
    private Long watermark;
    private Integer seq;
    private List<String> mids;
    public Status(){}
    public Long getWatermark() {
        return watermark;
    }
    public void setWatermark(Long watermark) {
        this.watermark = watermark;
    }
    public Integer getSeq() {
        return seq;
    }
    public void setSeq(Integer seq) {
        this.seq = seq;
    }
    public List<String> getMids() {
        return mids;
    }
    public void setMids(List<String> mids) {
        this.mids = mids;
    }
    
}

