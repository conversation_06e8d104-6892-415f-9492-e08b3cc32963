package com.birdeye.messenger.dto.facebook;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.ToString;
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class User implements Serializable{
    private static final long serialVersionUID = 1L;
    private String id;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    
    public User() {
    	
    }
    
    public User(String id) {
    	this.id = id;
    }
    
}
