package com.birdeye.messenger.dto.facebook;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.ToString;
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class Payload implements Serializable{
    private static final long serialVersionUID = 1L;
    private String url;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
    
}
