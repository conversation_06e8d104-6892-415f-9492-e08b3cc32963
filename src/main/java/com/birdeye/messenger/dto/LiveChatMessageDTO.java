package com.birdeye.messenger.dto;

import java.io.Serializable;

import com.birdeye.messenger.external.dto.Suggestion;

import lombok.Getter;
import lombok.Setter;

/**
 * 
 * <AUTHOR>
 *
 */
@Getter
@Setter
public class LiveChatMessageDTO extends ChatDTO implements Serializable {

	private static final long serialVersionUID = 8515453683127503813L;

	// Message Details
	private String message;
	
	// mandatory fields
	private Integer mc_id;
	
	private Suggestion suggestion;
	
	private String clientIp;
	
	private Boolean containsContactInfo = false;
	
}
