package com.birdeye.messenger.dto;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.birdeye.messenger.dto.NexusEmailDTO.EmailFileAttachementData;

import com.birdeye.messenger.util.SecureString;
import com.birdeye.messenger.util.SecureStringUtil;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class EmailDTO {
    private List<String> to = new ArrayList<>();
    private List<String> bcc = new ArrayList<>();
    private Map<String, File> attachments = new HashMap<String, File>();
    private String subject;
    private String body;
    @SecureString
    private String from;
    private Double amount;
    @SecureString
    private String replyTo;
    private String fromName;
    private int verifyCustomer = 0;
    public enum TEXT_TYPE {HTML, TEXT};
    private TEXT_TYPE textType = TEXT_TYPE.HTML;
    private String requestId;
    private String requestType;
    private String requestSubType;
    private String frequency;
    private String businessId;
    @SecureString
    private List<String> emailIdsToBeExcluded = new ArrayList<>();
    private Long emailDispatchTime;
    private Long emailReceivedTime;
    private List<EmailMessageDocument> emailMessageDocuments = new ArrayList<>();
    private List<MessengerMediaFileDTO> emailAttachments;
	private Integer emailDataId;
    private Integer encrypted;

    private String externalUid;
	private String attactmentUrl;
	private List<String> s3AttachementUrls = new ArrayList<>();
	private List<EmailFileAttachementData> fileAttachementData = new ArrayList<>();
	private Long businessNumber;
	private Integer customerId;
	private String tempBody;
	private String recipientType;
	private boolean isCustomTemplate;
	private Map<String, String> dataObject;
	private Integer isCustomerEmail;
	private String emailCategory;
    public void clearAndAddTo(String email){
        this.to = new ArrayList<>();
        this.to.add(email);
    }

    @Override
    public String toString() {
        return SecureStringUtil.buildSecureToString(this);
    }
}
