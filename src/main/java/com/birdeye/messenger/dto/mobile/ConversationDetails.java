package com.birdeye.messenger.dto.mobile;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.TimeZone;

import com.birdeye.messenger.util.SecureString;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.exception.BadDataException;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.TimeZoneUtil;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;

@Data
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class ConversationDetails {

    private Integer id;
    private String name;
    @SecureString
    private String phone;
    @SecureString
    private String emailId;
    private Integer businessId;
    private Integer tag;

    private List<MessagesByDate> msgsByDate;

    public ConversationDetails(ContactDocument contact, List<MessageDocument> messageDocuments, String timeZoneId) {
        this.id = contact.getC_id();
        this.name = contact.getC_name();
        this.phone = contact.getC_phone();
        this.emailId = contact.getC_email();
        this.businessId = contact.getB_id();
        Map<String, List<MessagesByDate.CommnMessages>> msgByDay = new HashMap<>();
        Map<String, Date> msgDateMap = new HashMap<>();
//        timezone = StringUtils.isNotEmpty(timezone) ? TimeZoneUtil.getTimeZoneID(timezone) : "PST";
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        timeZoneId = StringUtils.isNotEmpty(timeZoneId) ? timeZoneId : "PST";
        TimeZone tz = TimeZone.getTimeZone(timeZoneId);
        for (MessageDocument document : messageDocuments) {
            if (document.getCr_date() == null) {
                continue;
            }
            Date sentOnUTC = null;
            try {
                sentOnUTC = df.parse(document.getCr_date());
            } catch (ParseException e) {
                throw new BadDataException("Failed to parse cr_date for doc " + document.toString());
            }
            Date sentOn = ControllerUtil.convertToBusinessTimeZone(sentOnUTC, tz).getTime();
            String sendingTime = new SimpleDateFormat("MMM dd, yyyy").format(sentOn) + " " + tz.getDisplayName(tz.inDaylightTime(sentOn), TimeZone.SHORT, Locale.US);
            List<MessagesByDate.CommnMessages> list = msgByDay.get(sendingTime);
            if (list == null) {
                list = new ArrayList<MessagesByDate.CommnMessages>();
                msgByDay.put(sendingTime, list);
            }
            list.add(new MessagesByDate.CommnMessages(document, sentOn, timeZoneId));
            //updateMessageList(msgByDay, new CommnMessages(message, sentOn), sendingTime);
            msgDateMap.put(sendingTime, sentOn);
        }
        msgsByDate = new ArrayList<MessagesByDate>();
        for (String day : msgByDay.keySet()) {

            List<MessagesByDate.CommnMessages> msgs = sortMessage(msgByDay.get(day));

            if (CollectionUtils.isNotEmpty(msgs)) {
                MessagesByDate message = new MessagesByDate(day, msgs, msgDateMap.get(day));
                msgsByDate.add(message);
            }
        }
        sortGroupMessageByDate(msgsByDate);
    }

    private List<MessagesByDate.CommnMessages> sortMessage(List<MessagesByDate.CommnMessages> messages) {
        Collections.sort(messages, new Comparator<MessagesByDate.CommnMessages>() {
            @Override
            public int compare(MessagesByDate.CommnMessages o1, MessagesByDate.CommnMessages o2) {
                if (o1.getSentOn() == null || o2.getSentOn() == null) {
                    return -1;
                }
                if(o1.getSentAt().compareTo(o2.getSentAt()) == 0) {
                    return compareMsgId(o1, o2);
                }
                return o1.getSentAt().compareTo(o2.getSentAt());
            }

			private int compareMsgId(MessagesByDate.CommnMessages o1, MessagesByDate.CommnMessages o2) {
				if(StringUtils.isNotBlank(o1.getMsgId()) && StringUtils.isNotBlank(o2.getMsgId())){
					try {
						return Integer.valueOf(o1.getMsgId()).compareTo(Integer.valueOf(o2.getMsgId()));						
					}catch(Exception e) {
						return 0;
					}
				}
				return 0;
			}
        });
        return messages;
    }

    private void sortGroupMessageByDate(List<MessagesByDate> messages) {
        Collections.sort(messages, new Comparator<MessagesByDate>() {
            @Override
            public int compare(MessagesByDate o1, MessagesByDate o2) {
                if (o1.getMsgDate() == null || o2.getMsgDate() == null) {
                    return -1;
                }
                return o1.getMsgDate().compareTo(o2.getMsgDate());
            }
        });
    }

}
