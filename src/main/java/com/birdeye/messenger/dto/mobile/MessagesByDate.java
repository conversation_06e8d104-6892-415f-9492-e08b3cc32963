package com.birdeye.messenger.dto.mobile;

import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.util.MessengerUtil;
import com.birdeye.messenger.util.TimeZoneUtil;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.TimeZone;

public class MessagesByDate {

    private String grpDate;
    private Long msgDate;
    private List<CommnMessages> messages;

    public MessagesByDate() {
    }

    public MessagesByDate(String day, List<CommnMessages> msgs, Date msgDate) {
        this.grpDate = day;
        this.messages = msgs;
        this.msgDate = msgDate.getTime();
    }

    /**
     * @return the grpDate
     */
    public String getGrpDate() {
        return grpDate;
    }

    /**
     * @param grpDate the grpDate to set
     */
    public void setGrpDate(String grpDate) {
        this.grpDate = grpDate;
    }

    /**
     * @return the messages
     */
    public List<CommnMessages> getMessages() {
        return messages;
    }

    /**
     * @param messages the messages to set
     */
    public void setMessages(List<CommnMessages> messages) {
        this.messages = messages;
    }

    /**
     * @return the msgDate
     */
    public Long getMsgDate() {
        return msgDate;
    }

    /**
     * @param msgDate the msgDate to set
     */
    public void setMsgDate(Long msgDate) {
        this.msgDate = msgDate;
    }

    public static class CommnMessages {

        private String msgId;
        private String sentOn;
        private Long sentAt;
        private Integer senderId;
        private String sentBy;
        private String messageType;
        private String status;
        private String body;
        private String extn;
        private String mediaUrl;
        private String contentSize;
        private String contentType;
        private String thumbnail;
        private Integer source;
        private String timezone;

        public CommnMessages() {

        }

        private String setSender(Integer source) {
            if (source != null) {
                switch (source) {
                    case 0:
                        return "auto campaign";
                    case 1:
                        return "auto reply";
                    case 2:
                        return "webchat";
                    default:
                        return "auto reply";
                }
            }
            return "auto reply";
        }

        public CommnMessages(MessageDocument messageDocument, Date sentAt, String timeZoneId) {
            this.msgId = messageDocument.getM_id().toString();
            this.sentAt = sentAt.getTime();
//            timezone = StringUtils.isNotEmpty(timezone) ? TimeZoneUtil.getTimeZoneID(timezone) : "PST";
            timeZoneId = StringUtils.isNotEmpty(timeZoneId) ? timeZoneId : "PST";
            TimeZone tz = TimeZone.getTimeZone(timeZoneId);
            this.sentOn = new SimpleDateFormat("MMM dd, yyyy hh:mm a")
                    .format(this.sentAt) + " " +tz.getDisplayName(tz.inDaylightTime(sentAt), TimeZone.SHORT, Locale.US);
            this.timezone = tz.getDisplayName(tz.inDaylightTime(sentAt), TimeZone.SHORT, Locale.US);
            this.senderId = messageDocument.getU_id();
            this.sentBy = messageDocument.getU_name();
            this.messageType = messageDocument.getMsg_type();
            this.source = messageDocument.getSource();
            if(StringUtils.containsIgnoreCase(this.messageType, "SEND") && StringUtils.isEmpty(this.sentBy))
                this.sentBy = setSender(this.source);
            this.status = messageDocument.getMsg_status();
            this.body = MessengerUtil.decryptMessage(messageDocument);
            this.extn = messageDocument.getA_ext();
            this.mediaUrl = MessengerUtil.decode(messageDocument.getA_url(), "UTF-8");
            this.contentSize = messageDocument.getA_size();
            this.contentType = messageDocument.getA_contype();
            for (SUPPORTED_UPLOAD_FILES filetype : SUPPORTED_UPLOAD_FILES.values()) {
                if(this.mediaUrl != null && this.mediaUrl.contains(filetype.getExtension())){
                    this.thumbnail = filetype.getThumbnailUrl();
                    break;
                }
            }
        }

        private String getFileExtension(String fileName) {
            if (StringUtils.isNotBlank(fileName) && fileName.lastIndexOf(".") != -1 && fileName.lastIndexOf(".") != 0) {
                return fileName.substring(fileName.lastIndexOf(".") + 1);
            } else {
                return "";
            }
        }

        /**
         * @return the sentOn
         */
        public String getSentOn() {
            return sentOn;
        }

        /**
         * @param sentOn the sentOn to set
         */
        public void setSentOn(String sentOn) {
            this.sentOn = sentOn;
        }

        /**
         * @return the senderId
         */
        public Integer getSenderId() {
            return senderId;
        }

        /**
         * @param senderId the senderId to set
         */
        public void setSenderId(Integer senderId) {
            this.senderId = senderId;
        }

        /**
         * @return the sentBy
         */
        public String getSentBy() {
            return sentBy;
        }

        /**
         * @param sentBy the sentBy to set
         */
        public void setSentBy(String sentBy) {
            this.sentBy = sentBy;
        }

        /**
         * @return the messageType
         */
        public String getMessageType() {
            return messageType;
        }

        /**
         * @param messageType the messageType to set
         */
        public void setMessageType(String messageType) {
            this.messageType = messageType;
        }

        /**
         * @return the status
         */
        public String getStatus() {
            return status;
        }

        /**
         * @param status the status to set
         */
        public void setStatus(String status) {
            this.status = status;
        }

        /**
         * @return the body
         */
        public String getBody() {
            return body;
        }

        /**
         * @param body the body to set
         */
        public void setBody(String body) {
            this.body = body;
        }

        /**
         * @return the extn
         */
        public String getExtn() {
            return extn;
        }

        /**
         * @param extn the extn to set
         */
        public void setExtn(String extn) {
            this.extn = extn;
        }

        /**
         * @return the mediaUrl
         */
        public String getMediaUrl() {
            return mediaUrl;
        }

        /**
         * @param mediaUrl the mediaUrl to set
         */
        public void setMediaUrl(String mediaUrl) {
            this.mediaUrl = mediaUrl;
        }

        /**
         * @return the contentType
         */
        public String getContentType() {
            return contentType;
        }

        /**
         * @param contentType the contentType to set
         */
        public void setContentType(String contentType) {
            this.contentType = contentType;
        }

        /**
         * @return the sentAt
         */
        public Long getSentAt() {
            return sentAt;
        }

        /**
         * @param sentAt the sentAt to set
         */
        public void setSentAt(Long sentAt) {
            this.sentAt = sentAt;
        }

        /**
         * @return the contentSize
         */
        public String getContentSize() {
            return contentSize;
        }

        /**
         * @param contentSize the contentSize to set
         */
        public void setContentSize(String contentSize) {
            this.contentSize = contentSize;
        }

        /**
         * @return the thumbnail
         */
        public String getThumbnail() {
            return thumbnail;
        }

        /**
         * @param thumbnail the thumbnail to set
         */
        public void setThumbnail(String thumbnail) {
            this.thumbnail = thumbnail;
        }

        /**
         * @return the msgId
         */
        public String getMsgId() {
            return msgId;
        }

        /**
         * @param msgId the msgId to set
         */
        public void setMsgId(String msgId) {
            this.msgId = msgId;
        }

        /**
         * @return the source
         */
        public Integer getSource() {
            return source;
        }

        /**
         * @param source the source to set
         */
        public void setSource(Integer source) {
            this.source = source;
        }

        /**
         * @return the timezone
         */
        public String getTimezone() {
            return timezone;
        }

        /**
         * @param timezone the timezone to set
         */
        public void setTimezone(String timezone) {
            this.timezone = timezone;
        }

        @Override
        public String toString() {
            return "CommnMessages{" +
                    "msgId='" + msgId + '\'' +
                    ", sentOn='" + sentOn + '\'' +
                    ", sentAt=" + sentAt +
                    ", senderId=" + senderId +
                    ", sentBy='" + sentBy + '\'' +
                    ", messageType='" + messageType + '\'' +
                    ", status='" + status + '\'' +
                    ", body='" + body + '\'' +
                    ", extn='" + extn + '\'' +
                    ", mediaUrl='" + mediaUrl + '\'' +
                    ", contentSize='" + contentSize + '\'' +
                    ", contentType='" + contentType + '\'' +
                    ", thumbnail='" + thumbnail + '\'' +
                    ", source=" + source +
                    ", timezone='" + timezone + '\'' +
                    '}';
        }
    }

    /**
     * List of files extensions supported for upload on S3
     */
    public enum SUPPORTED_UPLOAD_FILES {

        MP4("mp4", "https://d3cnqzq0ivprch.cloudfront.net/prod/css/images/file-type/mp4s.png"),
        //JSON(".json","application/json"),
        PDF("pdf", "https://d3cnqzq0ivprch.cloudfront.net/prod/css/images/file-type/pdfs.png"),
        XLSM("xlsm", "https://d3cnqzq0ivprch.cloudfront.net/prod/css/images/file-type/xlss.png"),
        XLS("xls", "https://d3cnqzq0ivprch.cloudfront.net/prod/css/images/file-type/xlss.png"),
        XLSX("xlsx", "https://d3cnqzq0ivprch.cloudfront.net/prod/css/images/file-type/xlss.png"),
        PNG("png", ""),
        JPEG("jpeg", ""),
        JPG("jpg", ""),
        GIF("gif", "https://d3cnqzq0ivprch.cloudfront.net/prod/css/images/file-type/gifs.png"),
        TXT("txt", "https://d3cnqzq0ivprch.cloudfront.net/prod/css/images/file-type/docfile.png"),
        DOCX("docx", "https://d3cnqzq0ivprch.cloudfront.net/prod/css/images/file-type/docfile.png"),
        CSV("csv", "https://demo3.birdeye.com/common/css/images/file-type/xlss.png"),
        DOC("doc", "https://d3cnqzq0ivprch.cloudfront.net/prod/css/images/file-type/docfile.png"),
        OTHERS("others", "https://d3cnqzq0ivprch.cloudfront.net/prod/css/images/file-type/docfile.png");

        private String extension;
        private String thumbnailUrl;

        private SUPPORTED_UPLOAD_FILES(String extension, String thumbnailUrl) {
            this.extension = extension;
            this.thumbnailUrl = thumbnailUrl;
        }

        public String getExtension() {
            return extension;
        }

        public String getThumbnailUrl() {
            return thumbnailUrl;
        }
    }
}
