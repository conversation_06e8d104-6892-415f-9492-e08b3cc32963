package com.birdeye.messenger.dto.twitter;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import com.birdeye.messenger.dto.ConversationDTO;
import com.birdeye.messenger.dto.MessageDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class TwitterMessageRequest extends MessageDTO implements Serializable{

    private static final long serialVersionUID = 1L;
    private String for_user_id;
    private List<DirectMessageEvent> direct_message_events;
    private Map<String, User> users;
    private Map<String, App> apps;
    private ConversationDTO conversationDTO;


}
