package com.birdeye.messenger.dto.twitter;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class MessageData implements Serializable{
    private static final long serialVersionUID = 1L;

    private String text;
    private Entities entities;
    private Attachment attachment;
}
