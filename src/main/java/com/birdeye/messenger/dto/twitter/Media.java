package com.birdeye.messenger.dto.twitter;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class Media implements Serializable{
    private static final long serialVersionUID = 1L;

    private String id;
    private String id_str;
    private List<Integer> indices;
    private String media_url;
    private String media_url_https;
    private String url;
    private String display_url;
    private String expanded_url;
    private String type;
    private Sizes sizes;
    private VideoInfo video_info;
}
