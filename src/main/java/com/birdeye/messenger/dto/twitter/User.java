package com.birdeye.messenger.dto.twitter;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class User implements Serializable{

    private static final long serialVersionUID = 1L;

    private String id;
    private String created_timestamp;
    private String name;
    private String screen_name;
    @JsonProperty("protected")
    private boolean userProtected;
    private boolean verified;
    private Integer followers_count;
    private Integer friends_count;
    private Integer statuses_count;
    private String profile_image_url;
    private String profile_image_url_https;
    private String location;
    
}
