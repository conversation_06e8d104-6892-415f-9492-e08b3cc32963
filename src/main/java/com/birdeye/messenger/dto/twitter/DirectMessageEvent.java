package com.birdeye.messenger.dto.twitter;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class DirectMessageEvent implements Serializable{

    private static final long serialVersionUID = 1L;

    private String type;
    private String id;
    private String created_timestamp;
    private MessageCreate message_create;
}
