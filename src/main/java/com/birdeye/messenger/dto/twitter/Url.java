package com.birdeye.messenger.dto.twitter;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class Url implements Serializable{
    private static final long serialVersionUID = 1L;
    private String url;
    private String expanded_url;
    private String display_url;
    private List<Integer> indices;
}
