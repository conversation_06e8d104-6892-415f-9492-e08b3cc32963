package com.birdeye.messenger.dto.twitter;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class Entities implements Serializable{
    private static final long serialVersionUID = 1L;

    private List<String> hashtags;
    private List<String> symbols;
    private List<String> user_mentions;
    private List<Url> urls;
}
