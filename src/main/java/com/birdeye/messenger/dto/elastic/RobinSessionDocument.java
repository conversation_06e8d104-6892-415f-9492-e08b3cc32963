package com.birdeye.messenger.dto.elastic;

import com.birdeye.messenger.enums.RobinSessionEnum;
import com.birdeye.messenger.enums.Source;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
public class RobinSessionDocument {
    private RobinSessionEnum robinSession;
    private String channel;
    private Long createdAt;
    private Long lastUpdatedAt;
}
