package com.birdeye.messenger.dto.elastic;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Setter
@Getter
@NoArgsConstructor
public class UpsertMessage<T> implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private T doc;
    private boolean doc_as_upsert = false;

    public UpsertMessage(T doc, boolean isUpsert){
        this.doc = doc;
        this.doc_as_upsert = isUpsert;
    }

}
