package com.birdeye.messenger.dto.elastic;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VideoContextDocument implements Serializable {
	
	private static final long serialVersionUID = -4897597985292804389L;
	
	private String roomId;
	
	private Long roomStartEpoch;
	
	private Long conversationStartEpoch;
	
	private Long lastStateTransitionEpoch;
	
	/* NBO -> Nobody online
	 * OCO -> Only customer joined 
	 * OBO -> Only business joined
	 * ACT (Active → Both parties online)
	 * END (End)
	 */
	private String state;
	
	private String usrMeetingLink;
	
	public VideoContextDocument(String roomId, Long roomStartEpoch, Long conversationStartEpoch, Long lastStateTransitionEpoch, String state, String bizUserLink) {
		this.roomId = roomId;
		this.roomStartEpoch = roomStartEpoch;
		this.conversationStartEpoch = conversationStartEpoch;
		this.lastStateTransitionEpoch = lastStateTransitionEpoch;
		this.state = state;
		this.usrMeetingLink = bizUserLink;
	}
}
