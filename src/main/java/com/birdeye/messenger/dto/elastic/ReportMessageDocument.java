package com.birdeye.messenger.dto.elastic;

import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.dto.elastic.MessageDocument.MessageType;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.Source;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
/**
 * <AUTHOR>
 *
 */
public class ReportMessageDocument {

	private Integer businessId;
	private String customerId;
	private String createdDate;
	private Source channel;
	private CommunicationDirection communicationDirection;
	private MessageType messageType;
	private ActivityType activityType;
	
	public ReportMessageDocument(MessageDocument messageDoc) {
		this.businessId = messageDoc.getB_id();
		this.customerId = messageDoc.getC_id();
		this.createdDate = messageDoc.getCr_date();
		this.channel = Source.getValue(messageDoc.getSource());
		this.communicationDirection = messageDoc.getCommunicationDirection();
		if (this.channel == Source.CAMPAIGN)
			this.communicationDirection = CommunicationDirection.SEND;
		
		this.messageType = messageDoc.getMessageType();
		this.activityType = messageDoc.getActivityType();
	}
}
