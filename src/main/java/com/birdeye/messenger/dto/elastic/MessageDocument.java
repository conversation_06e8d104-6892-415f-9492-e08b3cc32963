package com.birdeye.messenger.dto.elastic;

import java.io.Serializable;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;

import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.enums.*;
import com.birdeye.messenger.util.MediaUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dto.apple.chat.RichLinkData;
import com.birdeye.messenger.dto.appointment.AppointmentInfo;
import com.birdeye.messenger.dto.payment.PaymentInfo;
import com.birdeye.messenger.sro.ReviewEvent;
import com.birdeye.messenger.sro.SurveyEvent;
import com.birdeye.messenger.util.ActivityBuilder;
import com.birdeye.messenger.util.MessengerUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.web.util.UriComponentsBuilder;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MessageDocument implements Identifiable,Serializable,Cloneable{
    /**
     *
     */
    private static final long serialVersionUID = 1L;
    // Messenger Contact Id
    private String c_id;
    // SMS/Facebook message Id
    private String m_id;
    private Integer e_id;
    private String from;
    private String to;
    private String cr_date;
    private Integer u_id;
    private String u_name;
    private String msg_type; // MESSENGER_EVENT
    private String msg_status;
    private String msg_body;
    private String msg_subject;
    private String a_ext;
    private String a_url;
    private String a_size;
    private String a_name;
    private String a_contype;
    private Integer source = 0;
    private Integer is_encrypt;
    private String type; // CREATE or UPDATE

    private String updatedAt;
    private MessageType messageType;
    private UserDetail createdBy;
    private CommunicationDirection communicationDirection;
    private SentThrough sentThrough;
    private List<MediaFile> mediaFiles;
    private UserDetail updatedBy;
    private NoteState noteState;
    private NoteType noteType;
    private ActivityType activityType;
    private UserDetail triggeredFor;
    private UserDetail triggeredFrom;
    private Channel channel;
    private String customChannel;
    private Integer b_id;
    private String ext_ref_uid;
    private Long durationInMS;
    // used for saving created date in epoch time
    private Long cr_time;

    // VoiceMail URL - twilio hosted.
    private String voiceMailUrl;

    private Integer templateId;
    private String pageUrlTrackedByGoogleAnalytics;

    private ActivityMeantFor activityMeantFor;
    private List<QuestionAndAnswer> qnaList;
    private CustomerInfo referrer;
    private CustomerInfo referredLead;
    private StatusEnum thankYouNoteStatus;
    private ReferralSource referralSource;
    private Long u_time; // update date
    private Integer reviewId;
    private Long review_cr_upd_time;
    private SurveyDetail survey_detail;
    private Long absoluteResponseTime;
    private Long relativeResponseTime;
    private Boolean firstResponse;
    private String refMsgId; // for response time calculation
    private BusinessDetail fromBusinessDetail;
    private BusinessDetail toBusinessDetail;
    private RobinResponseType robinResponseType;
    private UnansweredFaqType unansweredFaqType;
    private String clientIp;

    private Long lastUpdateDate;
    private boolean spam;

    private String storyReplyUrl;
    private String storyMentionUrl;
    private boolean liked;
    private MessageLikedBy messageLikedBy;
    private boolean deleted;
    private MessageDeletedBy messageDeletedBy;
    
    public void setLastUpdateDate() {
        Date now = new Date();
        this.lastUpdateDate = now.getTime();
    }
    
    //----------------------------
    private PaymentInfo paymentInfo;
    private AppointmentInfo appointmentInfo;

    private boolean secureFaq;
    private boolean answerFlag = false;
    private String authToken;
    private String version;
    private String locale;
    private Long eventProcessingTimeStamp;
    private Long timeElapsedInProcessing;
    private RichLinkData richLinkData;
    private ContactState customerType; //LEAD/CUSTOMER
    private Integer experienceScore; //sentiment score
    private Integer surveyScore; //overall score
    private CustomerInfo conversationSwitchedFrom;
    private CustomerInfo conversationSwitchedTo;

    @JsonIgnore
    private String requestSource;
    @JsonIgnore
    private Boolean isAppCompatible=false;
    
    private Integer smsId;
    private Integer emailId;
    
    private List<String> citations;
    private boolean faqFlag;
    private boolean fileFlag;
    
    private CustomerContactInfo customerContactInfo;
    private ContactInfoSourceEnum customerContactInfoSource;
    
    private boolean replyViaEmail;
    
	private String waMsgHeader;
	private String waMsgFooter;

    public MessageDocument() {
        this.type = Type.CREATE.getType();
    }

    public MessageDocument(ActivityBuilder builder) {
        this.e_id = builder.getAccountId();
        this.m_id = builder.getMId();
        this.c_id = builder.getMessengerContactId();
        this.messageType = builder.getMessageType();
        this.activityType = builder.getActivityType();
        this.createdBy = builder.getCreatedBy();
        DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
        this.cr_date = df.format(builder.getCreatedAt());
        // adding created date in epoch time below
        this.cr_time = builder.getCreatedAt().getTime();
        this.u_time = builder.getCreatedAt().getTime();
        if(builder.getSource() != null)
            this.source = builder.getSource();
        this.b_id = builder.getBusinessId();
    }
    
    public MessageDocument(ActivityDto activity) {
        this.e_id = activity.getAccountId();
        this.m_id = activity.getId().toString(); 
        this.c_id = activity.getMcId().toString();
        this.u_id = activity.getActorId();
        this.messageType = MessageType.ACTIVITY;
        this.activityType = activity.getActivityType();
        this.createdBy = new UserDetail(activity.getActorId(), activity.getActorName());
        DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
        this.cr_date = df.format(activity.getCreated());
        // adding created date in epoch time below
        this.cr_time = activity.getCreated().getTime();
        this.u_time = activity.getCreated().getTime();
        this.triggeredFor = new UserDetail(activity.getTo(), activity.getToName());
        this.triggeredFrom = new UserDetail(activity.getFrom(), activity.getFromName());
        this.activityMeantFor = activity.getActivityMeantFor();
        this.setQnaList(activity.getQnaList());
        this.source = activity.getSource();
        this.b_id = activity.getBusinessId();
        this.durationInMS = activity.getDurationInMS();
        this.referredLead = activity.getReferralLeadInfo();
        this.referrer = activity.getReferrerInfo();
        this.conversationSwitchedFrom = activity.getConversationSwitchedFrom();
        this.conversationSwitchedTo = activity.getConversationSwitchedTo();
        this.thankYouNoteStatus = activity.getThankYouNoteStatus();
        this.referralSource = activity.getReferralSource();
        if(Objects.nonNull(this.referralSource)) this.channel = Channel.REFERRAL;
        this.fromBusinessDetail = activity.getFromBusinessDetail();
        this.toBusinessDetail = activity.getToBusinessDetail();
        this.paymentInfo = activity.getPaymentInfo();
        this.appointmentInfo = activity.getAppointmentInfo();
        this.authToken = activity.getTransferAuthToken();
        if(Objects.nonNull(activity.getCustomerContactInfo())){
            this.customerContactInfo = activity.getCustomerContactInfo();
        }
        if(Objects.nonNull(activity.getCustomerContactInfoSource())){
            this.customerContactInfoSource = activity.getCustomerContactInfoSource();
        }

        // Set customChannel for custom channel activities
        if(ActivityType.CUSTOM_CHANNEL_CONVERSATION_STARTED.equals(activity.getActivityType())) {
            this.customChannel = activity.getCustomChannel();
        }
    }

    public static class Builder {

        private MessageDocument document;
        
        public Builder(MessageDocument document) {
            this.document = document;
        }

        public Builder addMessageInfo(MessageDocumentDTO messageDocumentDTO) {
            document.m_id = messageDocumentDTO.getM_id();
            document.from = messageDocumentDTO.getFrom();
            document.to = messageDocumentDTO.getTo();
            document.cr_date = messageDocumentDTO.getCr_date();
            // adding created date in epoch time below
            document.cr_time = messageDocumentDTO.getCr_time();
            //EXR - change for dashboard message miss
            document.u_time = new Date().getTime();
            document.setMsg_status(messageDocumentDTO.getMsg_status());
            document.setSource(messageDocumentDTO.getSource());
            document.setMessageType(messageDocumentDTO.getMessageType());
            document.setMsg_body(messageDocumentDTO.getMsg_Body());
            document.setMsg_subject(messageDocumentDTO.getMsg_subject());
            document.setIs_encrypt(messageDocumentDTO.getIsEncrypted());
            document.setC_id(messageDocumentDTO.getMcid().toString());
            document.communicationDirection=messageDocumentDTO.getCommunicationDirection();
            document.sentThrough=messageDocumentDTO.getSentThrough();
            document.channel=messageDocumentDTO.getChannel();
            document.customChannel=messageDocumentDTO.getCustomChannel();
            document.activityType =  messageDocumentDTO.getActivityType();
            document.ext_ref_uid=messageDocumentDTO.getExtRefUid();
            document.templateId = messageDocumentDTO.getTemplateId();
            document.setReferredLead(messageDocumentDTO.getReferredLead());
            document.setReferrer(messageDocumentDTO.getReferrer());
            document.pageUrlTrackedByGoogleAnalytics = messageDocumentDTO.getPageUrlTrackedByGoogleAnalytics();
            document.setPaymentInfo(messageDocumentDTO.getPaymentInfo());
            document.setVersion(messageDocumentDTO.getVersion());
            document.setLocale(messageDocumentDTO.getLocale());
            document.setRichLinkData(messageDocumentDTO.getRichLinkData());
            document.setCustomerType(messageDocumentDTO.getCustomerType());
            document.setExperienceScore(messageDocumentDTO.getExperienceScore());
            document.setSurveyScore(messageDocumentDTO.getSurveyScore());
            if(StringUtils.isNotBlank(messageDocumentDTO.getStoryReplyUrl())){
                Map<String,Object> mediaDetails =MediaUtils.getContentHeaders(messageDocumentDTO.getStoryReplyUrl());
                String storyReplyUrl = UriComponentsBuilder.fromUriString(messageDocumentDTO.getStoryReplyUrl())
                        .queryParam("type", mediaDetails.get("content-type").toString())
                        .build()
                        .toString();
                document.setStoryReplyUrl(storyReplyUrl);            }
            if(StringUtils.isNotBlank(messageDocumentDTO.getStoryMentionUrl())){
                Map<String,Object> mediaDetails =MediaUtils.getContentHeaders(messageDocumentDTO.getStoryMentionUrl());
                String storyMentionUrl = UriComponentsBuilder.fromUriString(messageDocumentDTO.getStoryMentionUrl())
                        .queryParam("type", mediaDetails.get("content-type").toString())
                        .build()
                        .toString();
                document.setStoryMentionUrl(storyMentionUrl);            }
            if(CollectionUtils.isNotEmpty(messageDocumentDTO.getCitations())){
                document.setCitations(messageDocumentDTO.getCitations());
            }
            if(Objects.nonNull(messageDocumentDTO.getCustomerContactInfo())){
                document.setCustomerContactInfo(messageDocumentDTO.getCustomerContactInfo());
            }
            if(Objects.nonNull(messageDocumentDTO.getCustomerContactInfoSource())){
                document.setCustomerContactInfoSource(messageDocumentDTO.getCustomerContactInfoSource());
            }
            document.setFaqFlag(messageDocumentDTO.isFaqFlag());
            document.setFileFlag(messageDocumentDTO.isFileFlag());
            document.waMsgHeader = messageDocumentDTO.getWaMsgHeader();
            document.waMsgFooter = messageDocumentDTO.getWaMsgFooter();
            return this;
        }


        public Builder addReviewInfo(ReviewEvent reviewEvent, Integer mcId) {

            document.m_id = reviewEvent.getReviewDetail().getReviewId().toString();
            DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
            document.cr_date = df.format(reviewEvent.getReviewDetail().getReviewDate());
            // adding created date in epoch time below
            document.cr_time = reviewEvent.getReviewDetail().getReviewDate();
            document.u_time = reviewEvent.getReviewDetail().getReviewDate();
            document.setMessageType(MessageType.REVIEW);
            document.setB_id(reviewEvent.getReviewDetail().getBusinessId());
            document.setC_id(mcId.toString());
            document.setReviewId(reviewEvent.getReviewDetail().getReviewId());
            document.setReview_cr_upd_time(reviewEvent.getReviewDetail().getReviewDate());
            return this;
        }

        public Builder addSurveyDetails(SurveyEvent.After surveyEvent, Integer mcId,String surveyName) {

            document.m_id = surveyEvent.getId().toString();
            DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
            document.cr_date = df.format(surveyEvent.getResponseDate());
            // adding created date in epoch time below
            document.cr_time = surveyEvent.getResponseDate().getTime();
            document.u_time = surveyEvent.getResponseDate().getTime();
            document.setMessageType(MessageType.SURVEY_RESPONSE);
            document.setB_id(surveyEvent.getBusinessId());
            document.setC_id(mcId.toString());
            SurveyDetail sd = null;
            if (surveyEvent.getOverallScore() != null){
                DecimalFormat decimalFormat = new DecimalFormat("#.#");
                sd = new SurveyDetail(surveyEvent.getId(),surveyEvent.getSurveyId(),surveyName,surveyEvent.getResponseDate().getTime(),Float.valueOf(decimalFormat.format(surveyEvent.getOverallScore())));
            }else {
                sd = new SurveyDetail(surveyEvent.getId(),surveyEvent.getSurveyId(),surveyName,surveyEvent.getResponseDate().getTime(),surveyEvent.getOverallScore());
            }
            document.setSurvey_detail(sd);
            return this;
        }

        public Builder addSenderName(UserDTO userDTO) {
            if(Objects.nonNull(userDTO)) {
                String userName = MessengerUtil.buildUserName(userDTO);
                if(userDTO.getId() != null) {
                    document.u_id = userDTO.getId();
                }
                document.u_name=userName;
                document.createdBy=new UserDetail(userDTO.getId(), userName);
            }
            return this;
        }

        public Builder addMessengerMediaFileInfo(MessengerMediaFileDTO messengerMediaFileDTO) {
            if (messengerMediaFileDTO != null) {
                // Handling for voicemail urls.
                if (MessengerMediaFileDTO.VOICEMAIL_TYPE.equals(messengerMediaFileDTO.getContentType())) {
                    document.voiceMailUrl = messengerMediaFileDTO.getUrl();
                } else {
                    document.a_contype = messengerMediaFileDTO.getContentType();
                    document.a_size = messengerMediaFileDTO.getContentSize();
                    document.a_name = messengerMediaFileDTO.getName();
                    document.a_url = messengerMediaFileDTO.getUrl();
                    document.a_ext = messengerMediaFileDTO.getFileExtension();
                    MediaFile mediaFile = new MediaFile(messengerMediaFileDTO.getFileExtension(),
                            messengerMediaFileDTO.getUrl(), messengerMediaFileDTO.getContentSize(),
                            messengerMediaFileDTO.getName(), messengerMediaFileDTO.getContentType());
                    document.mediaFiles = Collections.singletonList(mediaFile);
                }
            }
            return this;
        }

        public Builder addMessengerMediaFileInfoList(MessengerMediaFileDTO messengerMediaFileDTO,
                                                     List<MediaFile> mediaFiles) {
            if (messengerMediaFileDTO != null && MessengerMediaFileDTO.VOICEMAIL_TYPE.equals(messengerMediaFileDTO.getContentType())) {
                document.voiceMailUrl = messengerMediaFileDTO.getUrl();
            } else {
                if(CollectionUtils.isNotEmpty(mediaFiles)) {
                    MediaFile mediaFile = mediaFiles.get(0);
                    document.a_contype = mediaFile.getA_contype();
                    document.a_size = mediaFile.getA_size();
                    document.a_name = mediaFile.getA_name();
                    document.a_url = mediaFile.getA_url();
                    document.a_ext = mediaFile.getA_ext();
                    document.mediaFiles = mediaFiles;
                }else if(messengerMediaFileDTO != null) {
                    document.a_contype = messengerMediaFileDTO.getContentType();
                    document.a_size = messengerMediaFileDTO.getContentSize();
                    document.a_name = messengerMediaFileDTO.getName();
                    document.a_url = messengerMediaFileDTO.getUrl();
                    document.a_ext = messengerMediaFileDTO.getFileExtension();
                    MediaFile mediaFile = new MediaFile(messengerMediaFileDTO.getFileExtension(),
                            messengerMediaFileDTO.getUrl(), messengerMediaFileDTO.getContentSize(),
                            messengerMediaFileDTO.getName(), messengerMediaFileDTO.getContentType());
                    document.mediaFiles = Collections.singletonList(mediaFile);
                }
            }
            return this;
        }

        public Builder addEventType(MessengerEvent messengerEvent) {
            document.msg_type = messengerEvent.toString();
            return this;
        }

        public Builder addEId(Integer eId) {
            document.e_id = eId;
            return this;
        }

        public Builder addSpam(boolean spam) {
            document.spam = spam;
            return this;
        }
        
        public MessageDocument build() {
            return document;
        }

        public Builder addNoteInfo(MessageDocumentDTO messageDocumentDTO) {
            if(messageDocumentDTO.getMessageType().equals(MessageType.INTERNAL_NOTES)) {
                document.noteState=messageDocumentDTO.getNoteState();
                document.noteType=messageDocumentDTO.getNoteType();
            }
            return this;
        }

        public Builder addBusinessId(Integer businessId) {
            document.b_id = businessId;
            return this;
        }

        public Builder addSecureFaq(boolean secureFaq) {
            document.secureFaq = secureFaq;
            return this;
        }

        public Builder addClientIp(MessageDocumentDTO messageDocumentDTO) {
            if (messageDocumentDTO.getClientIp()!=null)
                document.clientIp = messageDocumentDTO.getClientIp();
            return this;
        }

        public Builder addEventProcessingTime(MessageDocumentDTO messageDocumentDTO) {
            if (messageDocumentDTO.getEventProcessingTimeStamp()!=null
                    && messageDocumentDTO.getTimeElapsedInProcessing()!=null) {
                document.eventProcessingTimeStamp = messageDocumentDTO.getEventProcessingTimeStamp();
                document.timeElapsedInProcessing = messageDocumentDTO.getTimeElapsedInProcessing();
            }
            return this;
        }

        public Builder addAnswerFlag(boolean answerFlag) {
            document.answerFlag = answerFlag;
            return this;
        }
    }


    @Getter
    public enum Type {
        CREATE("message_create"),
        UPDATE("message_update");

        String type;

        Type(String contactType) {
            this.type = contactType;
        }
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class MediaFile {
        private String a_ext;
        private String a_url;
        private String a_size;
        private String a_name;
        private String a_contype;
        private String msg_id;
        private String msg_status;

        public MediaFile(){}

        public MediaFile(String ext, String url, String contentLength, String name, String contentType) {
            this.a_contype = contentType;
            this.a_ext = ext;
            this.a_size = contentLength;
            this.a_name = name;
            this.a_url = url;
        }

        public MediaFile(MessengerMediaFile messengerMediaFile) {
            this.a_contype = messengerMediaFile.getContentType();
            this.a_size = messengerMediaFile.getContentSize();
            this.a_name = messengerMediaFile.getName();
            this.a_url = messengerMediaFile.getUrl();
            this.a_ext=FilenameUtils.getExtension(messengerMediaFile.getName());
        }

        public MediaFile(MessengerMediaFile messengerMediaFile,String fileExtension,String id) {
            this.a_contype = messengerMediaFile.getContentType();
            this.a_size = messengerMediaFile.getContentSize();
            this.a_name = messengerMediaFile.getName();
            this.a_url = messengerMediaFile.getUrl();
            this.a_ext=fileExtension;
            this.msg_id=id;
            this.msg_status="success";
        }

        public MediaFile(MessengerMediaFile messengerMediaFile,String fileExtension) {
            this.a_contype = messengerMediaFile.getContentType();
            this.a_size = messengerMediaFile.getContentSize();
            this.a_name = messengerMediaFile.getName();
            this.a_url = messengerMediaFile.getUrl();
            this.a_ext=fileExtension;
        }
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class UserDetail {
        Integer userId;
        String userName;

        public UserDetail() {
        }

        public UserDetail(Integer userId, String userName) {
            this.userId = userId;
            this.userName = userName;
        }

    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class CustomerInfo {
        Integer cid;
        String cname;
        Integer mcId;

        public CustomerInfo() {
        }

        public CustomerInfo(Integer cid, String cname, Integer mcId) {
            this.cid = cid;
            this.cname = cname;
            this.mcId = mcId;
        }

    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class QuestionAndAnswer implements Serializable {
        /**
         *
         */
        private static final long serialVersionUID = 1L;
        String question;
        String answer;

        public QuestionAndAnswer() {
        }

        public QuestionAndAnswer(String question, String answer) {
            this.question = question;
            this.answer = answer;
        }

    }

    public enum NoteState {
        CREATED, UPDATED, DELETED
    }

    public enum NoteType {
        INFO, WARNING, REMINDER
    }

    public enum MessageType {
        RICH_CONTENT_CHAT, CHAT, INTERNAL_NOTES, EVENTS, ACTIVITY, REVIEW, SURVEY_RESPONSE, RICH_LINK, ROBIN_SUGGESTION, WA_CONTACT, WA_LOCATION
    }

    public enum  CommunicationDirection {
        SEND, RECEIVE
    }

    public enum SentThrough {
        WEB, MOBILE, EMAIL_REPLY
    }

    public enum Channel {
        WEB_CHAT, FACEBOOK, SMS, CAMPAIGN, LIVE_CHAT, VOICE_CALL, EMAIL, QUICK_SEND,
        REFERRAL, REVIEW, SURVEY_RESPONSE, GOOGLE, INSTAGRAM, CONTACT_US, APPOINTMENT,
        APPLE, SECURE_MESSAGE, TWITTER, WHATSAPP, CUSTOM;
    }
    public enum RobinResponseType {
        SUGGESTION,DEFAULT,FAQ,INTENT,GPT
    }
    public enum UnansweredFaqType {
        UNANSWERED,UNANSWERED_QUALIFIED,ANSWERED,DELETED
    }
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SurveyDetail {
        private Integer surveyResponseId;
        private Integer surveyId;
        private String surveyName;
        private Long respDate;
        private Float overallScore;
    }

    @Override
    @JsonIgnore
    public Object getId() {
        String suffix=MessengerUtil.getMessageTypeSuffix(messageType, source);
        StringBuilder sb=new StringBuilder();
        sb.append(m_id);
        if(StringUtils.isNotBlank(suffix)) {
            sb.append(suffix);
        }
        return sb.toString();
    }
    
    @Data
    @AllArgsConstructor
    public static class BusinessDetail {
        private Integer businessId;
        private String businessName;
        private Integer mcId;
    }
    
    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }
}

