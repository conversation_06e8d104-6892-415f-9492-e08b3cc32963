package com.birdeye.messenger.dto.elastic;

import java.io.Serializable;
import java.util.List;

import com.birdeye.messenger.enums.AppleChatSessionEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AppleContextDocument implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String apple_conversation_id;
    private List<String> capabilityList;
    private Boolean appleOptOut;
    private AppleChatSessionEnum appleChatSession;
    private Boolean welcomeMessageSent;
	
}
