package com.birdeye.messenger.dto.elastic;

import java.io.Serializable;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import com.birdeye.messenger.util.SecureString;
import com.birdeye.messenger.util.SecureStringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.AppointmentEventDto;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CommunicationPreferencesDto;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.EmailPreferencesDto;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.SmsPreferencesDto;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.payment.CurrencyConstant;
import com.birdeye.messenger.enums.AppointmentActionEventType;
import com.birdeye.messenger.enums.AppointmentSource;
import com.birdeye.messenger.enums.AppointmentStatus;
import com.birdeye.messenger.enums.ContactState;
import com.birdeye.messenger.enums.ConversationView;
import com.birdeye.messenger.enums.LeadSource;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.PaymentStatus;
import com.birdeye.messenger.enums.ReferralSource;
import com.birdeye.messenger.sro.ReviewEvent;
import com.birdeye.messenger.sro.SurveyEvent;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.MessengerUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ContactDocument implements Identifiable, Serializable {

	private static final long serialVersionUID = -6986937186709689627L;
	
	private Integer b_id;
    private Integer e_id;
    private Long b_num;
    private String b_name;
    private String b_alias;
    private Integer c_tag;
    private Integer sms_on;
    private Integer c_id;
    private String c_name;
    @SecureString
    private String c_phone;
    @SecureString
    private String c_wa_phone;
    @SecureString
    private String c_email;
    private String l_msg;
    private String l_msg_on;
    private String type;
    private String unsubs_type;
    private Integer is_encrypted;
    private String imageUrl;
    private Integer m_c_id;
    private Integer lastMsgSource;
    private String cr_date;
    private Integer cr_asgn_id;
    private String  cr_asgn_name;
    private Integer  team_id;
    private String  team_name;
    private String assignmentType; //U -> User, T -> Team, null -> User/no-assignment 
    private String updatedAt;
    private LastMessageMetaData lastMessageMetaData;
    private List<MessageDocument> c_messages;
    private Byte import_status;

    private Integer lastMessageUserId;
    private String lastMessageType; // SEND, RECEIVE, INTERNAL_NOTE
    private String lastMessageUserName;
    private VideoContextDocument recentVideoConversationContext;
	private String emailUnsubcribedReason;
	private Integer templateId;
    private List<Integer> viewedBy;
	private Boolean c_read;
	//customer sentiment: BIRDEYE-71735
	private Integer sentimentScore;
    private String sentiment;
    private String google_conversation_id;
    private String instagram_conversation_id;
    private String facebook_conversation_id;
    private String twitter_conversation_id;
    // ---------------------- referrer info ------------
    private AppleContextDocument  appleContextDocument;
    private Boolean referrer;
    // -------------------------------------------------

    // ---------------------- custom channel info ------
    private String customChannel;
    // -------------------------------------------------

    // ------------------- lead info ---------------------------
    private Boolean lead;
    private ReferralSource referralSource;
    private LeadSource leadOrigin;
    private ContactState contactState;
    // --------------------------------------------------------

    // ------------------- flag to hide which conversation to hide from UI -----------
    // Earlier we did it on the basis of l_msg field (if its empty then we filter out those conversations
    // Now we are showing  lead conversations even if they are empty hence we need a flag to drive that.
    private boolean hide;
    // --------------------------------------------------------
    
    private List<Review> reviews;
    private Long l_review_on;
    private String l_rvr_name;

    // ------------------------------------- payments ------------------------------
    private List<Payment> payments;
    private List<Appointment> appointments;
    private Long l_payment_on;
    private Long l_appointment_activity_on;
    // ------------------------------------------------------------------------------
    
    private List<SurveyResponse> surveyResponses;
    private Long l_survey_rsp_on;

    private Boolean ongoingPulseSurvey;

    private Boolean blocked;
    
    private Long lastUpdateDate;

    private Boolean spam;

    private Integer spamStatusMarkedBy;
    
    private Integer latestSurveyScore;

    //---------RobinSessionDetails----------------------------------------------------
    private RobinSessionDocument robinSessionDocument;
    private Boolean isActive;

    /*** Performance Improvement ***/
    @JsonProperty("lastIncomingMessageTimeAllChannel")
    private Long lastIncomingMessageTime;

    private String liveChatSourceDevice;
    
    private CommunicationPreferencesDto commPreferences;

    public void setLastUpdateDate() {
	    	Date now = new Date();
	    	this.lastUpdateDate = now.getTime();
    }
    
    public ContactDocument() {
        this.type = Type.CREATE.getType();
    }

    public void setLastMessageMetaData(LastMessageMetaData lastMessageMetaData) {
        //LastMessageMetaData clone = SerializationUtils.clone(lastMessageMetaData);
        //LastMessageMetaData clone = (LastMessageMetaData) lastMessageMetaData.clone();
        //clone.setPayment(null);
        LastMessageMetaData lm = new LastMessageMetaData();
        lm.setLastFbReceivedAt(lastMessageMetaData.getLastFbReceivedAt());
        lm.setLastMessageChannel(lastMessageMetaData.getLastMessageChannel());
        lm.setLastMessageCustomChannel(lastMessageMetaData.getLastMessageCustomChannel());
        lm.setLastMessageType(lastMessageMetaData.getLastMessageType());
        lm.setLastReceivedMessageSource(lastMessageMetaData.getLastReceivedMessageSource());
        lm.setLastMessageUserId(lastMessageMetaData.getLastMessageUserId());
        lm.setLastMessageUserName(lastMessageMetaData.getLastMessageUserName());
        lm.setLastRespondedAt(lastMessageMetaData.getLastRespondedAt());
        lm.setLastMessageSource(lastMessageMetaData.getLastMessageSource());
        lm.setLastIgReceivedAt(lastMessageMetaData.getLastIgReceivedAt());
        lm.setLastIgSentAt(lastMessageMetaData.getLastIgSentAt());
        lm.setLastFbSentAt(lastMessageMetaData.getLastFbSentAt());
        lm.setL_payment_on(lastMessageMetaData.getL_payment_on());
        if(Objects.nonNull(lastMessageMetaData.getLastMessageDeletedBy())){
            lm.setLastMessageDeletedBy(lastMessageMetaData.getLastMessageDeletedBy());
            lm.setLastMessageDeleted(lastMessageMetaData.getLastMessageDeleted());
        }
        this.lastMessageMetaData = lm;
//        setLastMessageMetaData(lastMessageMetaData,false);
    }

    public void setLastMessageMetaData(LastMessageMetaData lastMessageMetaData,Boolean isDelete) {
        //LastMessageMetaData clone = SerializationUtils.clone(lastMessageMetaData);
        //LastMessageMetaData clone = (LastMessageMetaData) lastMessageMetaData.clone();
        //clone.setPayment(null);
        LastMessageMetaData lm = new LastMessageMetaData();
        lm.setLastFbReceivedAt(lastMessageMetaData.getLastFbReceivedAt());
        lm.setLastMessageChannel(lastMessageMetaData.getLastMessageChannel());
        lm.setLastMessageType(lastMessageMetaData.getLastMessageType());
        lm.setLastReceivedMessageSource(lastMessageMetaData.getLastReceivedMessageSource());
        lm.setLastMessageUserId(lastMessageMetaData.getLastMessageUserId());
        lm.setLastMessageUserName(lastMessageMetaData.getLastMessageUserName());
        lm.setLastRespondedAt(lastMessageMetaData.getLastRespondedAt());
        lm.setLastMessageSource(lastMessageMetaData.getLastMessageSource());
        lm.setLastIgReceivedAt(lastMessageMetaData.getLastIgReceivedAt());
        lm.setLastIgSentAt(lastMessageMetaData.getLastIgSentAt());
        lm.setLastFbSentAt(lastMessageMetaData.getLastFbSentAt());
        lm.setL_payment_on(lastMessageMetaData.getL_payment_on());
        if(BooleanUtils.isTrue(isDelete)){
            lm.setLastMessageDeletedBy(lastMessageMetaData.getLastMessageDeletedBy());
            lm.setLastMessageDeleted(true);
        }else{
            lm.setLastMessageDeletedBy(null);
        }
        this.lastMessageMetaData = lm;
    }

    public static class Builder {

        private ContactDocument document;

        public Builder(ContactDocument document) {
            this.document = document;
        }

        public Builder addBusinessInfo(BusinessDTO businessDTO) {
            if(businessDTO != null) {
                this.document.setB_alias(businessDTO.getBusinessAlias());
                this.document.setB_id(businessDTO.getBusinessId());
                this.document.setB_name(businessDTO.getBusinessName());
                this.document.setB_num(businessDTO.getBusinessNumber());
                this.document.setE_id(businessDTO.getRoutingId());
            }
            return this;
        }

        public Builder addCustomerInfo(CustomerDTO customerDTO) {
            if(Objects.nonNull(customerDTO)){
                this.document.setC_id(customerDTO.getId());
                this.document.setC_email(customerDTO.getEmailId());
                this.document.setC_name(MessengerUtil.buildCustomerName(customerDTO));
                this.document.setC_phone(customerDTO.getPhone());
				this.document.setSentimentScore(customerDTO.getSentimentScore());
                this.document.setSentiment(customerDTO.getSentiment());
                this.document.setLeadOrigin(customerDTO.getLeadSource());
                this.document.setLead(customerDTO.isLead());
                this.document.setContactState(customerDTO.getContactState());
                this.document.setBlocked(customerDTO.getBlocked());
                this.document.setSpam(customerDTO.getBlocked());
                if(Objects.nonNull(customerDTO.getIsActive())) {
                	this.document.setIsActive(customerDTO.getIsActive());
                }
                this.document.setCustomChannel(customerDTO.getCustomChannel());
                if (StringUtils.isNotBlank(customerDTO.getCustomerWAPhoneNumber())) {
                	this.document.setC_wa_phone(customerDTO.getCustomerWAPhoneNumber());
                }
				// CommPreferences
				setCommunicationPreferences(customerDTO);
            }
            return this;
        }

		private void setCommunicationPreferences(CustomerDTO customerDTO) {
			if (customerDTO.getCommPreferences() != null) {
				CommunicationPreferencesDto customerPreferences = customerDTO.getCommPreferences();
				CommunicationPreferencesDto contactPreferences = new CommunicationPreferencesDto();
				
				//TODO remove these
				//contactPreferences.setSmsOptin(customerPreferences.getSmsOptin());
				//contactPreferences.setSmsUnsubType(StringUtils.isBlank(customerPreferences.getSmsUnsubType()) ? ""
				//		: customerPreferences.getSmsUnsubType());
				
				if (customerPreferences.getSmsPreferences() != null) {
				    SmsPreferencesDto customerSmsPreferences = customerPreferences.getSmsPreferences();
				    SmsPreferencesDto contactSmsPreferencesDto = new SmsPreferencesDto();
				    contactSmsPreferencesDto.setMarketingOptin(customerSmsPreferences.getMarketingOptin());
				    contactSmsPreferencesDto.setMarketingOptoutType(
				        StringUtils.isBlank(customerSmsPreferences.getMarketingOptoutType()) ? "" : customerSmsPreferences.getMarketingOptoutType());
				    contactSmsPreferencesDto.setFeedbackOptin(customerSmsPreferences.getFeedbackOptin());
				    contactSmsPreferencesDto.setFeedbackOptoutType(
				        StringUtils.isBlank(customerSmsPreferences.getFeedbackOptoutType()) ? "" : customerSmsPreferences.getFeedbackOptoutType());
				    contactSmsPreferencesDto.setServiceOptin(customerSmsPreferences.getServiceOptin());
				    contactSmsPreferencesDto.setServiceOptoutType(
				        StringUtils.isBlank(customerSmsPreferences.getServiceOptoutType()) ? "" : customerSmsPreferences.getServiceOptoutType());
				    contactPreferences.setSmsPreferences(contactSmsPreferencesDto);
				}
				
				if (customerPreferences.getEmailPreferences() != null) {
					EmailPreferencesDto customerEmailPreferences = customerPreferences.getEmailPreferences();
					EmailPreferencesDto contactEmailPreferencesDto = new EmailPreferencesDto();
					contactEmailPreferencesDto.setMarketingOptin(customerEmailPreferences.getMarketingOptin());
					contactEmailPreferencesDto.setMarketingOptoutType(
							StringUtils.isBlank(customerEmailPreferences.getMarketingOptoutType()) ? ""
									: customerEmailPreferences.getMarketingOptoutType());
					contactEmailPreferencesDto.setFeedbackOptin(customerEmailPreferences.getFeedbackOptin());
					contactEmailPreferencesDto.setFeedbackOptoutType(
							StringUtils.isBlank(customerEmailPreferences.getFeedbackOptoutType()) ? ""
									: customerEmailPreferences.getFeedbackOptoutType());
					contactEmailPreferencesDto.setServiceOptin(customerEmailPreferences.getServiceOptin());
					contactEmailPreferencesDto.setServiceOptoutType(
							StringUtils.isBlank(customerEmailPreferences.getServiceOptoutType()) ? ""
									: customerEmailPreferences.getServiceOptoutType());
					contactPreferences.setEmailPreferences(contactEmailPreferencesDto);
				}
				contactPreferences.setWhatsappOptin(customerPreferences.getWhatsappOptin());
				contactPreferences.setWhatsappUnsubType(StringUtils.isBlank(customerPreferences.getWhatsappUnsubType()) ? ""
						: customerPreferences.getWhatsappUnsubType());
				contactPreferences.setIsPhoneAndWhatsAppNumberSame(customerPreferences.getIsPhoneAndWhatsAppNumberSame());				
				this.document.commPreferences = contactPreferences;
			}
		}
        //add experience score in case of unattributed reviews BIRDEYE-81068
        public Builder addSentimentDetails(CustomerDTO customerDTO) {
            if(Objects.nonNull(customerDTO)){
                this.document.setSentimentScore(customerDTO.getSentimentScore());
                this.document.setSentiment(customerDTO.getSentiment());
            }
            return this;
        }

        public Builder editCustomerInfo(CustomerDTO customerDTO) {
            if(Objects.nonNull(customerDTO)){
                String name = "";
                if(StringUtils.isNotBlank(customerDTO.getFirstName())) {
                    name += customerDTO.getFirstName();
                }
                if(StringUtils.isNotBlank(customerDTO.getLastName())) {
                    name += " " + customerDTO.getLastName();
                }
                if(StringUtils.isNotBlank(name)) {
                    this.document.setC_name(name);
                }
                this.document.setC_id(customerDTO.getId());
                this.document.setC_email(customerDTO.getEmailId());
                this.document.setC_phone(customerDTO.getPhone());
				// CommPreferences
				setCommunicationPreferences(customerDTO);
				
            }
            return this;
        }

        public Builder addMessageTag(MessageTag tag) {
            this.document.c_tag = tag!=null?tag.getCode():null;
            return this;
        }

        public Builder addMessengerContactInfo(MessengerContact messengerContact) {
            this.document.setM_c_id(messengerContact.getId());
            this.document.setL_msg(StringUtils.isNotBlank(messengerContact.getLastMessage()) ? messengerContact.getLastMessage() : "");
            DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
            this.document.cr_date = df.format(messengerContact.getCreatedAt());
            if(messengerContact.getLastMsgOn()!=null) {
            	this.document.setL_msg_on(df.format(messengerContact.getLastMsgOn()));
            }
            this.document.setUpdatedAt(df.format(messengerContact.getUpdatedAt()));
            this.document.setIs_encrypted(this.document.getIs_encrypted());
            this.document.setImageUrl(messengerContact.getImageUrl());
            this.document.setIs_encrypted(messengerContact.getEncrypted());
            Long l_payment_on = null;
			if (StringUtils.isNotEmpty(messengerContact.getLastMessageMetaData())) {
				LastMessageMetaData lastMessageMetaData = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
				this.document.setLastMessageMetaData(lastMessageMetaData);
				this.document.setLastMsgSource(lastMessageMetaData.getLastMessageSource() != null ? lastMessageMetaData.getLastMessageSource() : 1);
                List<Payment> payments = lastMessageMetaData.getPayment();
                if(CollectionUtils.isNotEmpty(payments)) {
                    this.document.setPayments(payments);
                    this.document.setL_payment_on(lastMessageMetaData.getL_payment_on());
                    l_payment_on = lastMessageMetaData.getL_payment_on();
                }
            }
            this.document.setCr_asgn_id(messengerContact.getCurrentAssignee()!=null?messengerContact.getCurrentAssignee():Constants.Elastic.UNASSIGNED_ID);
            this.document.setCr_asgn_name(StringUtils.isNotBlank(messengerContact.getCurrentAssigneeName())?messengerContact.getCurrentAssigneeName():Constants.Elastic.UNASSIGNED_NAME);
            this.document.setTeam_id(messengerContact.getTeamId()!=null?messengerContact.getTeamId():Constants.Elastic.UNASSIGNED_ID);
            this.document.setTeam_name(StringUtils.isNotBlank(messengerContact.getTeamName())?messengerContact.getTeamName():Constants.Elastic.UNASSIGNED_NAME);
            this.document.setAssignmentType(messengerContact.getAssignmentType()!=null?messengerContact.getAssignmentType().name():"U");
            this.document.setImport_status(messengerContact.getImportStatus());
			if (messengerContact.getTemplateId() != null) {
				this.document.setTemplateId(messengerContact.getTemplateId());
			}
			addReffererInfo(messengerContact);

            this.document.hide = (StringUtils.isBlank(messengerContact.getLastMessage()) && messengerContact.getLastReviewDate()==null &&  messengerContact.getLastSurveyDate()==null && l_payment_on==null);
            // no last message present hence hide, only exception is REFERRAL LEAD
            if(Boolean.TRUE.equals(messengerContact.getLead()) && Objects.nonNull(messengerContact.getLeadSource())
                    && messengerContact.getLeadSource().equals(LeadSource.REFERRAL)) {
                this.document.hide = false;
            }
            this.document.setFacebook_conversation_id(messengerContact.getFacebookId());
            this.document.setGoogle_conversation_id(messengerContact.getGoogleConversationId());
            this.document.setInstagram_conversation_id(messengerContact.getInstagramConversationId());
            this.document.setTwitter_conversation_id(messengerContact.getTwitterConversationId());
            if (messengerContact.getSpamMarkedBy() != null){
                this.document.setSpam(messengerContact.getSpam());
                this.document.setBlocked(messengerContact.getBlocked());
                this.document.setSpamStatusMarkedBy(messengerContact.getSpamMarkedBy());
            }else{
                this.document.setBlocked(false);
                this.document.setSpam(false);
            }
            if(Objects.nonNull(messengerContact.getLastIncomingMessageTime())){
                this.document.setLastIncomingMessageTime(messengerContact.getLastIncomingMessageTime());
            }
            if(StringUtils.isNotBlank(messengerContact.getDevice())){
                this.document.setLiveChatSourceDevice(messengerContact.getDevice());
            }
            return this;
        }

        public Builder addLastMessageSenderDetail(UserDTO userDTO, String messageType) {
            this.document.lastMessageType = messageType;
            if(Objects.nonNull(userDTO)) {
                this.document.lastMessageUserId = userDTO.getId();
                this.document.lastMessageUserName = MessengerUtil.buildUserName(userDTO);
            }
            return this;
        }
        
        public ContactDocument build() {
            return this.document;
        }

		public Builder addViewedStatus(MessengerContact messengerContact) {
			this.document.viewedBy = StringUtils.isNotBlank(messengerContact.getViewedBy())
					? MessengerUtil.convertCommaSeparatedStringToList(messengerContact.getViewedBy())
					: new ArrayList<>();
			// updating c_read to isRead value set in message contact or false everytime a
			// new messages is exchanged
			this.document.c_read = messengerContact.getIsRead() != null ? messengerContact.getIsRead() : false;
			return this;
		}

        public Builder addReffererInfo(MessengerContact messengerContact) {
            if(messengerContact != null) {
                this.document.setReferrer(messengerContact.getReferrer());
                this.document.setReferralSource(messengerContact.getReferralSource());
            }
            return this;
        }


        public Builder addReviewDetails(ReviewEvent.Review review) {
            Review review1 = new Review();
            review1.setAsstd_by(review.getAssistedBy());
            review1.setCmnt(review.getComment());
            review1.setFeatured(review.getFeatured());
            review1.setId(review.getReviewId());
            review1.setRcmded(review.getRecommended());
            review1.setRdate(review.getReviewDate());
            review1.setRespnded(review.getIsResponded());
            review1.setRtng(Objects.isNull(review.getRating())? 0:review.getRating() );
            review1.setRvr_name(review.getReviewerName());
            review1.setShrd(review.getIsShared());
            review1.setS_id(review.getSourceId());
            review1.setStatus(review.getStatus());
            review1.setTag_ids(review.getTagIds());
            review1.setTcktd(review.getIsTicketed());
            review1.setUpdtd(review.getIsUpdated());
            review1.setRvr_id(review.getReviewerId());
            List<Review> reviews = new ArrayList<>();
            reviews.add(review1);
            this.document.setReviews(reviews);
            return this;
        }

        public Builder addSurveyDetails(SurveyEvent.After surveyEvent,String surveyName,Long event_time) {
            SurveyResponse surveyResponse = new SurveyResponse();
            surveyResponse.setId(surveyEvent.getId());
            surveyResponse.setSurveyName(surveyName);
            surveyResponse.setEvent_time(event_time);
            if (surveyEvent.getOverallScore() != null){
                DecimalFormat decimalFormat = new DecimalFormat("#.#");
                surveyResponse.setOverallScore(Float.valueOf(decimalFormat.format(surveyEvent.getOverallScore())));
            }
            surveyResponse.setRespDate(surveyEvent.getResponseDate().getTime());
            surveyResponse.setSurveyId(surveyEvent.getSurveyId());
            List<SurveyResponse> surveyResponses = new ArrayList<>();
            surveyResponses.add(surveyResponse);
            this.document.setSurveyResponses(surveyResponses);
            return this;
        }
        
        public Builder addPulseSurveyStatus(CustomerDTO customerDTO) {
        	if (customerDTO != null && customerDTO.getOngoingPulseSurvey() != null) {
        		Boolean ongoingPulseSurvey = customerDTO.getOngoingPulseSurvey();
        		this.document.setOngoingPulseSurvey(ongoingPulseSurvey);
        	}
            return this;
        }

        public Builder addAppointmentInfo(AppointmentEventDto appointmentEventDto, UserDTO userDTO) {
            Appointment appointment =  new Appointment();
            appointment.setSource(appointmentEventDto.getSource());
            appointment.setAction(appointmentEventDto.getAction());
            appointment.setAppointmentRequestId(appointmentEventDto.getAppointmentId());
            appointment.setSpecialistName(appointmentEventDto.getSpecialistName());
            appointment.setStartTime(appointmentEventDto.getStartTime());
            appointment.setUserId(appointmentEventDto.getUserId());
            if(userDTO != null) {
                appointment.setUserName(MessengerUtil.buildUserName(userDTO));
            }
            appointment.setCreatedAt(appointmentEventDto.getEventTime());
            appointment.setUpdatedAt(appointmentEventDto.getEventTime());
            appointment.setStatus(appointmentEventDto.getStatus());
            List<Appointment> appointments = new ArrayList<>();
            appointments.add(appointment);
            this.document.setAppointments(appointments);
            return this;
        }
    }

    @Getter
    public enum Type {
        CREATE("contact_create"),
        UPDATE("contact_update");

        String type;

        Type(String contactType) {
            this.type = contactType;
        }
    }
    
    public boolean assignedToUser() {
    	return (StringUtils.isBlank(assignmentType) || Constants.Assignment.USER_ASSIGNED.equalsIgnoreCase(assignmentType)) && cr_asgn_id!=null && cr_asgn_id > 0 ;
    }
    
    public boolean assignedToTeamOnly() {
    	return Constants.Assignment.TEAM_ASSIGNED.equalsIgnoreCase(assignmentType) && team_id!=null;
    }
    
    public boolean unassigned() {
    	return (StringUtils.isBlank(assignmentType) || Constants.Assignment.USER_ASSIGNED.equals(assignmentType)) && (Constants.Assignment.UNASSIGNED_ID.equals(cr_asgn_id) || cr_asgn_id==null);
    }
    
    public boolean unassignedOrUserAssigned() {
    	return unassigned() || assignedToUser();
    }
    
    public boolean notInAnyTeamBucket() {
    	return team_id == null || team_id < 0;
    }

    @JsonIgnore
    public Long get_l_msg_on_epoch() {
		DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date parsedUTCDate = null;
		try {
			parsedUTCDate = df.parse(getL_msg_on());
			return parsedUTCDate.getTime();
		} catch (ParseException e) {
			log.error("Error occured while parsing date:{}",l_msg_on);
		}
		return null;
	}
    
    @JsonIgnore
    public Optional<RecentContent> getRecentReviewContent() {
    	RecentContent content = new RecentContent();
		if(CollectionUtils.isEmpty(reviews)) {
			return Optional.empty();
		} else {
			Optional<Review> recentReview = reviews.stream().max(Comparator.comparingLong(Review::getRdate));
			content.setType(ContentType.REVIEW);
    		content.setEpoch(recentReview.get().getRdate());
    		content.setText(recentReview.get().getProcessedComment());
    		content.setEncrypted(0);
    		content.setContent(recentReview.get());
			return Optional.of(content);
			
		}
    }
    
    @JsonIgnore
	public Optional<RecentContent> getRecentSurveyResponseContent() {
		if (CollectionUtils.isEmpty(surveyResponses)) {
			return Optional.empty();
		} else {
			Optional<SurveyResponse> recentSurveyResponse = surveyResponses.stream()
					.max(Comparator.comparingLong(SurveyResponse::getRespDate));
			RecentContent content = new RecentContent();
			content.setType(ContentType.SURVEY_RESPONSE);
    		content.setEpoch(recentSurveyResponse.get().getRespDate());
    		content.setText(recentSurveyResponse.get().getSurveyName());
    		content.setEncrypted(0);
    		content.setContent(recentSurveyResponse.get());
    		return Optional.of(content);
		}
	}
	
    @JsonIgnore
	public Optional<RecentContent> getRecentMessageContent() {
		if (StringUtils.isEmpty(l_msg_on) || StringUtils.isEmpty(l_msg)) {
			return Optional.empty();
		} else {
			RecentContent content = new RecentContent();
			Long recentMessageDate = get_l_msg_on_epoch();
			String inboxPerformanceImprovementEnabledAccount = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("accounts_with_inbox_performance_improvement", "261968");
			List<String> inboxPerformanceImprovementEnabledAccountList = ControllerUtil.getTokensListFromString(inboxPerformanceImprovementEnabledAccount);
			if (e_id != null && inboxPerformanceImprovementEnabledAccountList.contains(String.valueOf(e_id))) {
				if (Objects.nonNull(lastIncomingMessageTime)) {
					recentMessageDate = lastIncomingMessageTime;
				}
			}
			content.setType(ContentType.MESSAGE);
    		content.setEpoch(recentMessageDate);
    		content.setText(l_msg);
    		content.setEncrypted(is_encrypted);
    		return Optional.of(content);
		}
	}
    
    @JsonIgnore
    public Optional<RecentContent> getRecentContentByType(ConversationView view, boolean reviewsAccess, boolean surveysAccess) {
    	if(view == ConversationView.REVIEW) {
    		return getRecentReviewContent();
    	} else if(view == ConversationView.SURVEY) {
    		return getRecentSurveyResponseContent();
    	} else if(view == ConversationView.MESSAGE) {
    		return getRecentMessageContent();
    	}else if(view == ConversationView.PAYMENT) {
    		return getRecentPaymentContent();
    	}else if(view == ConversationView.APPOINTMENT) {
    		return getRecentAppointmentContent();
    	} else {
    		Optional<RecentContent> recentReviewContent = getRecentReviewContent();
    		Optional<RecentContent> recentPaymentContent = getRecentPaymentContent();
    		Optional<RecentContent> recentSurveyResponseContent = getRecentSurveyResponseContent();
    		Optional<RecentContent> recentMessageContent = getRecentMessageContent();
    		Optional<RecentContent> recentAppointmentContent = getRecentAppointmentContent();
    		List<RecentContent> recentContents=new ArrayList<ContactDocument.RecentContent>();
    		if(reviewsAccess && recentReviewContent.isPresent()) {
    			recentContents.add(recentReviewContent.get());
    		}
    		if(surveysAccess && recentSurveyResponseContent.isPresent()) {
    			recentContents.add(recentSurveyResponseContent.get());
    		}
    		if(recentMessageContent.isPresent()) {
    			recentContents.add(recentMessageContent.get());
    		}
    		if(recentPaymentContent.isPresent()) {
    			recentContents.add(recentPaymentContent.get());
    		}
    		if(recentAppointmentContent.isPresent()) {
    			recentContents.add(recentAppointmentContent.get());
    		}
    		if(CollectionUtils.isNotEmpty(recentContents)) {
    			Collections.sort(recentContents, (o1, o2) -> (o1.getEpoch() == null || o2.getEpoch() == null) ? -1
    					: o2.getEpoch().compareTo(o1.getEpoch()));
    			return Optional.of(recentContents.get(0));
    		}
    	}
    	return Optional.empty();
    }
    
    @JsonIgnore
    public Optional<RecentContent> getRecentPaymentContent() {
    	RecentContent content = new RecentContent();
		if(CollectionUtils.isEmpty(payments)) {
			return Optional.empty();
		} else {
			Optional<Payment> recentPayment = payments.stream().max(Comparator.comparingLong(Payment::getUpdatedAt));
			content.setType(ContentType.PAYMENT);
    		content.setEpoch(recentPayment.get().getUpdatedAt());
    		content.setText(getRecentPaymentText(recentPayment.get()));
    		content.setStatus(recentPayment.get().getStatus()!=null?recentPayment.get().getStatus().name():null);
    		content.setEncrypted(0);
			return Optional.of(content);
			
		}
    }

    @JsonIgnore
    public Optional<RecentContent> getRecentAppointmentContent() {
        RecentContent content = new RecentContent();
        if (CollectionUtils.isEmpty(appointments)) {
            return Optional.empty();
        }
        Optional<Appointment> recentAppointment = appointments.stream()
                .max(Comparator.comparingLong(Appointment::getUpdatedAt));
        content.setType(ContentType.APPOINTMENT);
        content.setEpoch(recentAppointment.get().getUpdatedAt());
        if (AppointmentSource.MICROSITE.getValue().equals(recentAppointment.get().getSource())) {
            content.setText(recentAppointment.get().getCustomerComment());
        }
        content.setText(getRecentAppointmentText(recentAppointment.get()));
        content.setStatus(
                recentAppointment.get().getAction() != null ? recentAppointment.get().getAction() : null);
        content.setEncrypted(0);
        return Optional.of(content);

    }
    private String getRecentAppointmentText(Appointment appointment) {
        String result="";
        SimpleDateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
        //to be discussed later
        String date = "$date";
//        String date = DateUtils.convertDate(new Date(appointment.getStartTime()));
        String specialistName= appointment.getSpecialistName();
        if(AppointmentStatus.SUCCESS.getValue().equals(appointment.getStatus())) {
            if(AppointmentActionEventType.BOOKED.getValue().equals(appointment.getAction())) {
                result="Appointment is booked for " +date+ (specialistName != null && !specialistName.isEmpty() ? " with " + specialistName : "");
            }
            else if(AppointmentActionEventType.CONFIRMED.getValue().equals(appointment.getAction())) {
                result="Appointment is confirmed for " +date+ (specialistName != null && !specialistName.isEmpty() ? " with " + specialistName : "");
            }
            else if(AppointmentActionEventType.RESCHEDULED.getValue().equals(appointment.getAction())) {
                result="Appointment has been rescheduled to " +date;
            }
            else if(AppointmentActionEventType.CANCELED.getValue().equals(appointment.getAction())) {
                result="Appointment is canceled for "+date+ (specialistName != null && !specialistName.isEmpty() ? " with " + specialistName : "");
            }
            else if(AppointmentActionEventType.COMPLETED.getValue().equals(appointment.getAction())) {
                result="Appointment of "+date+ (specialistName != null && !specialistName.isEmpty() ? " with " + specialistName : "")+ " is completed" ;
            }
        }
        else if (AppointmentStatus.REQUESTED.getValue().equals(appointment.getStatus())){
        	if(AppointmentActionEventType.BOOKED.getValue().equals(appointment.getAction())) {
        		result="Appointment booking requested for " +date+ (specialistName != null && !specialistName.isEmpty() ? " with " + specialistName : "");
        	}
        	else if(AppointmentActionEventType.RESCHEDULED.getValue().equals(appointment.getAction())) {
        		result="Appointment reschedule requested to " +date+ (specialistName != null && !specialistName.isEmpty() ? " with " + specialistName : "");
        	}
        	else if(AppointmentActionEventType.CANCELED.getValue().equals(appointment.getAction())) {
        		result="Appointment cancellation requested for "+date+ (specialistName != null && !specialistName.isEmpty() ? " with " + specialistName : "");
        	}
        }
        else {
            if(AppointmentActionEventType.BOOKED.getValue().equals(appointment.getAction())) {
                result="Appointment booking failed for " +date+ (specialistName != null && !specialistName.isEmpty() ? " with " + specialistName : "");
            }
            else if(AppointmentActionEventType.CONFIRMED.getValue().equals(appointment.getAction())) {
                result="Appointment confirmation failed for " +date+ (specialistName != null && !specialistName.isEmpty() ? " with " + specialistName : "");
            }
            else if(AppointmentActionEventType.RESCHEDULED.getValue().equals(appointment.getAction())) {
                result="Appointment reschedule failed for " +date+ (specialistName != null && !specialistName.isEmpty() ? " with " + specialistName : "");
            }
            else if(AppointmentActionEventType.CANCELED.getValue().equals(appointment.getAction())) {
                result="Appointment cancellation failed for "+date+ (specialistName != null && !specialistName.isEmpty() ? " with " + specialistName : "");
            }
        }
        return result;
	}

	private String getRecentPaymentText(Payment payment) {
		String currencySymbol = Objects.nonNull(payment.getCurrency()) ? payment.getCurrency().getCurrencySymbol() : CurrencyConstant.USD.getCurrencySymbol();
		String result="";
    	String amount=MessengerUtil.getFormattedAmount(payment.getAmount(), Optional.empty());
    	if(PaymentStatus.NOT_PAID.equals(payment.getStatus()) || PaymentStatus.INCOMPLETE.equals(payment.getStatus())) {
    		result="Payment request of "+currencySymbol+amount+ " is sent";
    	}
    	else if(PaymentStatus.PAYMENT_CANCELLED.equals(payment.getStatus())) {
    		result="Payment request canceled";
    	}
    	else if(PaymentStatus.COMPLETED.equals(payment.getStatus())) {
    		result="Payment of "+currencySymbol+amount+ " received";
    	}
    	else if(PaymentStatus.MARKED_AS_PAID.equals(payment.getStatus())) {
    		result="Payment request of "+currencySymbol+amount+ " is marked as paid";
    	}
    	else if(PaymentStatus.PAYMENT_FAILED.equals(payment.getStatus())) {
    		result="Payment of "+currencySymbol+amount+ " failed";
    	}
    	else if(PaymentStatus.FAILED.equals(payment.getStatus())) {
    		result="Payment of "+currencySymbol+amount+ " failed";
    	}
    	else if(PaymentStatus.REFUND_PENDING.equals(payment.getStatus())) {
    		result="Refund "+currencySymbol+amount+ " initiated";
    	}
    	else if(PaymentStatus.PARTIAL_REFUND_PENDING.equals(payment.getStatus())) {
    		result="Partial refund "+currencySymbol+amount+ " initiated";
    	}
    	else if(PaymentStatus.FULLY_REFUNDED.equals(payment.getStatus())) {
    		result="Payment refund of "+currencySymbol+amount+ " is sent";
    	}
    	else if(PaymentStatus.PARTIALLY_REFUNDED.equals(payment.getStatus())) {
    		result="Partial refund of "+currencySymbol+amount+ " is sent";
    	}
    	else if(PaymentStatus.REFUND_FAILED.equals(payment.getStatus())) {
    		result="Refund of "+currencySymbol+amount+ " failed";
    	}
    	else if(PaymentStatus.REFUND_CANCELLED.equals(payment.getStatus())) {
    		result="Refund request canceled";
    	}
    	else if(PaymentStatus.PROCESSING.equals(payment.getStatus())) {
    		result="Payment of "+currencySymbol+amount+ " has been initiated";
    	}
    	else if(PaymentStatus.UPDATE_PAYMENT_METHOD.equals(payment.getStatus())) {
    		result="Payment method has been updated";
    	}
		return result;
	}
	@Override
	@JsonIgnore
	public Object getId() {
		StringBuilder sb = new StringBuilder();
		sb.append(m_c_id);
		return sb.toString();
	}

	@Data
    public static class Review {
    	private Integer id;
    	private Float rtng;
    	private Integer s_id;
    	private Boolean rcmded;
    	private String rvr_name;
    	private String cmnt;
    	private Integer asstd_by;
    	private List<Integer> tag_ids;
    	private Boolean respnded;
    	private Boolean shrd;
    	private Boolean tcktd;
    	private Integer status;
    	private Boolean updtd;
    	private Integer featured;
    	private Long rdate;
    	private Integer rvr_id;
    	
        @JsonIgnore
    	public String getProcessedComment() {
    		return StringUtils.isEmpty(cmnt)?Constants.MESSAGING.REVIEW_WITHOUT_COMMENT:cmnt;
    	}
    }
    	
	
	@Data
    public static class SurveyResponse {
    	private Integer id;
    	private Integer surveyId;
    	private String surveyName;
    	private Long respDate;
    	private Long event_time;
    	private Float overallScore;
    }
	
	public static enum ContentType { 
		REVIEW, SURVEY_RESPONSE, MESSAGE, ALL,PAYMENT,APPOINTMENT
	}
	
	@Data
	public static class RecentContent {
		private ContentType type;
		private String text;
		private Long epoch;
		private Integer encrypted;
		private Object content;
		private String status;
	}

	@Data
	public static class Payment implements Cloneable {
        String paymentId;
        Double amount;
        PaymentStatus status;
        String text;
        Long createdAt;
        Long updatedAt;
        String method;
        CurrencyConstant currency;



        @Override
        public Object clone() throws CloneNotSupportedException {
            return super.clone();
        }
    }

    @Data
    public static class Appointment implements Cloneable {
        Long appointmentRequestId;
        String action;
        String status;
        String specialistName;
        String source;
        Long userId;
        String customerComment;
        String userName;
        Long startTime;
        Long createdAt;
        Long updatedAt;


        @Override
        public Object clone() throws CloneNotSupportedException {
            return super.clone();
        }
    }

    @Override
    public String toString() {
        return SecureStringUtil.buildSecureToString(this);
    }
}