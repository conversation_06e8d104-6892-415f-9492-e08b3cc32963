/**
 * 
 */
package com.birdeye.messenger.dto.secure.messaging;

import java.io.Serializable;

import com.birdeye.messenger.util.SecureString;
import com.birdeye.messenger.util.SecureStringUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SecureLinkInfoResponse implements Serializable {

    private String businessName;

    private String logoUrl;

    @SecureString
    private String customerPhoneNumber;

    @Override
    public String toString() {
        return SecureStringUtil.buildSecureToString(this);
    }

}
