/**
 * 
 */
package com.birdeye.messenger.dto.secure.messaging;

import java.io.Serializable;
import java.text.ParseException;
import java.util.Date;
import java.util.List;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.util.MessengerUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetSecureMessagesResponse implements Serializable {

    private List<SecureMessageResponse> messages;

    private Long count;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Slf4j
    public static class SecureMessageResponse implements Serializable {
        private String id;
        private Long sentAtUTC;
        private MessageDocument.UserDetail createdBy;
        private String status;
        private String content;
        private String subject;
        private Integer source;
        private MessageDocument.MessageType messageType;
        private List<MessageDocument.MediaFile> mediaFiles;
        private MessageDocument.CommunicationDirection direction;
        private MessageDocument.Channel channel;
        private MessageDocument.SentThrough sentThrough;
        private String m_id;
        private String type; // See MessageDocument.Type field to identify whether this document is updated
                             // or not e.g case of Referral activity

        public SecureMessageResponse(MessageDocument messageDocument) {
            this.id = messageDocument.getM_id()
                    + MessengerUtil.getMessageTypeSuffix(messageDocument.getMessageType(), messageDocument.getSource());
            this.channel = messageDocument.getChannel();
            this.content = MessengerUtil.decryptMessage(messageDocument);
            Date createdDate = null;
            if (messageDocument.getCr_time() != null)
                createdDate = new Date(messageDocument.getCr_time()); // server time zone is UTC from epoch time
            else if (messageDocument.getCr_date() != null) {
                try {
                    createdDate = Constants.SERVER_TIME_FORMATTER.parse(messageDocument.getCr_date()); // server time
                                                                                                       // zone is UTC
                                                                                                       // from date
                                                                                                       // string
                } catch (ParseException e) {
                    log.error("Exception in parsing Date for messageDoc {} ", messageDocument);
                }
            }
            if (createdDate != null) {
                this.sentAtUTC = createdDate.getTime();
            }
            this.createdBy = messageDocument.getCreatedBy();
            this.direction = messageDocument.getCommunicationDirection();
            this.m_id = messageDocument.getM_id();
            this.type = messageDocument.getType();
            this.mediaFiles = messageDocument.getMediaFiles();
            this.sentThrough = messageDocument.getSentThrough();
            this.source = messageDocument.getSource();
            this.subject = messageDocument.getMsg_subject();
        }
    }

}
