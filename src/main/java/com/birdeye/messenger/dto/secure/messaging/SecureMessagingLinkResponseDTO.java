/**
 * 
 */
package com.birdeye.messenger.dto.secure.messaging;

import java.io.Serializable;

import com.birdeye.messenger.dao.entity.secure.messaging.SecureMessagingLink;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SecureMessagingLinkResponseDTO implements Serializable {

    private Integer id;

    private Integer mcId;

    private Integer cId;

    private Integer accountId;

    private Integer businessId;

    public SecureMessagingLinkResponseDTO(
            SecureMessagingLink messengerContactSecureMessagingLink) {
        this.id = messengerContactSecureMessagingLink.getId();
        this.mcId = messengerContactSecureMessagingLink.getMcId();
        this.cId = messengerContactSecureMessagingLink.getCId();
        this.accountId = messengerContactSecureMessagingLink.getAccountId();
        this.businessId = messengerContactSecureMessagingLink.getBusinessId();
    }

}
