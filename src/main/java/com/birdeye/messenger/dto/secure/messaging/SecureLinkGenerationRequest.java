/**
 * 
 */
package com.birdeye.messenger.dto.secure.messaging;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

/**
 * <AUTHOR>
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SecureLinkGenerationRequest implements Serializable {

    @NonNull
    private Integer mcId;

    private Integer source = 1;

    private Integer accountId;

    private Integer userId;

    private boolean sentThroughAddAndSend=true;

}
