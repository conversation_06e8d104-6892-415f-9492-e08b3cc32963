/**
 * 
 */
package com.birdeye.messenger.dto.secure.messaging;

import java.io.Serializable;
import java.util.Date;

import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.secure.messaging.SecureMessagingLink;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SecureMessagingLinkDTO implements Serializable {

    private MessengerContact messengerContact;

    private Integer cId;

    private Integer businessId;

    private Integer accountId;

    private Date createdAt = new Date();

    private Integer createdBy;

    public SecureMessagingLinkDTO(
            SecureLinkGenerationRequest secureLinkGenerationRequest, MessengerContact messengerContact) {
        this.messengerContact = messengerContact;
        this.cId = messengerContact.getCustomerId();
        this.businessId = messengerContact.getBusinessId();
        this.accountId = secureLinkGenerationRequest.getAccountId();
        this.createdBy = secureLinkGenerationRequest.getUserId();
    }

    public SecureMessagingLink getMessengerContactSecureMessagingLink() {
        SecureMessagingLink messengerContactSecureMessagingLink = new SecureMessagingLink();
        messengerContactSecureMessagingLink.setCId(cId);
        messengerContactSecureMessagingLink.setMessengerContact(messengerContact);
        messengerContactSecureMessagingLink.setMcId(messengerContact.getId());
        messengerContactSecureMessagingLink.setAccountId(accountId);
        messengerContactSecureMessagingLink.setBusinessId(businessId);
        messengerContactSecureMessagingLink.setCreatedAt(createdAt);
        messengerContactSecureMessagingLink.setCreatedBy(createdBy);
        messengerContactSecureMessagingLink.setUpdatedAt(createdAt);
        messengerContactSecureMessagingLink.setUpdatedBy(createdBy);
        messengerContactSecureMessagingLink.setExpiryTime(null);
        return messengerContactSecureMessagingLink;

    }

}
