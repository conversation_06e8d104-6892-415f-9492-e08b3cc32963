/**
 * 
 */
package com.birdeye.messenger.dto.secure.messaging;

import com.birdeye.messenger.dto.MessageResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SecureMessageReceiveResponse extends MessageResponse {

    private Integer id;

    private Long createdAt;

}
