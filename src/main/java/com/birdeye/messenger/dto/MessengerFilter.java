package com.birdeye.messenger.dto;

import java.util.List;
import java.util.Map;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import lombok.Data;

@Data
@JsonInclude(Include.NON_NULL)
public class MessengerFilter extends MessangerBaseFilter {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private List<String> sources;
	// for query specific params
	private Map<String, Object> params;
	// date format to be used with startDate and endDate params
	// will be used when startDate and endDate is non-null
	private String dateFormat = Constants.FORMAT_YYYY_MM_DD_HH_MM_SS;
	private List<Integer> businessIds;
	private String order;
	private String lastMsgTime;
	private Integer teamId;
	private Integer tag;
	private Integer operation;
	private String searchTerm;
	private Integer moveConversationToTop;
	private Integer startIndex;
	private Integer count;
	 private Boolean markRead = true ;
	private Integer enterpriseId;
	private Integer messengerEnabled;
	private List<Integer> mcIds;
	private List<String> messageDocumentIds;
	private String documentIds;

	/**
	 * @see MessageDocument.MessageType
	 */
	private List<String> messageTypes;
	private Integer reviewId;
	private boolean secureFaq;
}
