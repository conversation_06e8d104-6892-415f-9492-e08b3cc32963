package com.birdeye.messenger.dto.report;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ByTimeDownloadReportDTO {

    private Integer receivedMessages;
    private Integer activeConversations;
    private Double responseTime;
    private String date;

    public ByTimeDownloadReportDTO(String date) {
        this.date = date;
    }
}
