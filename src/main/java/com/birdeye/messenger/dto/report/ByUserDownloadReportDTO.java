package com.birdeye.messenger.dto.report;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ByUserDownloadReportDTO {

    private Integer activeConversations;
    private Double responseTime;
    private Integer userId;
    private String userName;

    public ByUserDownloadReportDTO(Integer userId, String userName) {
        this.userId = userId;
        this.userName = userName;
    }
}
