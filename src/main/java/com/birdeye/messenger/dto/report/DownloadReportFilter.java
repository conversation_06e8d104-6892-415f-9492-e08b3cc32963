package com.birdeye.messenger.dto.report;

import com.birdeye.messenger.annotations.CustomerTypeSubset;
import com.birdeye.messenger.annotations.ListValidator;
import com.birdeye.messenger.enums.ContactState;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Slf4j
public class DownloadReportFilter {
    private List<Integer> businessIds;
    private Integer accountId;
    private DownloadFilter filter;
    private Integer startIndex;
    private Integer size;
    private String viewType;
    private String accountTimeZoneId;
    private String timezoneId; //accountTimeZoneId from Doup
    //NPS filters - BIRDEYEV2-5104
    @CustomerTypeSubset(anyOf = {ContactState.LEAD, ContactState.CUSTOMER},message = "Customer type must be only LEAD or CUSTOMER")
    private ContactState customerType;
    @ListValidator(message = "Experience scores cannot be less than 0 or greater than 10")
    private List<Integer> experienceScores;
    @ListValidator(message = "Survey scores cannot be less than 0 or greater than 10")
    private List<Integer> surveyScores;
    private boolean unansweredFaq;
    
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Slf4j
    public static class DownloadFilter {
        private List<Integer> userIds;
        private List<String> sources;
        private String startDate;
        private String endDate;
        private boolean lastOneYear;
        private String interval;
        private Long startTimeEpoch;
        private Long endTimeEpoch;
    }
}
