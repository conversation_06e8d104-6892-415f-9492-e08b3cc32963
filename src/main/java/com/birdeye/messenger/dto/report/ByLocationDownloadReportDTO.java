package com.birdeye.messenger.dto.report;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ByLocationDownloadReportDTO {

    private Integer receivedMessages;
    private Integer activeConversations;
    private Double responseTime;
    private String locationName;
    private Integer locationId;
    private Integer businessId;
    private Integer nodeId;

    public ByLocationDownloadReportDTO(String locationName, Integer businessId) {
        this.locationName = locationName;
        this.businessId = businessId;
    }

    public Integer getNodeId() {
        return businessId;
    }

}
