package com.birdeye.messenger.dto.instagram;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class InstagramUserDetailsMessage implements Serializable{

	private static final long serialVersionUID = 1L;
	
	private String id;
	private String name;
	private String profile_pic;
	
}
