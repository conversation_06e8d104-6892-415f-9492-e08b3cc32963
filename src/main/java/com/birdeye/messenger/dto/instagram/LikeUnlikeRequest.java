package com.birdeye.messenger.dto.instagram;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
public class LikeUnlikeRequest implements Serializable{

    private static final long serialVersionUID = 1L;
    
    
    private boolean like;
    private String messageId;
    private String source;
    private Integer messengerContactId;
    private Integer businessId;
    private Integer userId;
    private Integer accountId;
    private boolean publishSocialEvent;
}
