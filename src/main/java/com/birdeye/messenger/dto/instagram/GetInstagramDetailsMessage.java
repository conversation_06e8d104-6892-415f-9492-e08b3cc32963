package com.birdeye.messenger.dto.instagram;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.Setter;
@JsonIgnoreProperties(ignoreUnknown = true)
@Setter
@Getter
public class GetInstagramDetailsMessage implements Serializable{

	private static final long serialVersionUID = 1L;
	
	private String userId;
	private String accountId;
	
	public GetInstagramDetailsMessage() {}

	public GetInstagramDetailsMessage(String userId, String accountId) {
		super();
		this.userId = userId;
		this.accountId = accountId;
	}
	
	
	
}
