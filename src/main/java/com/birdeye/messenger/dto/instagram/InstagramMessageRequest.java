package com.birdeye.messenger.dto.instagram;

import java.io.Serializable;
import java.util.List;

import com.birdeye.messenger.dto.ConversationDTO;
import com.birdeye.messenger.dto.MessageDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.ToString;

@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class InstagramMessageRequest extends MessageDTO implements Serializable{
    private static final long serialVersionUID = 1L;
    private String object;
    private List<Entry> entry;
    private ConversationDTO conversationDTO;
    
    public String getObject() {
        return object;
    }

    public void setObject(String object) {
        this.object = object;
    }

    public List<Entry> getEntry() {
        return entry;
    }

    public void setEntry(List<Entry> entry) {
        this.entry = entry;
    }
    
    
    public InstagramMessageRequest() {}

	public ConversationDTO getConversationDTO() {
		return conversationDTO;
	}

	public void setConversationDTO(ConversationDTO conversationDTO) {
		this.conversationDTO = conversationDTO;
	}
    
}
