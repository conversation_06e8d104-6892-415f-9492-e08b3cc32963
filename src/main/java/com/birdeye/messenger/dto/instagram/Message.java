package com.birdeye.messenger.dto.instagram;

import java.io.Serializable;
import java.util.List;

import com.birdeye.messenger.dto.facebook.Attachments;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;
import lombok.ToString;

@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
@Data
public class Message implements Serializable{
    private static final long serialVersionUID = 1L;
    private String mid;
    private int seq;
    private String text;
    private List<Attachments> attachments;
    private Boolean is_echo=false;
    private Boolean is_unsupported=false;
    private Boolean is_deleted=false;
    private String app_id;
    private ReplyTo reply_to;
    private Referral referral;
}
