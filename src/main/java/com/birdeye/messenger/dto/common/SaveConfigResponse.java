package com.birdeye.messenger.dto.common;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SaveConfigResponse {
    
    private Long id;
    private String status; // "created" or "updated"
    
    
	public SaveConfigResponse(String status) {
		super();
		this.status = status;
	}
    
    
}
