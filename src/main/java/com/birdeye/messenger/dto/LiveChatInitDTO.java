package com.birdeye.messenger.dto;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;

/**
 * 
 * <AUTHOR>
 *
 */
@Getter
@Setter
public class LiveChatInitDTO extends ChatDTO implements Serializable {

	private static final long serialVersionUID = 8761370462769042943L;

	// id of the webchat widget in use
	private Integer widgetConfigId;
	
	//init message body for integrations
	private String  messageBody;

}
