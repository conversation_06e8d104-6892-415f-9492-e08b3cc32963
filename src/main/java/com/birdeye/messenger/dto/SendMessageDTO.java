package com.birdeye.messenger.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dto.apple.chat.AppleAttachments;
import com.birdeye.messenger.enums.AppleInteractiveMessageType;
import com.birdeye.messenger.util.SecureString;
import com.birdeye.messenger.util.SecureStringUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.ToString;


@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class SendMessageDTO extends MessageDTO implements Serializable {
	// Messenger Contact Id
	private String toCustomerId;
	private String toBusinessUserId;
	// Customer Id of associated Messenger Contact
	private Integer customerId;
	private Integer fromBusinessId;
	private String body;
	private String plainTextRobinResponse;
	private String mediaurl;
	private Integer templateId;
	private Long requestId;
	private Integer surveyId;
	private List<MessengerMediaFile> mediaUrls = new ArrayList<>();
	@SecureString
	private String toPhone;
	@SecureString
	private String fromPhone;
	private Integer source;
	private String customChannel;
	private Integer encrypted;
	private UserDTO userDTO;
	private String templateType;
	private CustomerDTO customerDTO;
	private ConversationDTO conversationDTO;
	private String emailSubject;
	private Integer userId;
	@SecureString
	private String replyToEmailId;
	private String businessIdentifierId;
	private String templateName;
	@SecureString
	private String toEmailId;
	@SecureString
	private String fromEmailId;
	private String surveyType;
	private Boolean ongoingPulseSurvey;
	private String paymentRequestEmailBody; // only for email request since link is a button in email request and field body above has both link and text.
	private List<AppleAttachments> appleMediaUrls; // only for apple send request
	private List<String> multiMessages;
	Boolean updateLastResponseAt;
	private AppleInteractiveMessageType appleInteractiveMessageType;
	private List<String> mediaIds;
	private String mediaId;
	// for local use only
	private BusinessDTO businessDTO;
	private String type;
	private String socialFeedId;
	private Boolean isGlobalTemplate=false;
	@NotEmpty(message ="User don't have access to any account/subaccount")
	private List<Integer> businessIds;
	private Boolean isStopUnsubscribeTextPresent = false; //internal field

	private String waMsgHeader;
	private String waMsgFooter;

	public SendMessageDTO() {
	}

	public SendMessageDTO(String body, Integer fromBusinessId) {
		this.body = body;
		this.fromBusinessId = fromBusinessId;
	}

	@Override
	public String toString() {
		return SecureStringUtil.buildSecureToString(this);
	}
}
