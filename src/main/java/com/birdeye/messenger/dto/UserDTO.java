package com.birdeye.messenger.dto;

import java.io.Serializable;
import java.util.List;

import com.birdeye.messenger.util.SecureString;
import com.birdeye.messenger.util.SecureStringUtil;
import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
public class UserDTO implements Serializable{

    /**
	 * 
	 */
	private static final long serialVersionUID = -4876395998504209777L;

	private Integer id;
    private String firstName;
    private String lastName;
    @SecureString
    private String emailId;
    private String name;
    @SecureString
    private String phone;
    private boolean enterpriseUser;
	private List<Integer> locationIds;
    private Integer status;
	
	public UserDTO(Integer id) {
		this.id = id;
	}

    public String getName() {
        if(name == null){
            if(StringUtils.isBlank(firstName) &&
                    StringUtils.isBlank(lastName) &&
                    StringUtils.isNotBlank(emailId)){
                name = emailId.substring(0, emailId.indexOf("@"));
            }else if(StringUtils.isNotBlank(firstName) ||
                StringUtils.isNotBlank(lastName)){
                name = firstName;
                if(StringUtils.isNotBlank(lastName)){
                    if(StringUtils.isBlank(name)){
                        name = lastName;
                    }else{
                        name = name + " " + lastName;
                    }
                }
            }
        }
        return name;
    }

	public String formatUserDtoForLua() {
		return id+":"+status;
	}

    @Override
    public String toString() {
        return SecureStringUtil.buildSecureToString(this);
    }
}
