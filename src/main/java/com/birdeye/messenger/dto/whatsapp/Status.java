package com.birdeye.messenger.dto.whatsapp;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

/**
 * <AUTHOR>
 *
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class Status implements Serializable {

	private static final long serialVersionUID = 1L;
	
	String id; //wa message id
	String status;
	public List<Error> errors;
	
	@Data
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class Error {
	    public int code;
	    public String title;
	    public String message;
	    public ErrorData error_data;
	    public String href;
	    
	    @Data
	    @JsonIgnoreProperties(ignoreUnknown = true)
	    public static class ErrorData {
	        public String details;
	    }
	}
	
}
