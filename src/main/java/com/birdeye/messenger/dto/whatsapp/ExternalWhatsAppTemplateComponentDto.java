package com.birdeye.messenger.dto.whatsapp;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import lombok.Data;

/**
 * checkout whatsApp template components:- {@link https://developers.facebook.com/docs/whatsapp/business-management-api/message-templates/components}
 * 
 * <AUTHOR>
 */
@Data
public class ExternalWhatsAppTemplateComponentDto implements Serializable {
	
	private static final long serialVersionUID = 1L;

	// template component type - HEADER, BODY, FOOTER or BUTTONS
	private String type;
	
	// attached media type - in case of HEADER, this could be IMAGE, VIDEO, or DOCUMENT
	private String format;
	
	// main message body
	private String text;
	
	// button list
	private List<Map<String, Object>> buttons;
	
	// example for parameters/tokens
	private Map<String, List<Object>> example;

}
