package com.birdeye.messenger.dto.whatsapp;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

/**
 * <AUTHOR>
 *
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class Value implements Serializable {

	private static final long serialVersionUID = 1L;

	private String messaging_product; //whatsapp
	private Metadata metadata;
	private List<Contact> contacts;
	private List<Message> messages;
	private List<Status> statuses;

}
