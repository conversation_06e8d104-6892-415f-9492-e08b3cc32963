package com.birdeye.messenger.dto.whatsapp;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

/**
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class Entry implements Serializable {

	private static final long serialVersionUID = 1L;

	private String id; //WHATSAPP_BUSINESS_ID
	private List<Change> changes;

}
