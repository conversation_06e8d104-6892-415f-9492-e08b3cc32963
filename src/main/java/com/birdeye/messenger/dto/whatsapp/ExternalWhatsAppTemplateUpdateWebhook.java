package com.birdeye.messenger.dto.whatsapp;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * dummy event -
 * https://birdeye.atlassian.net/browse/BIRD-128944?focusedCommentId=784642
 */
@Data
public class ExternalWhatsAppTemplateUpdateWebhook implements Serializable {

	private static final long serialVersionUID = 1L;

	private String object;
	private List<ExternalWhatsAppTemplateUpdateDto> entry;

	@Data
	public static class ExternalWhatsAppTemplateUpdateDto implements Serializable {

		private static final long serialVersionUID = 1L;

		// waba id
		private String id;
		
		//template update time
		private long time;
		private List<ExternalWhatsAppTemplateChangeDto> changes;
	}

	@Data
	public static class ExternalWhatsAppTemplateChangeDto implements Serializable {

		private static final long serialVersionUID = 1L;

		private String field;
		private ExternalWhatsAppTemplateValueDto value;

	}

	@Data
	public static class ExternalWhatsAppTemplateValueDto implements Serializable {

		private static final long serialVersionUID = 1L;

		private String event;
		private String message_template_id;
		private String message_template_name;
		private String message_template_language;
		private String message_template_element;
		private String reason;
		
		private String previous_category;
		private String new_category;
		
		private String previous_quality_score;
		private String new_quality_score;

	}
}