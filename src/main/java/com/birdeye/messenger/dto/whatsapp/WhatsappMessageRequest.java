package com.birdeye.messenger.dto.whatsapp;

import java.io.Serializable;
import java.util.List;

import com.birdeye.messenger.dto.ConversationDTO;
import com.birdeye.messenger.dto.MessageDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

/**
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class WhatsappMessageRequest extends MessageDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	private String object; //whatsapp_business_account
	private List<Entry> entry;
	private ConversationDTO conversationDTO;

}
