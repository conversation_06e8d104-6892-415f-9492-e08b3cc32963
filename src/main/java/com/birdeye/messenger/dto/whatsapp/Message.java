package com.birdeye.messenger.dto.whatsapp;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

/**
 * <AUTHOR>
 *
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class Message  implements Serializable {

	private static final long serialVersionUID = 1L;

	private String from;
	private String id;
	private Long timestamp;
	private Text text;
	private String type; //text, image, video, document, contact, location, sticker, reaction
	private Media image;
	private Media video;
	private Media document;
	private Media audio;
	private Media sticker;
	private Location location;
	private List<Contact> contacts;
	private Reaction reaction;
	private Button button;

	@Data
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class Contact {
		private Name name;
		private String birthday;
		private List<Phone> phones;
		private List<Email> emails;

		@Data
		@JsonIgnoreProperties(ignoreUnknown = true)
		public static class Name {
			String formatted_name;
		}

		@Data
		@JsonIgnoreProperties(ignoreUnknown = true)
		public static class Phone {
			private String type; //HOME, WORK, MOBILE, MAIN
			private String phone;
		}

		@Data
		@JsonIgnoreProperties(ignoreUnknown = true)
		public static class Email {
			private String type; //HOME, WORK
			private String email;
		}

	}

	@Data
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class Location {
		private String latitude;
		private String longitude;
		private String name;
		private String address;
	}
	
	@Data
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class Reaction {
		private String emoji;
		private String message_id;
	}

}
