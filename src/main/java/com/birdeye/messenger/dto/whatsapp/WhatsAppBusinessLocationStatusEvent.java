package com.birdeye.messenger.dto.whatsapp;

import lombok.Data;

@Data
public class WhatsAppBusinessLocationStatusEvent {

	private Integer accountId;
	private Integer businessId;
	private String wabaId;
	private String wabaName;
	
	// location connection status - CONNECTED, DISCONNECTED, BANNED...
	private String waba_status;
	
	private String phoneNumberId;
	private boolean status;
	
	private Integer isBusinessVerified;
	private String messagingLimit;
	private String metaBusinessId;
	
	// event type - ONBOARD or MESSAGING_LIMIT_UPDATE
	private String type;
	
}
