package com.birdeye.messenger.dto.whatsapp;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.birdeye.messenger.dao.entity.whatsapp.WhatsAppTemplateTokens;
import com.birdeye.messenger.dto.CustomFieldDto;
import com.birdeye.messenger.util.LocaleUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonIgnoreProperties(ignoreUnknown=true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
public class WhatsAppTemplateDto implements Serializable {

	private static final long serialVersionUID = 1L;

	// template id
	private Integer id;
	private String name;
	private String templateType;
	
	private String type = "whatsapp";
	private String status;

	private String body;

	//same as body text
	private String description;

	private String language;
	private String wabaId;
	private String wabaName;
	private String media_url;

	private String headerFormat;
	private String headerMediaUrl;
	
	private String headerExtParameter;
	private String headerText;
	private TemplateHeaderLocationDto headerLocation;

	private String footerText;

	private Map<Integer, WACustomFieldDto> tokenMappings;

	private Object buttons;

	// token count present in body text
	private Integer tokenCount;

	private Long updatedOn; // in milliseconds

	private String parameterFormat; //POSITIONAL or NAMED
	private List<WhatsAppTemplateTokens> templatesToken;
	
	private CustomFieldDto templateHeaderToken;
	
	private String metaBusinessId;
	
	private List<Integer> businessIds;


	// used in getWhatsappTemplatesList
	public WhatsAppTemplateDto(Integer id, String wabaId, String wabaName, String templateName, String category, String bodyText, String language,
            String status, String headerMediaUrl, Date updatedOn, String metaBusinessId) {
		this.id = id;
		this.wabaId = wabaId;
		this.wabaName = wabaName;
		this.name = templateName;
		this.templateType = "whatsapp_" + (category.toLowerCase());
		this.description = bodyText;
		this.language = LocaleUtil.formatLocale(language);
		this.status = status;
		this.media_url = headerMediaUrl;
		this.updatedOn = updatedOn.getTime();
		this.metaBusinessId = metaBusinessId;
	}

	// used in get WhatsappTemplateById
	public WhatsAppTemplateDto(Integer id, String wabaName, String templateName, String category, String headerText, String bodyText, String language, String status, String headerFormat, 
			String headerMediaUrl, Object buttons, String footerText, Date updatedOn) {
		this.id = id;
		this.wabaName = wabaName;
		this.name = templateName;
		this.templateType = category;
		this.headerText = headerText;
		this.body = bodyText;
		this.language = language;
		this.status = status;
		this.headerFormat = headerFormat;
		this.headerMediaUrl = headerMediaUrl;
		this.buttons = buttons;
		this.footerText = footerText;
		this.updatedOn = updatedOn.getTime();
	}
	
	@Data
	public static class TemplateHeaderLocationDto {

		private String name;
		private String address;
		private Double latitude;
		private Double longitude;

	}

}
