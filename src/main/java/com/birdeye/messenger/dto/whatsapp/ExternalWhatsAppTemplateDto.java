package com.birdeye.messenger.dto.whatsapp;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

@Data
public class ExternalWhatsAppTemplateDto implements Serializable {

	private static final long serialVersionUID = 1L;

	// whatsapp template id
	private String id;
	
	// whatsapp business template name
	private String name;
	
	// parameter format - POSITIONAL, NAMED
	private String parameter_format;
	
	// whatsapp template component list
	private List<ExternalWhatsAppTemplateComponentDto> components;
	
	// whatsapp template language - en_IN, en_US, etc.
	private String language;
	
	// whatsapp template status - PENDING, APPROVED or REJECTED
	private String status;
	
	// whatsapp template category - MARKETING, AUTHENTICATION or UTILITY
	private String category;

}
