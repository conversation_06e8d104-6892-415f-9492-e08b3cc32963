package com.birdeye.messenger.dto;

import java.io.Serializable;
import java.util.List;

import com.birdeye.messenger.util.SecureString;
import com.birdeye.messenger.util.SecureStringUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * 
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ChatDTO extends MessageDTO implements Serializable {

	private static final long serialVersionUID = 4871851879559664814L;

	// Customer Details
	private String name;
	private String mobileNumber;
	private boolean customerTyping;
	// TODO: Email is removed from webchat widget form.
	@SecureString
	private String email;

	// Business Details
	private Long enterpriseNumber;
	private Long businessNumber;
	private String businessName;
	private String apiKey;
	private String domain;
	private String countryCode;
	private String pageUrlTrackedByGoogleAnalytics;

	// source details like website,microsite
	private Integer source;

	private Integer teamId;
	
	private Integer widgetConfigId;
	private Boolean emailMandatory;
	private List<WebChatCustomFieldDataRequest> customFieldsData;

	private Boolean anonymousConversation=false;
	private Boolean locationSwitch=false;
	private Integer fromMcId;
	private Integer fromBusinessId;
	private Boolean showPreChatForm = true;
	private Boolean existingContact = false;
	private String  customerLocation;

	private String device;

	private List<String> widgetAgent;
	private boolean hidden;

	@Override
	public String toString() {
		return SecureStringUtil.buildSecureToString(this);
	}

}
