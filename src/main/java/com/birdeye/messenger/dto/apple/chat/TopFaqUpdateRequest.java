/**
 * 
 */
package com.birdeye.messenger.dto.apple.chat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import com.birdeye.messenger.dao.entity.TopFAQs;
import com.birdeye.messenger.enums.IntentType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TopFaqUpdateRequest implements Serializable {

    @NotNull
    @NotEmpty
    @Size(max = 6, message = "FAQs should be more than 0 and less than 7")
    private List<FaqUpdateRequest> faqUpdateRequests;

    private Integer accountId;

    private Integer userId;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FaqUpdateRequest implements Serializable {

        @NotNull
        private Integer id;

        private Integer faqId;

        private IntentType intentType;

        @Min(value = 1)
        @Max(value = 9)
        private Integer priority;

        public void updateFaq(TopFAQs topFAQs, Integer accountId, Integer userId) {
            topFAQs.setAccountId(accountId);
            topFAQs.setFaqId(faqId);
            topFAQs.setIntentType(intentType);
            topFAQs.setPriority(priority);
            topFAQs.setUpdatedBy(userId);
            topFAQs.setUpdatedAt(new Date());
        }
        
    }

}
