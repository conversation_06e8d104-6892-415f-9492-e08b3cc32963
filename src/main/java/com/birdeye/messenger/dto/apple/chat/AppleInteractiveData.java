package com.birdeye.messenger.dto.apple.chat;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class AppleInteractiveData implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private InteractiveData data;
	
	@JsonIgnoreProperties(ignoreUnknown = true)
	@Data
	public static class InteractiveData implements Serializable{
		private static final long serialVersionUID = 1L;
		private ReplyMessage replyMessage;
		@JsonProperty(value = "quick-reply")
		private QuickReply quickReply;
	}
	@JsonIgnoreProperties(ignoreUnknown = true)
	@Data
	public static class ReplyMessage implements Serializable{
		private static final long serialVersionUID = 1L;
		private String title;
	}

	@JsonIgnoreProperties(ignoreUnknown = true)
	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	public static class QuickReply implements Serializable {
		private static final long serialVersionUID = 1L;
		private String selectedIdentifier;
	}
}
