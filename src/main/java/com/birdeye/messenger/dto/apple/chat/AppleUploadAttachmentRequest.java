package com.birdeye.messenger.dto.apple.chat;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class AppleUploadAttachmentRequest implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Integer messageId;
	private String imageUrl;
	private String decryptionKey;
	private String subBucket;
	private Long size;
	private String name;
	private String mimeType;
	
}
