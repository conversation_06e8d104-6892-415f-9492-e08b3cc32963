/**
 * 
 */
package com.birdeye.messenger.dto.apple.chat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import com.birdeye.messenger.dao.entity.TopFAQs;
import com.birdeye.messenger.enums.IntentType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TopFaqRequest implements Serializable {

    @NotNull
    @NotEmpty
    @Size(max = 6, message = "FAQs should be more than 0 and less than 7")
    private List<FaqRequest> faqRequests;

    private Integer accountId;

    private Integer userId;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class FaqRequest implements Serializable {

        private Integer faqId;

        @Min(value = 1)
        @Max(value = 6)
        private Integer priority;

        private IntentType intentType;

        public TopFAQs getTopFAQs(Integer accountId, Integer userId) {
            return TopFAQs.builder().accountId(accountId).createdAt(new Date()).faqId(faqId)
                    .createdBy(userId).intentType(intentType).priority(Objects.nonNull(priority) ? priority : 0)
                    .updatedAt(new Date()).updatedBy(userId).build();
        }

        public void updateTopFAQ(TopFAQs topFAQs, Integer accountId, Integer userId) {
            topFAQs.setAccountId(accountId);
            topFAQs.setFaqId(faqId);
            topFAQs.setIntentType(intentType);
            topFAQs.setPriority(priority);
            topFAQs.setUpdatedBy(userId);
            topFAQs.setUpdatedAt(new Date());
        }

    }

}
