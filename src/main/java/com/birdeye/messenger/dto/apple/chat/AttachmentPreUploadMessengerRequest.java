/**
 * 
 */
package com.birdeye.messenger.dto.apple.chat;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AttachmentPreUploadMessengerRequest implements Serializable {

	private static final long serialVersionUID = 1L;
	
	private String key;
	private Long size;

}
