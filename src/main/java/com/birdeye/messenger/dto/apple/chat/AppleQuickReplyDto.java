package com.birdeye.messenger.dto.apple.chat;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
public class AppleQuickReplyDto implements Serializable{/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Integer mcId;
	private Integer businessId;
	private Integer accountId;
	private Long lastMsgOn;

}
