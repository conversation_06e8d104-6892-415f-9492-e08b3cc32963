/**
 * 
 */
package com.birdeye.messenger.dto.apple.chat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import jakarta.validation.constraints.NotNull;

import com.birdeye.messenger.dao.entity.AppleLocationTeamMapping;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(value = Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AppleLocationTeamMappingRequest implements Serializable {

    private Integer accountId;

    private List<LocationTeamMapping> locationTeamMappings;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(value = Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Builder
    public static class LocationTeamMapping {

        @NotNull
        private Integer businessId;

        @NotNull
        private Integer teamId;

        public LocationTeamMapping(AppleLocationTeamMapping appleLocationTeamMapping) {
            this.businessId = appleLocationTeamMapping.getBusinessId();
            this.teamId = appleLocationTeamMapping.getTeamId();
        }

        public AppleLocationTeamMapping getAppleLocationTeamMapping(Integer accountId) {
            Date date = new Date();
            return AppleLocationTeamMapping.builder().accountId(accountId).businessId(businessId).teamId(teamId)
                    .createdAt(date).updatedAt(date).build();

        }

    }

}
