package com.birdeye.messenger.dto.apple.chat;

import java.util.ArrayList;
import java.util.List;

import com.birdeye.messenger.constant.Constants;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ListPickerInteractiveData extends AppleInteractiveMessageData {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private InteractiveData data=new InteractiveData();
	private ReceivedMessage receivedMessage=new ReceivedMessage();
	private ReplyMessage replyMessage=new ReplyMessage();
	
	@Data
	@JsonIgnoreProperties(ignoreUnknown = true)
	@JsonInclude(JsonInclude.Include.NON_NULL)
	@NoArgsConstructor
	public static class InteractiveData{
		private ListPicker listPicker=new ListPicker();
		private String mspVersion= "1.0";
		private String requestIdentifier;
        private List<ImageData> images = Constants.DEFAULT_IMAGES_DATA;
	}
	@Data
	@JsonIgnoreProperties(ignoreUnknown = true)
	@JsonInclude(JsonInclude.Include.NON_NULL)
	@NoArgsConstructor
	public static class ListPicker{
		private List<Section> sections=new ArrayList<ListPickerInteractiveData.Section>();
	}
	@Data
	@JsonIgnoreProperties(ignoreUnknown = true)
	@JsonInclude(JsonInclude.Include.NON_NULL)
	@NoArgsConstructor
	@AllArgsConstructor
	public static class Item{
		@JsonInclude(JsonInclude.Include.NON_NULL)
		private String identifier;
		@JsonInclude(JsonInclude.Include.NON_NULL)
		private Integer order;
		@JsonInclude(JsonInclude.Include.NON_NULL)
        private String style = "icon";
		@JsonInclude(JsonInclude.Include.NON_NULL)
		private String subtitle;
		@JsonInclude(JsonInclude.Include.NON_NULL)
		private String title;
        private String imageIdentifier = "2";

        public Item(String identifier, Integer order, String title) {
            this.identifier = identifier;
            this.order = order;
            this.title = title;
        }
	}
	@Data
	@JsonIgnoreProperties(ignoreUnknown = true)
	@NoArgsConstructor
	@AllArgsConstructor
	public static class Section{
		private List<Item> items=new ArrayList<ListPickerInteractiveData.Item>();
		@JsonInclude(JsonInclude.Include.NON_NULL)
		private Integer order;
		@JsonInclude(JsonInclude.Include.NON_NULL)
		private String title;
		@JsonInclude(JsonInclude.Include.NON_NULL)
		private boolean multipleSelection;
	}
	@Data
	@JsonIgnoreProperties(ignoreUnknown = true)
	@NoArgsConstructor
	@AllArgsConstructor
	public static class ReceivedMessage{
		@JsonInclude(JsonInclude.Include.NON_NULL)
        private String style;
		@JsonInclude(JsonInclude.Include.NON_NULL)
		private String subtitle;
		@JsonInclude(JsonInclude.Include.NON_NULL)
		private String title;
        private String imageIdentifier = "1";
		
	}
	@Data
	@JsonIgnoreProperties(ignoreUnknown = true)
	@NoArgsConstructor
	@AllArgsConstructor
	public static class ReplyMessage{
		@JsonInclude(JsonInclude.Include.NON_NULL)
        private String style;
		@JsonInclude(JsonInclude.Include.NON_NULL)
		private String title;
		@JsonInclude(JsonInclude.Include.NON_NULL)
		private String subtitle;
        private String imageIdentifier = "1";
	}

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ImageData {
        private String data;
        private String identifier;

    }
}
