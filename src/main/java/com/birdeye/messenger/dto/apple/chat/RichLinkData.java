/**
 * 
 */
package com.birdeye.messenger.dto.apple.chat;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RichLinkData implements Serializable {

	private static final long serialVersionUID = 1L;

	private String url;

	private String title;

	private Assets assets;

	@AllArgsConstructor
	@NoArgsConstructor
	@Data
	public static class Assets {
		private VideoAsset video;

		private ImageAsset image;

	}

	@AllArgsConstructor
	@NoArgsConstructor
	@Data
	public static class VideoAsset {
		private String url;

		private String mimeType;
	}

	@AllArgsConstructor
	@NoArgsConstructor
	@Data
	public static class ImageAsset {
		private String data;

		private String mimeType;
	}

}
