package com.birdeye.messenger.dto.apple.chat;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QuickReplyInteractiveData extends AppleInteractiveMessageData {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private InteractiveData data=new InteractiveData();	
	@Data
	@JsonIgnoreProperties(ignoreUnknown = true)
	@JsonInclude(JsonInclude.Include.NON_NULL)
	@NoArgsConstructor
	public static class InteractiveData{
		@JsonProperty("quick-reply")
		private QuickReply quickReply=new QuickReply();
		private String version= "1.0";
		private String requestIdentifier;
	}
	@Data
	@JsonIgnoreProperties(ignoreUnknown = true)
	@JsonInclude(JsonInclude.Include.NON_NULL)
	@NoArgsConstructor
	public static class QuickReply{
		private List<Item> items=new ArrayList<QuickReplyInteractiveData.Item>();
		private String summaryText;
	}
	@Data
	@JsonIgnoreProperties(ignoreUnknown = true)
	@JsonInclude(JsonInclude.Include.NON_NULL)
	@NoArgsConstructor
	@AllArgsConstructor
	public static class Item{
		private String identifier;
		private String title;
	}
}
