package com.birdeye.messenger.dto.apple.chat;

import java.io.Serializable;
import java.util.List;

import com.birdeye.messenger.dto.MessageDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;
import lombok.ToString;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class AppleUserMessage extends MessageDTO implements Serializable {
	private String body;
	private String sourceId;
	private String locale;
	private String intent;
	private String destinationId;
	private Integer v;
	private String type;
	private String id;
	private	List<String> capabilityList;
	private UserInfo userInfo;
	private	List<Attachment> attachments;
	private AppleInteractiveData interactiveData;
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class UserInfo {
        private String displayName;
        private String userDeviceLocale;
    }
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Attachment {
        private String mimeType;
        private String name;
        private String url;
        private String owner;
        private String key;
        private String signature;
        private Long size;
    }
   
}
