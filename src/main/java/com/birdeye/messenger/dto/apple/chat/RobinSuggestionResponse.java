/**
 * 
 */
package com.birdeye.messenger.dto.apple.chat;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;

import com.birdeye.messenger.external.dto.SuggestionHolder;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RobinSuggestionResponse implements Serializable {
	private static final long serialVersionUID = 1L;

	private String title;
	private String subTitle;
    private List<Suggestion> suggestions;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private class Suggestion implements Serializable {
        private String suggestion;
        private String imageUrl;
    }

	public RobinSuggestionResponse(SuggestionHolder suggestionHolder) {
		this.title = suggestionHolder.getQueryText();
		this.subTitle = suggestionHolder.getSubHeader();
		this.suggestions = CollectionUtils.isNotEmpty(suggestionHolder.getSuggestions())
                ? suggestionHolder.getSuggestions().stream().map(s -> new Suggestion(s.getValue(), s.getImageUrl()))
                        .collect(Collectors.toList())
				: null;
	}

}