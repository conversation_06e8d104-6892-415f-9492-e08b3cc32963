/**
 * 
 */
package com.birdeye.messenger.dto.apple.chat;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AttachmentPreUploadResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	private String mmcsUrl;
	private String uploadUrl;
	private String mmcsOwner;
}
