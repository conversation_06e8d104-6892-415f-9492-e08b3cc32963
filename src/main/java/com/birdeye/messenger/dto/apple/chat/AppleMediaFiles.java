package com.birdeye.messenger.dto.apple.chat;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
public class AppleMediaFiles {
	private String name;
	private String size;
	private String mimeType;
	@JsonProperty("signature-base64")
	private String signature;
	private String key;
	private String url;
	private String owner;
}
