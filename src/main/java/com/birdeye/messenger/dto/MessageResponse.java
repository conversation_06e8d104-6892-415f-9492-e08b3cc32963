package com.birdeye.messenger.dto;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.SortedSet;
import java.util.TreeSet;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.constant.MessengerConstants;
import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dto.apple.chat.RichLinkData;
import com.birdeye.messenger.dto.appointment.AppointmentInfo;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.MessageType;
import com.birdeye.messenger.dto.elastic.MessageDocument.SurveyDetail;
import com.birdeye.messenger.dto.payment.PaymentInfo;
import com.birdeye.messenger.enums.ActivityMeantFor;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.ContactInfoSourceEnum;
import com.birdeye.messenger.enums.ReferralSource;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.enums.StatusEnum;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.MessengerUtil;
import com.birdeye.messenger.util.TimeZoneUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MessageResponse {

    protected Integer mcId;
    protected Integer customerId;
    protected Integer businessId;
    protected SortedSet<Message> messages;
    protected boolean hasMore;
    protected String fbIntegrationStatus;
    protected boolean restrictFBReply;
    protected boolean isFBUserReachable;
    protected Integer lastReceivedMsgSource;
    protected Integer customMessagesCount=0;
	protected Integer lastMsgSource;
    protected String lastMsgCustomChannel;
    protected List<ViewedByUser> viewedByUsers;
    protected Boolean appleOptOut;

    protected boolean isGoogleUserReachable;
    protected String googleIntegrationStatus;
    protected boolean restrictGoogleReply;
    
    protected String instagramIntegrationStatus;
    protected String appleIntegrationStatus;
    protected boolean restrictInstagramReply;

    private Integer sentimentScore;
    private String sentiment;
    private Boolean ongoingPulseSurvey = Boolean.FALSE;
    private String textingNumber;
    private boolean onlyActivities=true;

    protected String twitterIntegrationStatus;
    protected boolean restrictWAReply;
    protected boolean isWAUserReachable;
    protected boolean verifiedBusiness;
    protected boolean wabaLimitReached;
    protected String metaBusinessId;
    protected boolean replyFromWAReceived;


    public MessageResponse(){}
    public MessageResponse(List<MessageDocument> messageDocuments, String timeZoneId, Integer messengerContactId, Boolean hasMore, Integer userId,String requestSource) {
        this.mcId = messengerContactId;
        this.messages = new TreeSet<>(getMessageComparator());
        this.hasMore = hasMore;
        final DateFormat serverTimeFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (MessageDocument doc : messageDocuments) {
            if(StringUtils.isNotBlank(requestSource) && "MOBILE".equalsIgnoreCase(requestSource)){
                doc.setRequestSource(requestSource);
            }
            Date createdDate = null;
            if(doc.getCr_time() != null)
                createdDate = new Date(doc.getCr_time()); // server time zone is UTC from epoch time
            else if(doc.getCr_date() != null) {
                try {
                    createdDate = serverTimeFormatter.parse(doc.getCr_date()); // server time zone is UTC from date string
                } catch (ParseException e) {
                    log.error("Exception in parsing Date for messageDoc {} ", doc);
                }
            }
            if(createdDate != null) {
                String sendingTimeString = TimeZoneUtil.formatDateForTz(createdDate, Constants.DATE_FORMAT_MESSENGER_UI, timeZoneId);
                final Message message = new Message(doc, createdDate, sendingTimeString);
                if(message.isDeleted()){
                   if(Objects.nonNull(userId) && userId.equals(message.getDeletedByUser())){
                        message.setContent(Constants.YOU_DELETED_THIS_MESSAGE);
                    }else{
                        message.setContent(Constants.THIS_MESSAGE_WAS_DELETED);
                    }
                }
                if(filterVideoMessagesForMobile(message,requestSource,doc.getIsAppCompatible())){
                    if(!Source.EMAIL.getSourceId().equals(doc.getSource()) && BooleanUtils.isFalse(doc.getIsAppCompatible()) && "MOBILE".equalsIgnoreCase(requestSource) && CollectionUtils.isNotEmpty(message.mediaFiles) && MessageDocument.CommunicationDirection.SEND.equals(message.getDirection())){
                        List<Message> splitMessages = new ArrayList<>();
                        int mediaFileCounter = 1;
                        for (MessageDocument.MediaFile mediaFile : message.mediaFiles) {
                            Message tempMessage = new Message(doc, createdDate, sendingTimeString);                            
                            tempMessage.id = tempMessage.getId().concat("_M" + mediaFileCounter);
                            tempMessage.mediaFiles = Collections.singletonList(mediaFile);
                            tempMessage.status = mediaFile.getMsg_status();
                            if (mediaFileCounter<message.getMediaFiles().size()) {
                                tempMessage.setContent("");
                            }
                            splitMessages.add(tempMessage);
                            this.messages.add(tempMessage);
                            mediaFileCounter++;
                        }
                    }else{
                        this.messages.add(message);
                    }
                }
                if (onlyActivities) {
                    onlyActivities = checkIfMessageIsNotActivity(doc.getMessageType(), doc.getActivityType());
                }
            }
        }

    }

    @Data
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public static class Message implements Comparable<Message>,Cloneable {
        private String id;
        private String sentOn;
        private Long sentAt;
        private Long sentAtUTC;
        private MessageDocument.UserDetail createdBy;
        private String status;
        private String content;
		private String subject;
        private Integer source;
        private MessageDocument.MessageType messageType;
        private List<MessageDocument.MediaFile> mediaFiles;
        private MessageDocument.NoteType noteType;
        private MessageDocument.NoteState noteState;
        private ActivityType activityType;
        private ActivityMeantFor activityMeantFor;
        private MessageDocument.CommunicationDirection direction;
        private MessageDocument.Channel channel;
        private String customChannel;
        private MessageDocument.SentThrough sentThrough;
        private MessageDocument.UserDetail triggeredFor;
        private MessageDocument.UserDetail triggeredFrom;
		private String m_id;
		private String voiceMailUrl;
		private Boolean transcriptNotAvailable;
		private boolean isCustom;
        private String extRefUid;
		private Long durationInMs;
		private Integer templateId;
        private String pageUrlTrackedByGoogleAnalytics;

        private List<MessageDocument.QuestionAndAnswer> qnaList;
        private MessageDocument.CustomerInfo referrer;
        private MessageDocument.CustomerInfo referredLead;
        private MessageDocument.CustomerInfo conversationSwitchedFrom;
    	private MessageDocument.CustomerInfo conversationSwitchedTo;
        private StatusEnum thankYouNoteStatus;
        private ReferralSource referralSource;
        private String type;    // See MessageDocument.Type field to identify whether this document is updated or not e.g case of Referral activity
        private Integer reviewId;
        private Long reviewCreationUpdationTime;
        protected SurveyDetail surveyDetail;
        private MessageDocument.BusinessDetail fromBusinessDetail;
        private MessageDocument.BusinessDetail toBusinessDetail;

        private PaymentInfo paymentInfo;
        private AppointmentInfo appointmentInfo;
        private String authToken;
		private RichLinkData richLinkData;

        private String storyReplyUrl;
        private String storyMentionUrl;
        private boolean liked;
        private boolean deleted;
        private Integer deletedByUser;
        private boolean answerFlag;
        private Integer smsId;
        
        private List<String> citations;
        private boolean faqFlag;
        private boolean fileFlag;
        private CustomerContactInfo customerContactInfo;
        private ContactInfoSourceEnum customerContactInfoSource;
        
        private boolean replyViaEmail;
        private String waMsgHeader;
        private String waMsgFooter;

        public Message() {}
        public Message(MessageDocument document, Date sentAt, String sentOnStr) {
            this.id = document.getM_id() + MessengerUtil.getMessageTypeSuffix(document.getMessageType(), document.getSource());
            this.sentAt = sentAt.getTime();
            this.sentAtUTC = sentAt.getTime();
            this.sentOn = sentOnStr;
            this.status = document.getMsg_status();
            this.content = MessengerUtil.decryptMessage(document);
            this.subject = document.getMsg_subject();
            this.source = document.getSource();
            this.pageUrlTrackedByGoogleAnalytics = document.getPageUrlTrackedByGoogleAnalytics();
            if (Objects.isNull(document.getMessageType())) {
            	log.info("populate new fields on old message docs for contact: {}",document.getC_id());
                populateWithOldFields(document);
            } else {
                populateWithNewFields(document);
                this.sentThrough = document.getSentThrough();
                this.noteType = document.getNoteType();
                this.activityType = document.getActivityType();
                this.triggeredFor = document.getTriggeredFor();
                this.triggeredFrom = document.getTriggeredFrom();
                this.voiceMailUrl = document.getVoiceMailUrl();
                this.extRefUid=document.getExt_ref_uid();
                if (MessengerConstants.NO_TRANSCRIPTION_MESSAGE.equalsIgnoreCase(this.content)) {
                		this.transcriptNotAvailable = true;
                }
                this.durationInMs = document.getDurationInMS();
            }
            this.thankYouNoteStatus = document.getThankYouNoteStatus();
            this.referrer = document.getReferrer();
            this.referredLead = document.getReferredLead();
            this.conversationSwitchedFrom=document.getConversationSwitchedFrom();
            this.conversationSwitchedTo=document.getConversationSwitchedTo();
            this.referralSource = document.getReferralSource();
            this.qnaList = document.getQnaList();
            this.type = StringUtils.isNotBlank(document.getType()) ? document.getType() : MessageDocument.Type.CREATE.getType();
            this.reviewId = document.getReviewId();
            this.reviewCreationUpdationTime = document.getReview_cr_upd_time();
            this.surveyDetail=document.getSurvey_detail();
            this.fromBusinessDetail=document.getFromBusinessDetail();
            this.toBusinessDetail=document.getToBusinessDetail();
            this.paymentInfo = document.getPaymentInfo();
            this.appointmentInfo = document.getAppointmentInfo();
            if(Objects.nonNull(this.paymentInfo) && StringUtils.isBlank(this.paymentInfo.getTransactionId())) {
                this.paymentInfo.setTransactionId(this.paymentInfo.getId());
            }
            if(Objects.nonNull(document.getAuthToken())) {
            	this.authToken = document.getAuthToken();
            }
			this.richLinkData = document.getRichLinkData();
            if(StringUtils.isNotBlank(document.getStoryReplyUrl())){
                this.storyReplyUrl = document.getStoryReplyUrl();
            }
            if(StringUtils.isNotBlank(document.getStoryMentionUrl())){
                this.storyMentionUrl = document.getStoryMentionUrl();
            }
            if(BooleanUtils.isTrue(document.isLiked())){
                this.liked = document.isLiked();
            }
            if(BooleanUtils.isTrue(document.isDeleted())){
                this.deleted = document.isDeleted();
                this.deletedByUser = document.getMessageDeletedBy().getDeletedByUser();
            }
            if(Objects.nonNull(document.isAnswerFlag())){
                this.answerFlag = document.isAnswerFlag();
            }
            if(Objects.nonNull(document.getSmsId())){
                this.smsId = document.getSmsId();
            }
            if(CollectionUtils.isNotEmpty(document.getCitations())){
                this.citations = document.getCitations();
            }
            if(Objects.nonNull(document.getCustomerContactInfo())){
                this.customerContactInfo = document.getCustomerContactInfo();
            }
            if(Objects.nonNull(document.getCustomerContactInfoSource())){
                this.customerContactInfoSource = document.getCustomerContactInfoSource();
            }
            this.faqFlag = document.isFaqFlag();
            this.fileFlag = document.isFileFlag();
            this.replyViaEmail = document.isReplyViaEmail();
            this.waMsgHeader = document.getWaMsgHeader();
            this.waMsgFooter = document.getWaMsgFooter();
		}
        
        public Message(UserDTO userDTO, ConversationDTO conversationDTO, MessengerMediaFile messengerMediaFile, String timezoneId) {
			this.m_id = String.valueOf(conversationDTO.getId());
			this.id = String.valueOf(conversationDTO.getId())
					+ MessengerUtil.getMessageTypeSuffix(conversationDTO.getMessageType(), conversationDTO.getSource());
            Date sentAt = conversationDTO.getSentOn() != null ? conversationDTO.getSentOn() : conversationDTO.getCreateDate();
            this.sentAt = sentAt.getTime();
            this.sentAtUTC = sentAt.getTime();
            this.sentOn = TimeZoneUtil.formatDateForTz(sentAt, Constants.DATE_FORMAT_MESSENGER_UI, timezoneId);
            if (StringUtils.isNotBlank(conversationDTO.getBody()) && conversationDTO.getEncrypted() != null && conversationDTO.getEncrypted() == 1) {
                try {
                    this.content = EncryptionUtil.decrypt(conversationDTO.getBody(), StringUtils.join(conversationDTO.getSender(), conversationDTO.getRecipient()), StringUtils.join(conversationDTO.getRecipient(), conversationDTO.getSender()), false);
                } catch (Exception e) {
                }
            } else {
                this.content = conversationDTO.getBody();
            }
            this.status = "success";
            this.source=conversationDTO.getSource();
            populateWithNewFields(userDTO, conversationDTO, messengerMediaFile);
        }

        private void populateWithNewFields(MessageDocument document) {
            this.createdBy = document.getCreatedBy();
            this.messageType = document.getMessageType();
            this.direction = document.getCommunicationDirection();
            this.channel = document.getChannel();
            this.customChannel = document.getCustomChannel();
            if(CollectionUtils.isNotEmpty(document.getMediaFiles())) {
                List<MessageDocument.MediaFile> mediaToRemove = new ArrayList<>();
                this.mediaFiles = document.getMediaFiles();
                mediaFiles.forEach(media->{
                	if(StringUtils.isBlank(media.getMsg_status())){
                		media.setMsg_status(document.getMsg_status());
                		media.setMsg_id(document.getM_id());
                	}
                    if(BooleanUtils.isFalse(document.getIsAppCompatible()) && MessageDocument.CommunicationDirection.SEND.equals(document.getCommunicationDirection())
                            && "MOBILE".equalsIgnoreCase(document.getRequestSource())
                    && StringUtils.isNotBlank(media.getA_ext()) && Constants.SUPPORTED_VIDEO_FORMAT.contains(media.getA_ext())){
                        mediaToRemove.add(media);
                    }
                });
                mediaFiles.removeAll(mediaToRemove);
            }
			if (document.getTemplateId() != null) {
				this.templateId = document.getTemplateId();
			}
        }

        private void populateWithOldFields(MessageDocument document) {
            this.source = document.getSource();
            this.messageType = MessageDocument.MessageType.CHAT;
            this.createdBy = new MessageDocument.UserDetail(document.getU_id(), document.getU_name());
            // handle old values of SMS_RECEIVE, MMS_RECEIVE, SMS_SEND and MMS_SEND
            if (document.getMsg_type().equals("SMS_RECEIVE") || document.getMsg_type().equals("MMS_RECEIVE")) {
                this.messageType = MessageDocument.MessageType.CHAT;
                this.direction = MessageDocument.CommunicationDirection.RECEIVE;
            }
            if (document.getMsg_type().equals("SMS_SEND") || document.getMsg_type().equals("MMS_SEND")) {
                this.messageType = MessageDocument.MessageType.CHAT;
                this.direction = MessageDocument.CommunicationDirection.SEND;
                if (StringUtils.isBlank(this.createdBy.getUserName()) && this.direction.equals(MessageDocument.CommunicationDirection.RECEIVE)) {
                    this.createdBy.setUserName(setSender(document.getSource()));
                }
            }
            if (Source.FACEBOOK.getSourceId().equals(document.getSource())) {
                this.channel = MessageDocument.Channel.FACEBOOK;
            }
            if (Source.SMS.getSourceId().equals(document.getSource())) {
                this.channel = MessageDocument.Channel.SMS;
            }
			if (Source.EMAIL.getSourceId().equals(document.getSource())) {
				this.channel = MessageDocument.Channel.EMAIL;
			}
            if (Source.WEB_CHAT.getSourceId().equals(document.getSource()) || Source.WEB_CHAT_BIRDEYE_PROFILE.getSourceId().equals(document.getSource())) {
                this.channel = MessageDocument.Channel.WEB_CHAT;
            }
			if (StringUtils.isNotBlank(document.getA_url())) {
				String url = ControllerUtil.decode(document.getA_url(), "UTF-8");
				MessageDocument.MediaFile mediaFile=new MessageDocument.MediaFile(document.getA_ext(), url,
						document.getA_size(), document.getA_name(), document.getA_contype());
				mediaFile.setMsg_status(document.getMsg_status());
				mediaFile.setMsg_id(document.getM_id());
				this.mediaFiles = Collections.singletonList(mediaFile);
			}
			if (document.getTemplateId() != null) {
				this.templateId = document.getTemplateId();
			}
        }

        private void populateWithNewFields(UserDTO userDTO, ConversationDTO document, MessengerMediaFile mMFile) {
            this.messageType = document.getMessageType();
            this.createdBy = new MessageDocument.UserDetail(userDTO.getId(), userDTO.getName());
            this.direction = document.getCommunicationDirection();
            this.channel = document.getChannel();
            this.customChannel = document.getCustomChannel();
            this.noteType = document.getNoteType();
            this.noteState = document.getNoteState();
            this.activityType = document.getActivityType();
            this.sentThrough = document.getSentThrough();
            if (mMFile != null && StringUtils.isNotBlank(mMFile.getUrl()))
                this.mediaFiles = Collections.singletonList(new MessageDocument.MediaFile(FilenameUtils.getExtension(mMFile.getUrl()), mMFile.getUrl(), mMFile.getContentSize(), mMFile.getName(), mMFile.getContentType()));
            if (StringUtils.isBlank(this.createdBy.getUserName()) && this.direction.equals(MessageDocument.CommunicationDirection.RECEIVE)) {
                this.createdBy.setUserName(setSender(document.getSource()));
            }
			if (document.getTemplateId() != null) {
				this.templateId = document.getTemplateId();
			}
        }

        // for old document  support only
        @Deprecated
        private String setSender(Integer source) {
            if (source != null) {
                switch (source) {
                    case 0:
                        return "auto campaign";
                    case 1:
                        return "auto reply";
                    case 2:
                        return "webchat";
                    default:
                        return "auto reply";
                }
            }
            return "auto reply";
        }

        @Override
        public int compareTo(Message message) {
            if(this.sentOn.compareTo(message.getSentOn()) == 0) {
                return this.id.compareTo(message.getId());
            }
            return this.sentOn.compareTo(message.getSentOn());
        }

		public Message(UserDTO userDTO, ConversationDTO conversationDTO, List<MessengerMediaFile> messengerMediaFiles,
				String timezoneId) {
			this.m_id = String.valueOf(conversationDTO.getId());
			this.id = String.valueOf(conversationDTO.getId())
					+ MessengerUtil.getMessageTypeSuffix(conversationDTO.getMessageType(), conversationDTO.getSource());
			Date sentAt = conversationDTO.getSentOn() != null ? conversationDTO.getSentOn()
					: conversationDTO.getCreateDate();
			this.sentAt = sentAt.getTime();
			this.sentAtUTC = sentAt.getTime();
			this.sentOn = TimeZoneUtil.formatDateForTz(sentAt, Constants.DATE_FORMAT_MESSENGER_UI, timezoneId);
			if (StringUtils.isNotBlank(conversationDTO.getBody()) && conversationDTO.getEncrypted() != null
					&& conversationDTO.getEncrypted() == 1) {
				try {
					this.content = EncryptionUtil.decrypt(conversationDTO.getBody(),
							StringUtils.join(conversationDTO.getSender(), conversationDTO.getRecipient()),
							StringUtils.join(conversationDTO.getRecipient(), conversationDTO.getSender()), false);
				} catch (Exception e) {
				}
			} else {
				this.content = conversationDTO.getBody();
			}
			this.status = "success";
			this.source = conversationDTO.getSource();
			populateWithNewFields(userDTO, conversationDTO, messengerMediaFiles);
		}

		private void populateWithNewFields(UserDTO userDTO, ConversationDTO document,
				List<MessengerMediaFile> mMFiles) {
			this.messageType = document.getMessageType();
			this.createdBy = new MessageDocument.UserDetail(userDTO.getId(), userDTO.getName());
			this.direction = document.getCommunicationDirection();
			this.channel = document.getChannel();
			this.customChannel = document.getCustomChannel();
			this.noteType = document.getNoteType();
			this.noteState = document.getNoteState();
			this.activityType = document.getActivityType();
			this.sentThrough = document.getSentThrough();
			this.subject = document.getSubject();
			if (CollectionUtils.isNotEmpty(mMFiles)) {
				List<MessageDocument.MediaFile> media = new ArrayList<MessageDocument.MediaFile>();
				for (MessengerMediaFile mMFile : mMFiles) {
					media.add(new MessageDocument.MediaFile(FilenameUtils.getExtension(mMFile.getUrl()),
							mMFile.getUrl(), mMFile.getContentSize(), mMFile.getName(), mMFile.getContentType()));
				}
				this.mediaFiles = media;
			}
			if (StringUtils.isBlank(this.createdBy.getUserName())
					&& this.direction.equals(MessageDocument.CommunicationDirection.RECEIVE)) {
				this.createdBy.setUserName(setSender(document.getSource()));
			}
			if (document.getTemplateId() != null) {
				this.templateId = document.getTemplateId();
			}
		}

        @Override
        public Message clone() throws CloneNotSupportedException{
            return (Message) super.clone();
        }
    }

     public static Comparator<Message> getMessageComparator() {
        return (m1, m2) -> {
            if(m1.getSentAt().compareTo(m2.getSentAt()) == 0) {
                return m1.getId().compareTo(m2.getId());
            }
            return m1.getSentAt().compareTo(m2.getSentAt());
        };
    }
     
    private boolean checkIfMessageIsNotActivity(MessageType messageType, ActivityType activityType) {
        boolean activity = messageType == MessageType.ACTIVITY;
        return (!activity
                || (activity && !Constants.EXCLUDED_CONVERSATION_SUMMARY_ACTIVITY_TYPES.contains(activityType))) ? false
                        : true;
    }
    
    private boolean filterVideoMessagesForMobile(Message message, String requestSource, Boolean isAppCompatible){
        if(BooleanUtils.isFalse(isAppCompatible) && MessageDocument.CommunicationDirection.SEND.equals(message.getDirection()) && "MOBILE".equalsIgnoreCase(requestSource) && MessageType.CHAT.equals(message.getMessageType()) && CollectionUtils.isEmpty(message.getMediaFiles()) 
        && StringUtils.isBlank(message.getContent())){
            return false;
        }
        return true;
    }
 }
