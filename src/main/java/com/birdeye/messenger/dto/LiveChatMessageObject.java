package com.birdeye.messenger.dto;


import java.util.Date;
import java.util.List;

import com.birdeye.messenger.dao.entity.LiveChatMessage;
import com.birdeye.messenger.dto.elastic.MessageDocument.Channel;
import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.dto.elastic.MessageDocument.MessageType;
import com.birdeye.messenger.dto.elastic.MessageDocument.SentThrough;
import com.birdeye.messenger.enums.ContactInfoSourceEnum;
import com.birdeye.messenger.util.SecureString;
import com.birdeye.messenger.util.SecureStringUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for SMS Entity.
 *
 */

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = JsonInclude.Include.NON_EMPTY)
@NoArgsConstructor
public class LiveChatMessageObject {

    private Integer id;
    private Integer businessId;
    private int customerId;
    private Integer messengerContactId;
    private String sender;
    private String recepient;
    private String messageBodyUnencrypted; //TODO: Fix these below : use only single messageBody
    private String messageBodyEncrypted;
    private String pageUrlTrackedByGoogleAnalytics;
    private Date createDate;
    private String mediaURL;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sentOn;
    private Integer source;
    private Integer encrypted;
    
    private MessageType messageType;
    private CommunicationDirection communicationDirection;
    private SentThrough sentThrough;
    private Channel channel;
    private Integer recordingDuration;
    private Boolean emailMandatory;
    @SecureString
    private String email;
    private String failureReason;
    private String clientIp;
    private Boolean spam = false;
    private Boolean answerFlag = Boolean.FALSE;

    private List<String> citations;
    private boolean faqFlag;
    private boolean fileFlag;
    private CustomerContactInfo customerContactInfo;
    private ContactInfoSourceEnum customerContactInfoSource;
    private String device;

    public LiveChatMessageObject(LiveChatMessage liveChatMessage, ConversationDTO conversationDTO) {
        this.id = liveChatMessage.getId();
        this.businessId = liveChatMessage.getBusinessId();
        this.customerId = liveChatMessage.getCustomerId();
        this.sender = liveChatMessage.getSender();
        this.recepient = liveChatMessage.getRecepient();
        this.encrypted = liveChatMessage.getEncrypted();
        this.emailMandatory=liveChatMessage.getEmailMandatory();
        if(liveChatMessage.getEncrypted() == 1) {
            this.messageBodyEncrypted = liveChatMessage.getMessageBody();
        }
        else {
            this.messageBodyUnencrypted = liveChatMessage.getMessageBody();
        }
        this.failureReason = liveChatMessage.getFailureReason();
        this.createDate = liveChatMessage.getCreateDate();
        this.sentOn = liveChatMessage.getSentOn();
        this.source = liveChatMessage.getSource();
        this.messengerContactId=liveChatMessage.getMessagerContactId();
		if (conversationDTO != null) {
			this.messageType = conversationDTO.getMessageType();
			this.communicationDirection = conversationDTO.getCommunicationDirection();
			this.sentThrough = conversationDTO.getSentThrough();
			this.channel = conversationDTO.getChannel();
		}
    }

    @Override
    public String toString() {
        return SecureStringUtil.buildSecureToString(this);
    }
  
}
