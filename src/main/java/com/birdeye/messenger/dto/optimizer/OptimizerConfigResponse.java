package com.birdeye.messenger.dto.optimizer;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.JsonNode;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OptimizerConfigResponse {
    
    private String agentId;
    private Integer channel;
    private JsonNode config; // directly saved as a JSON.
}
