package com.birdeye.messenger.dto.leadgenagent;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class ResponseGeneratorToolRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String agentId;
    private String intent;
    private String sentiment;
    private String message_body;
    private Integer channel;
    private MetaData metaData;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Builder
    public static class MetaData implements Serializable {
        private static final long serialVersionUID = 1L;

        private Integer businessId;
        private Integer accountId;
        private Integer userId;
        private Integer mcId;
        private String msgId;
        private String requestId;
        private Integer customerId;

    }
}
