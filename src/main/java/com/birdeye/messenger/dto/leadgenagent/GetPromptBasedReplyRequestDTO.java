package com.birdeye.messenger.dto.leadgenagent;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetPromptBasedReplyRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<Action> actions;
    private Metadata meta;
    private List<Map<String, String>> chatHistory;
    private String message;
    private String intent;
    private String sentiment;



    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Action implements Serializable {

        private static final long serialVersionUID = 1L;

        private String type;
        private String mode;
        private String prompt;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Metadata implements Serializable {

        private static final long serialVersionUID = 1L;

        private Integer businessId;
        private Integer accountId;
        private Integer userId;
        private Integer mcId;
        private String msgId;
        private String requestId;
        private String accountName;
        private String businessLocationName;
        private String businessPhone;
        private String businessEmail;

    }



}
