package com.birdeye.messenger.dto.leadgenagent;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class EscalationNotifierToolResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String messageBody;
    private String responseType;
    private JsonNode config;
    private Integer mcId;
    private String escalationStepType;
    private Integer stepOrder;
    private String status;

    // Metadata from request
    private Integer businessId;
    private Integer accountId;
    private Integer userId;
    private String msgId;
    private String requestId;
    private String conversationId;
}
