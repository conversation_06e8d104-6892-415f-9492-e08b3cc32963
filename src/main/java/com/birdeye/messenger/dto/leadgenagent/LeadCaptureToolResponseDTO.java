package com.birdeye.messenger.dto.leadgenagent;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class LeadCaptureToolResponseDTO {

    private Integer mcId;
    private List<LeadCaptureToolResponseDTO.LeadCaptureToolFieldsResponseDTO> fields;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Builder
    public static class LeadCaptureToolFieldsResponseDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        private String name;
        private String label;
        private String type;
        private Boolean captured;
        private String value;

    }
}
