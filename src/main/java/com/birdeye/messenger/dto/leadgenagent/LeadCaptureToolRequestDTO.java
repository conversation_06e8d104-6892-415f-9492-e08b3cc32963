package com.birdeye.messenger.dto.leadgenagent;

import com.birdeye.messenger.dto.leadcapture.LeadCaptureFieldDto;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class LeadCaptureToolRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String agentId;
    private Integer mcId;
    private List<LeadCaptureToolFieldsDTO> fields;
    private String toolChannel;
    private Integer channel;
    private Integer accountId;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Builder
    public static class LeadCaptureToolFieldsDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        private String name;
        private String label;
        private String type;
        private Boolean captured;

    }

}
