package com.birdeye.messenger.dto.leadgenagent;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class ResponseGeneratorToolResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    //Need to discuss with <PERSON>tyam

    private String messageBody;
    private String responseType;
    private JsonNode config;
    private Integer mcId;


}

//prompt
//prechatformoff//imporve the efficiency