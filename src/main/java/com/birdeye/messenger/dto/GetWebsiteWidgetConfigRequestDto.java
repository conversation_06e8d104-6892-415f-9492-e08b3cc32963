package com.birdeye.messenger.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class GetWebsiteWidgetConfigRequestDto{
    
    private String businessId;
    private String apiKey;
    @JsonProperty("isMicroSite")
    private boolean isMicroSite;
    private String activationStatus;
    private String version;
}
