package com.birdeye.messenger.dto;


import java.util.Date;

import com.birdeye.messenger.dto.elastic.MessageDocument.Channel;
import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.dto.elastic.MessageDocument.MessageType;
import com.birdeye.messenger.dto.elastic.MessageDocument.SentThrough;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.dao.entity.Sms;
import com.birdeye.messenger.util.SecureString;
import com.birdeye.messenger.util.SecureStringUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * DTO for SMS Entity.
 *
 */

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = JsonInclude.Include.NON_EMPTY)
@NoArgsConstructor
@ToString
public class SmsDTO {

    private Integer smsId;
    private int businessId;
    private int customerId;
    private int messengerContactId;
    private int toUserId;
    @SecureString
    private String fromNumber;
    @SecureString
    private String toNumber;
    private String messageSid;
    private String messageBodyUnencrypted; //TODO: Fix these below : use only single messageBody
    private String messageBodyEncrypted;
    private String pageUrlTrackedByGoogleAnalytics;
    private Date createDate;
    private String mediaURL;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sentOn;
    private String failureReason;
    private Integer errorCode;
    private Long reviewRequestId;
    private Integer source;
    private Integer encrypted;
    
    private MessageType messageType;
    private CommunicationDirection communicationDirection;
    private SentThrough sentThrough;
    private Channel channel;
    private Integer recordingDuration;
    private Boolean spam;
    private String rType;

    //TODO: move it to Converter class.
    public SmsDTO(Sms sms, ConversationDTO conversationDTO) {
        this.smsId = sms.getSmsId();
        this.businessId = sms.getBusinessId();
        this.customerId = sms.getCustomerId();
        this.toUserId = sms.getToUserId();
        this.fromNumber = sms.getFromNumber();
        this.toNumber = sms.getToNumber();
        this.messageSid = sms.getMessageSid();
        this.encrypted = sms.getEncrypted();
        if(sms.getEncrypted() == 1) {
            this.messageBodyEncrypted = sms.getMessageBody();
        }
        else {
            this.messageBodyUnencrypted = sms.getMessageBody();
        }
        this.createDate = sms.getCreateDate();
        this.mediaURL = sms.getMediaURL();
        this.sentOn = sms.getSentOn();
        this.failureReason = sms.getFailureReason();
        this.errorCode = sms.getErrorCode();
        this.reviewRequestId = sms.getReviewRequestId();
        this.source = sms.getSource();
		if (conversationDTO != null) {
			this.messageType = conversationDTO.getMessageType();
			this.communicationDirection = conversationDTO.getCommunicationDirection();
			this.sentThrough = conversationDTO.getSentThrough();
			this.channel = conversationDTO.getChannel();
		}
    }
    
    /** Body from Campaign is not encrypted, For quick send sms, Channel is being set as SMS.
    For other campaign messages, Channel would remain CAMPAIGN. Tag for all campaign messages would be CAMPAIGN (5) */
    public static SmsDTO forCampaignSMS(Sms sms,boolean quickSend) {
    	
	    	SmsDTO smsDto = new SmsDTO(sms, null);
	    	//Message meta data
	    	smsDto.messageType = MessageType.CHAT;
	    	smsDto.communicationDirection = CommunicationDirection.SEND;
	    	smsDto.sentThrough = SentThrough.WEB;
	    	smsDto.channel = quickSend ? Channel.SMS : Channel.CAMPAIGN;
	    	
	    	return smsDto;
    }

    public static SmsDTO getSmsDTOContactUs(CustomerDTO customerDto, ContactUsEventDTO request, String phoneNumber,
    		Source source) {
    	SmsDTO smsDto = new SmsDTO();
    	smsDto.setBusinessId(customerDto.getBusinessId());
    	smsDto.setMessageBodyUnencrypted(request.getCustomerComment());
    	smsDto.setEncrypted(1);
    	smsDto.setCustomerId(customerDto.getId());
    	// From Customer
    	smsDto.setToNumber(customerDto.getPhoneE164());
    	// To Business
    	smsDto.setFromNumber(phoneNumber);
    	smsDto.setSource(source.getSourceId());
    	smsDto.setMessageType(MessageType.CHAT);
    	smsDto.setCommunicationDirection(CommunicationDirection.RECEIVE);
    	smsDto.setChannel(Channel.CONTACT_US);
    	smsDto.setCreateDate(new Date(request.getRequestDate()));
    	smsDto.setSpam(customerDto.getBlocked());
    	return smsDto;
    }
    
    public static SmsDTO getSmsDTOAppointment(CustomerDTO customerDto, ContactAppointmentEventDTO request, String phoneNumber,
    		Source source) {
    	SmsDTO smsDto = new SmsDTO();
    	smsDto.setBusinessId(customerDto.getBusinessId());
    	smsDto.setMessageBodyUnencrypted(request.getCustomerComment());
    	smsDto.setEncrypted(1);
    	smsDto.setCustomerId(customerDto.getId());
    	// From Customer
    	smsDto.setToNumber(customerDto.getPhoneE164());
    	// To Business
    	smsDto.setFromNumber(phoneNumber);
    	smsDto.setSource(source.getSourceId());
    	smsDto.setMessageType(MessageType.CHAT);
    	smsDto.setCommunicationDirection(CommunicationDirection.RECEIVE);
    	smsDto.setChannel(Channel.APPOINTMENT);
    	smsDto.setCreateDate(new Date(request.getRequestDate()));
    	smsDto.setPageUrlTrackedByGoogleAnalytics(request.getPageUrl());
    	return smsDto;
    }

    @Override
    public String toString() {
        return SecureStringUtil.buildSecureToString(this);
    }

}
