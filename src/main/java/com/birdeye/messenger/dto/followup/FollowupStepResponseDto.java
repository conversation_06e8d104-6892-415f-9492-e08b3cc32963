package com.birdeye.messenger.dto.followup;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FollowupStepResponseDto {
    
    private Integer followUpDurationSeconds;
    private String messageDuringBusinessHours;
    private String messageOutsideBusinessHours;
}
