package com.birdeye.messenger.dto.followup;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustomerFollowupResponse {
    
    private String status;
    private String message;
    private String sentMessage;
    private Boolean success;
    
    public static CustomerFollowupResponse success(String sentMessage) {
        CustomerFollowupResponse response = new CustomerFollowupResponse();
        response.setStatus("SUCCESS");
        response.setMessage("Customer follow-up message sent successfully");
        response.setSentMessage(sentMessage);
        response.setSuccess(true);
        return response;
    }
    
    public static CustomerFollowupResponse error(String errorMessage) {
        CustomerFollowupResponse response = new CustomerFollowupResponse();
        response.setStatus("ERROR");
        response.setMessage(errorMessage);
        response.setSuccess(false);
        return response;
    }
}
