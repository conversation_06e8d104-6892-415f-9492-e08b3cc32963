package com.birdeye.messenger.dto.followup;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FollowupConfigResponse {
    
    private String agentId;
    private Integer channel;
    private Integer enabled; // 0/1 flag
    private List<Integer> followUpUsing; // Decoded from comma-separated string to source IDs
    private List<FollowupStepResponseDto> followUps;
}
