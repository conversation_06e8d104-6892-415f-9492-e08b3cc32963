package com.birdeye.messenger.dto.followup;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class SaveFollowupConfigRequest {
    
    private String agentId;
    private Integer accountId;
    private Integer channel;
    private Integer enabled; 
    private List<Integer> followUpUsing; // List of source IDs to be encoded as comma-separated
    private List<FollowupStepDto> followUps;
}
