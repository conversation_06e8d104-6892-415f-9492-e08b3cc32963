package com.birdeye.messenger.dto.appointment;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown=true)
public class InsuranceDetails implements Serializable {

	Integer insuranceId;
	String insuranceName;
	Integer insurancePlanId;
	String insurancePlanName;

}
