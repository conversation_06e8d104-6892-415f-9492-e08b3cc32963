package com.birdeye.messenger.dto.appointment;

import lombok.Data;

@Data
public class AppointmentInfo {
    Long appointmentId;
    String altAppointmentId;
    Long specialistId;
    String specialistName;
    Long serviceId;
    String serviceName;
    Long startTime;
    Long endTime;
    String firstName;
    String lastName;
    Boolean selfBooking;
    String action;
    String status;
    String actionBy;
    Long userId;
    String source;
    Long eventTime;
    Integer widgetId;
    String altWidgetId;
    Long createdAt;
    Long updatedAt;
    Long parentId;
    Boolean isActive;
    String smsComment;
    String specialistImageUrl;
    String specialization;
    Integer insuranceId;
    String insuranceName;
    Integer insurancePlanId;
    String insurancePlanName;
}
