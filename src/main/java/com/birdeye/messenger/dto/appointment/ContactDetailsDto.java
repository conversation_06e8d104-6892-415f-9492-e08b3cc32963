package com.birdeye.messenger.dto.appointment;

import com.birdeye.messenger.util.SecureString;
import com.birdeye.messenger.util.SecureStringUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ContactDetailsDto implements Serializable {
    private static final long serialVersionUID = 1L;
    private Integer cid;
    private Integer ecid;
    private String firstName;
    private String lastName;
    private String dob;
    @SecureString
    private String email;
    @SecureString
    private String phone;

    @Override
    public String toString() {
        return SecureStringUtil.buildSecureToString(this);
    }
}
