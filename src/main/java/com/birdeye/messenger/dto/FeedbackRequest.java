package com.birdeye.messenger.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import jakarta.validation.constraints.NotNull;

import java.util.List;

@Getter
@Setter
@ToString
public class FeedbackRequest {

	@NotNull
	private Integer eventId;
	@NotNull
	private String intentType;
	@NotNull
	private Integer value;
	@NotNull
	private Integer widgetConfigId;

	@NotNull
	private Integer source;
	
	private String messageId;

	private List<String> widgetAgent;



}
