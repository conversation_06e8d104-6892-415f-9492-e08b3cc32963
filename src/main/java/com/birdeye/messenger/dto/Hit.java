package com.birdeye.messenger.dto;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Hit<S> {
	private S _source;
	private Map<String,List<? extends Object>> _innerHitsMap;
	public Hit(S _source) {
		super();
		this._source = _source;
		this._innerHitsMap=new HashMap<String, List<? extends Object>>();
	}

}
