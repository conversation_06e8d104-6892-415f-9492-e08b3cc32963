/**
 * 
 */
package com.birdeye.messenger.dao.secure.messaging;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import jakarta.transaction.Transactional;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.secure.messaging.SecureMessagingLink;
import com.birdeye.messenger.dao.repository.SecureMessagingLinkRepository;
import com.birdeye.messenger.dto.secure.messaging.SecureMessagingLinkDTO;
import com.birdeye.messenger.dto.secure.messaging.SecureMessagingLinkResponseDTO;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Slf4j
@Service
public class SecureMessagingAuthDAOImpl implements SecureMessagingAuthDAO {

    @Autowired
    private SecureMessagingLinkRepository messengerContactSecureMessagingLinkRepository;

    @Autowired
    @Lazy
    private SecureMessagingAuthDAO secureMessagingAuthDAO;

    /*
     * Get from cache if present else save new link
     */
    @Override
    @Cacheable(cacheNames = Constants.MESSENGER_CONTACTS_SECURE_MESSAGING_LINKS_CACHE, key = "#mcId +'-'+ #cId", unless = "#result == null")
    @Transactional
    public SecureMessagingLinkResponseDTO saveMessengerContactSecureMessagingLink(Integer mcId,
            Integer cId, SecureMessagingLinkDTO messengerContactSecureMessagingLinkDTO) {
        log.info(
                "saveMessengerContactSecureMessagingLink called with mcId : {},cId : {} and messengerContactSecureMessagingLinkDTO : {}",
                mcId, cId, messengerContactSecureMessagingLinkDTO);
        messengerContactSecureMessagingLinkDTO.setCId(cId);
        SecureMessagingLink messengerContactSecureMessagingLink = null;
        SecureMessagingLinkResponseDTO messagingLinkResponseDTO = null;
        try {
            messengerContactSecureMessagingLink = messengerContactSecureMessagingLinkDTO
                    .getMessengerContactSecureMessagingLink();
            messengerContactSecureMessagingLink = messengerContactSecureMessagingLinkRepository
                    .save(messengerContactSecureMessagingLink);
            log.info("messengerContactSecureMessagingLink : {} saved successfully",
                    messengerContactSecureMessagingLink);
            messagingLinkResponseDTO = new SecureMessagingLinkResponseDTO(
                    messengerContactSecureMessagingLink);
        } catch (Exception e) {
            log.error("error : {} occurred while saving messengerContactSecureMessagingLink : {}", e.getMessage(),
                    messengerContactSecureMessagingLink);
            messagingLinkResponseDTO = secureMessagingAuthDAO
                    .getMessengerContactSecureMessagingLinkByMcIdAndCId(mcId, cId);
        }
        secureMessagingAuthDAO.saveSecureMessagingLinkResponseDTOInCache(messagingLinkResponseDTO);
        return messagingLinkResponseDTO;
    }

    @Override
    @Cacheable(cacheNames = Constants.MESSENGER_CONTACTS_SECURE_MESSAGING_LINKS_CACHE, key = "#mcId +'-'+ #cId", unless = "#result == null")
    public SecureMessagingLinkResponseDTO getMessengerContactSecureMessagingLinkByMcIdAndCId(
            Integer mcId, Integer cId) {
        log.info("getMessengerContactSecureMessagingLinkByMcIdAndCId called with mcId : {} and cId : {}", mcId, cId);

        SecureMessagingLink messengerContactSecureMessagingLink = messengerContactSecureMessagingLinkRepository
                .getMessengerContactSecureMessagingLinkByMcIdAndCId(mcId, cId).orElse(null);
        if (Objects.nonNull(messengerContactSecureMessagingLink)) {
            log.info("messengerContactSecureMessagingLink : {} fetched", messengerContactSecureMessagingLink);
            return new SecureMessagingLinkResponseDTO(messengerContactSecureMessagingLink);
        }
        return null;
    }

    @Override
    @Cacheable(cacheNames = Constants.MESSENGER_CONTACTS_SECURE_MESSAGING_LINKS_CACHE, key = "'secure-link-'+#id", unless = "#result == null")
    public SecureMessagingLinkResponseDTO getMessengerContactSecureMessagingLinkById(
            Integer id) {
        log.info(
                "getMessengerContactSecureMessagingLinkById called with id: {}",
                id);

        Optional<SecureMessagingLink> messengerContactSecureMessagingLink = messengerContactSecureMessagingLinkRepository.findById(id);
        if (messengerContactSecureMessagingLink.isPresent()) {
            log.info("messengerContactSecureMessagingLink : {} fetched", messengerContactSecureMessagingLink.get());
            return new SecureMessagingLinkResponseDTO(messengerContactSecureMessagingLink.get());
        }
        return null;
    }

    @Override
    @CachePut(cacheNames = Constants.MESSENGER_CONTACTS_SECURE_MESSAGING_LINKS_CACHE, key = "'secure-link-'+#secureMessagingLinkResponseDTO.id", unless = "#result == null")
    public SecureMessagingLinkResponseDTO saveSecureMessagingLinkResponseDTOInCache(
            SecureMessagingLinkResponseDTO secureMessagingLinkResponseDTO) {
        return secureMessagingLinkResponseDTO;
    }


    @Override
    public Map<String, SecureMessagingLink> getMessengerContactSecureMessagingLinkResponseDTOsByMcIdsAndCIds(
            List<Integer> mcIds, List<Integer> cIds) {
        log.info(
                "getMessengerContactSecureMessagingLinkResponseDTOsByMcIds called with mcIds : {} and cIds : {}",
                mcIds, cIds);
        List<SecureMessagingLink> secureMessagingLinks = CollectionUtils.isNotEmpty(cIds)
                && CollectionUtils.isNotEmpty(mcIds)
                        ? messengerContactSecureMessagingLinkRepository
                                .getMessengerContactSecureMessagingLinkByMcIdsAndCIds(mcIds, cIds)
                        : new ArrayList<>();
        log.info(
                "secureMessagingLinks fetched : {}",
                secureMessagingLinks);
        return CollectionUtils.isNotEmpty(secureMessagingLinks)
                ? secureMessagingLinks.stream().collect(Collectors.toMap(
                        k -> String.valueOf(String.valueOf(k.getMcId()) + "-" + String.valueOf(k.getCId())), v -> v))
                : new HashMap<>();
    }

}
