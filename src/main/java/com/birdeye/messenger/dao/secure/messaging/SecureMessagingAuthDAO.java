/**
 * 
 */
package com.birdeye.messenger.dao.secure.messaging;

import java.util.List;
import java.util.Map;

import com.birdeye.messenger.dao.entity.secure.messaging.SecureMessagingLink;
import com.birdeye.messenger.dto.secure.messaging.SecureMessagingLinkDTO;
import com.birdeye.messenger.dto.secure.messaging.SecureMessagingLinkResponseDTO;

/**
 * <AUTHOR>
 *
 */
public interface SecureMessagingAuthDAO {

    SecureMessagingLinkResponseDTO saveMessengerContactSecureMessagingLink(Integer mcId, Integer cId,
            SecureMessagingLinkDTO messengerContactSecureMessagingLinkDTO);

    SecureMessagingLinkResponseDTO getMessengerContactSecureMessagingLinkByMcIdAndCId(Integer mcId,
            Integer cId);

    SecureMessagingLinkResponseDTO getMessengerContactSecureMessagingLinkById(
            Integer id);

    Map<String, SecureMessagingLink> getMessengerContactSecureMessagingLinkResponseDTOsByMcIdsAndCIds(
            List<Integer> mcIds, List<Integer> cIds);
    
    SecureMessagingLinkResponseDTO saveSecureMessagingLinkResponseDTOInCache(
            SecureMessagingLinkResponseDTO secureMessagingLinkResponseDTO);
}
