package com.birdeye.messenger.dao.entity;

import lombok.Data;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "messenger_message_archive")
@Data
public class MessengerMessageArchive implements Serializable{
    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id")
    private Integer id;

    @Basic(optional = false)
    @NotNull
    @Column(name = "message_id")
    private Integer messageId;

    @Column(name = "channel")
    private String channel;

    @Column(name = "sent_through")
    private String sentThrough;

    @Column(name = "communication_direction")
    private String communicationDirection;

    @Basic(optional = false)
    @NotNull
    @Column(name = "messenger_contact_id")
    private int messengerContactId;

    @Basic(optional = false)
    @NotNull
    @Column(name = "account_id")
    private int accountId;

    @Basic(optional = false)
    @NotNull
    @Column(name = "message_type")
    private String messageType;

    @Column(name = "created_by")
    private Integer createdBy;

    @Basic(optional = false)
    @NotNull
    @Column(name = "created_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdDate;

    @Column(name = "ext_ref_uid")
    private String extRefUid;

    @Column(name = "page_url_tracked_by_google_analytics")
    private String pageUrlTrackedByGoogleAnalytics;

    @Column(name="review_id")
    private Integer reviewId;

    @Column(name="survey_response_id")
    private Integer surveyResponseId;
}
