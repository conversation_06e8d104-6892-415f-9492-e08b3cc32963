package com.birdeye.messenger.dao.entity;

import java.util.Date;

import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Data;

/**
 *
 * <AUTHOR> Iqubal
 */
@Entity
@Table(name = "webchat_custom_location_name")
@Data
public class WebchatCustomLocationName {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	@Column(name = "id")
	private Integer id;

	@Column(name = "business_id")
	private Integer businessId;
	
	@Column(name = "account_id")
	private Integer accountId;

	@Column(name = "primary_name")
	private String primaryName;
	
	@Column(name = "secondary_name")
	private String secondaryName;

	@Column(name = "created_at")
	@Temporal(TemporalType.TIMESTAMP)
	private Date createdAt;

	@Column(name = "updated_at")
	@Temporal(TemporalType.TIMESTAMP)
	private Date updatedAt;

	public WebchatCustomLocationName() {
		
	}
	public WebchatCustomLocationName(Integer accountId, Integer businessId, String primaryName, String secondaryName) {
		this.accountId=accountId;
		this.businessId=businessId;
		this.primaryName=primaryName;
		this.secondaryName=secondaryName;
	}
}