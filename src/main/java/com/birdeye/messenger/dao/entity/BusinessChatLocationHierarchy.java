package com.birdeye.messenger.dao.entity;

import java.util.Date;

import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Data;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "business_chat_location_hierarchy")
@Data
public class BusinessChatLocationHierarchy {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	@Column(name = "id")
	private Integer id;

	@Column(name = "level_id")
	private Integer levelId;

	@Column(name = "sub_level")
	private String subLevel;

	@JoinColumn(name = "chat_widget_config_id", referencedColumnName = "id", insertable = false, updatable = false)
	@ManyToOne(optional = false)
	private BusinessChatWidgetConfig businessChatWidget;

	@Column(name = "chat_widget_config_id")
	private Integer businessChatWidgetId;

	@Column(name = "updated_by")
	private String updatedBy;

	@Column(name = "created_at")
	@Temporal(TemporalType.TIMESTAMP)
	private Date createdAt;

	@Column(name = "updated_at")
	@Temporal(TemporalType.TIMESTAMP)
	private Date updatedAt;

	public BusinessChatLocationHierarchy(Integer levelId, String subLevel, Integer businessChatWidgetId, String updatedBy) {
		this.levelId = levelId;
		this.subLevel = subLevel;
		this.businessChatWidgetId = businessChatWidgetId;
		this.updatedBy = updatedBy;
	}

	@Override
	public String toString() {
		return "BusinessChatLocationHierarchy{" +
				"id=" + id +
				", levelId=" + levelId +
				", subLevel='" + subLevel + '\'' +
				", businessChatWidget=" + businessChatWidget +
				", businessChatWidgetId=" + businessChatWidgetId +
				", updatedBy='" + updatedBy + '\'' +
				", createdAt=" + createdAt +
				", updatedAt=" + updatedAt +
				'}';
	}

	public BusinessChatLocationHierarchy() {
	}
}