package com.birdeye.messenger.dao.entity;

import java.io.Serializable;
import java.util.Date;

import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "business_chat_widget_config")
@Getter
@Setter
public class BusinessChatWidgetConfig implements Serializable,Cloneable {


    private static final long serialVersionUID = 1L;

	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id")
    private Integer id;

    @Column(name = "business_id")
    private Long businessId;

    @Column(name = "chat_theme")
    private String chatTheme;

    @Column(name = "chat_icon")
    private String chatIcon;

    @Column(name = "chat_icon_value")
    private String chatIconValue;


    @Column(name = "banner_color")
    private String bannerColor;

    @Column(name = "banner_text_color")
    private String bannerTextColor;

    //DEFAULT VALUE = #1976D2
    @Column(name = "btn_color")
    private String btnColor;

    //DEFAULT VALUE = #FFFFFF
    @Column(name = "btn_txt_color")
    private String btnTxtColor;

    @Column(name = "enable_reply_in_business_hr")
    private Integer enableReplyInBusinessHr;

    @Column(name = "enable_reply_post_business_hr")
    private Integer enableReplyPostBusinessHr;

    //DEFAULT VALUE = Thank you for contacting [BIZ_NAME]! Our team member will reach out shortly.
    @Column(name = "auto_reply_txt")
    private String autoReplyTxt;

    @Column(name = "reply_text_post_business_hr")
    private String replyTextPostBusinessHr;

    @Column(name = "header_headline")
    private String headerHeadline;

    @Column(name = "header_description")
    private String headerDescription;

    @Column(name = "thankyou_msg_headline")
    private String webChatOnlineClosingMessageHeader;

    @Column(name = "thankyou_msg_description")
    private String webChatOnlineClosingMessageBody;

    @Column(name = "business_domain")
    private String businessDomain;

    // null : disabled, 0 : instant, Other applicable values (5/10/15/20)
    @Column(name = "popup_interval")
    private Integer popupInterval = 5;
    

    // 0 or 1 as permissible values : 0 to disable, 1 to enable
    @Column(name = "microsite")
    private Integer microsite;

    @Column(name = "chat_bubble")
    private String chatBubble;

    @Column(name = "enable_chat_bubble")
    private Integer enableChatBubble;

    @Column(name = "enable_chat_bubble_sound")
    private Integer enableChatBubbleSound;

    @Column(name = "chat_icon_color")
    private String chatIconColor;

    @Column(name = "chat_icon_fore_color")
    private String chatIconForeColor;

    @Column(name = "updated_by")
    private String updatedBy;

    @Column(name = "created_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;

    @Column(name = "updated_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedAt;

    @Column(name = "enabled")
    private Integer enabled = 1;

    @Column(name = "widget_clone_id")
    private Integer widgetCloneId;

    @Column(name = "enterprise_id")
    private Integer enterpriseId;

    @Column(name = "widget_name")
    private String widgetName;

    @Column(name = "livechat_enabled")
    private Integer livechatEnabled;

    @Column(name = "chatbot_enabled")
    private Integer chatbotEnabled;

    @Column(name = "enable_google_analytics")
    private Integer enableGoogleAnalytics;

    @Column(name = "google_analytics_version")
    private String googleAnalyticsVersion;

    @Column(name = "google_tracking_id")
    private String googleTrackingId;

    @Column(name = "offline_closing_message_header")
    private String offlineClosingMessageHeader;

    @Column(name = "offline_closing_message_body")
    private String offlineClosingMessageBody;

//    @OneToOne(fetch = FetchType.EAGER, cascade = CascadeType.MERGE)
//    @JoinColumn(name = "id", referencedColumnName = "widget_id")
//    private LiveChatWidgetConfig liveChatWidgetConfig;

    @Column(name = "installed")
    private Integer installed;

    @Column(name = "status_updated_on")
    private Date statusUpdatedOn;


    @Column(name = "websites")
    private String websites;

    @Column(name = "external_id")
    private Long externalId;

    @Column(name = "all_location_disabled")
    private Integer allLocationDisabled = 0;
    
    @Column(name = "email_mandatory")
    private Integer emailMandatory;

    @Column(name = "custom_field_enabled")
    private Boolean customFieldEnabled = true;

    @Column(name = "disclaimer_text")
    private String disclaimer;

    @Column(name = "auto_detect_location", nullable = false)
    private Integer autoDetectLocation = 1;

    @Column(name = "disclaimer_selection_status")
    private Integer disclaimerSelectionStatus = 0;

    @Column(name = "enable_prechat_form")
    private Integer enablePrechatForm = 1;

    @Column(name = "prechat_form_inside_business_hours")
    private Integer prechatFormInsideBusinessHours = 1;

    @Column(name = "prechat_form_outside_business_hours")
    private Integer prechatFormOutsideBusinessHours = 1;
    
    @Column(name = "timer_display")
    private Integer timerDisplay = 1;
    
    @Column(name = "timer_seconds")
    private Integer timerSeconds = 0;
    
    @Column(name = "mobile_view_size")
    private String mobileView;
    
    @Column(name = "widget_agent")
    private String widgetAgent;


    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }



}
