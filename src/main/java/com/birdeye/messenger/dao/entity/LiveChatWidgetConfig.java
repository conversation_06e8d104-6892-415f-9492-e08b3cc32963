package com.birdeye.messenger.dao.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * <AUTHOR> D
 */
@Entity
@Table(name = "live_chat_widget_config")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LiveChatWidgetConfig implements Serializable {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id")
    private Integer id;

    @Column(name = "widget_id")
    private Integer widgetId;

    @Column(name = "offline_closing_message_header")
    private String offlineClosingMessageHeader = "We'll text you!";

    @Column(name = "offline_closing_message_body")
    private String offlineClosingMessageBody = "We'll follow up with you soon. You can always text us at [Business Phone].";

    @Column(name = "offline_welcome_message")
    private String offlineWelcomeMessage = "We're closed right now but we got your message. We'll text you on [Business Phone] when we're back!";

    @Column(name = "online_closing_message_header")
    private String onlineClosingMessageHeader = "Text us!";

    @Column(name = "online_closing_message_body")
    private String onlineClosingMessageBody = "The best way to get ahold of us is to text us [Business Phone].";

    @Column(name = "online_welcome_message")
    private String onlineWelcomeMessage = "Give us a moment while we look for someone available to help you.";
    
    @Column(name = "online_text_message")
    private String onlineTextMessage = "Thank you for contacting [Business Name]! Someone from our team will reach out shortly.";
    
    @Column(name = "offline_text_message")
    private String offlineTextMessage = "Thank you for contacting [Business Name]! We are currently closed, but we’ll text you when we’re back!";

}
