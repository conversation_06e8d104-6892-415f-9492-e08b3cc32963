package com.birdeye.messenger.dao.entity;

import java.io.Serializable;
import java.util.Date;

import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.NotNull;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * Entity for storing custom channel messages.
 * Similar to Sms entity for SMS messages.
 */
@Entity
@Table(name = "custom_channel_message")
@Getter
@Setter
@ToString
public class CustomChannelMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id")
    private Integer id;

    @NotNull
    @Column(name = "business_id")
    private Integer businessId;

    @NotNull
    @Column(name = "m_c_id")
    private Integer mcId;

    @Column(name = "message_body")
    private String messageBody;

    @Column(name = "media_url")
    private String mediaURL;

    @Column(name = "custom_channel")
    private String customChannel;

    @Column(name = "email_subject")
    private String emailSubject;

    @Column(name = "create_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;

    @Column(name = "sent_on")
    @Temporal(TemporalType.TIMESTAMP)
    private Date sentOn;

    @Column(name = "encrypted")
    private Integer encrypted;

    @Column(name = "failure_reason")
    private String failureReason;

    @Column(name = "communication_direction")
    private String communicationDirection;

    public CustomChannelMessage() {
        this.createDate = new Date();
        this.encrypted = 0; // Not encrypted by default
    }
    
}
