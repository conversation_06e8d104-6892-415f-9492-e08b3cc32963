package com.birdeye.messenger.dao.entity;

import java.io.Serializable;
import java.util.Date;

import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.NotNull;

import com.birdeye.messenger.enums.PulseSurveyStatusEnum;

import lombok.Data;

/**
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "receive_msg_bypass_inbox_audit")
@Data
public class ReceiveMsgBypassInboxAudit implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	@Column(name = "id")
	private Integer id;

	@Column(name = "account_id")
	private Integer accountId;

	@Column(name = "business_id")
	private Integer businessId;

	@Column(name = "customer_id")
	private Integer customerId;

	@Column(name = "msg_type")
	private String msgType; // stop or pulse

	@Column(name = "channel")
	private String channel; //sms

	@Basic(optional = false)
	@Column(name = "created_at")
	@Temporal(TemporalType.TIMESTAMP)
	private Date updatedAt = new Date();


	public ReceiveMsgBypassInboxAudit(Integer accountId, Integer businessId, Integer customerId, String msgType,
			String channel) {
		super();
		this.accountId = accountId;
		this.businessId = businessId;
		this.customerId = customerId;
		this.msgType = msgType;
		this.channel = channel;
	}
}