package com.birdeye.messenger.dao.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "customer_sentiment_audit")
@Data
@NoArgsConstructor
public class CustomerSentimentAudit implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    private Integer id;

    @Column(name = "customer_ids")
    private String customerIds;

    @Column(name = "ec_id")
    private Integer ecId;


    @Column(name = "status")
    private String status;



    @Column(name = "created")
    @Temporal(TemporalType.TIMESTAMP)
    private Date created;


    public CustomerSentimentAudit(String customerIds, Integer ecId, String status, Date created) {
        this.customerIds = customerIds;
        this.ecId = ecId;
        this.status = status;
        this.created = created;
    }
}