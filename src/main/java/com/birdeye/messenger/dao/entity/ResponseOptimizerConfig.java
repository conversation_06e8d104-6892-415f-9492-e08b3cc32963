package com.birdeye.messenger.dao.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

@Entity
@Table(name = "response_optimizer_config")
@Data
public class ResponseOptimizerConfig {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "agent_id", nullable = false)
    private String agentId;

    @Column(name = "account_id", nullable = false)
    private Integer accountId;

    @Column(name = "channel", nullable = false)
    private Integer channel;

    @Column(name = "config", columnDefinition = "TEXT")
    private String config; // JSON string containing the channel-specific configuration

    @Column(name = "created_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;

    @Column(name = "updated_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedAt;
}
