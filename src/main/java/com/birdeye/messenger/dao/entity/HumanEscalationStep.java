package com.birdeye.messenger.dao.entity;

import com.birdeye.messenger.enums.EscalationConfigType;
import com.birdeye.messenger.enums.EscalationStepType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Data;

@Entity
@Table(name = "human_escalation_step")
@Data
public class HumanEscalationStep {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "config_id", nullable = false)
    private HumanEscalationConfig config;

    @Enumerated(EnumType.STRING)
    @Column(name = "config_type", nullable = false)
    private EscalationConfigType configType;

    @Column(name = "order", nullable = false)
    private Integer order;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false)
    private EscalationStepType type;

    @Column(name = "message", columnDefinition = "TEXT")
    private String message;

    @Column(name = "title")
    private String title;

    @Column(name = "state")
    private Boolean state;

    @Column(name = "editable")
    private Boolean editable;

    @Column(name = "duration_seconds")
    private Integer durationSeconds; 

}
