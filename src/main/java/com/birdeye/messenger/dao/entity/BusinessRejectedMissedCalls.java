package com.birdeye.messenger.dao.entity;

import java.io.Serializable;
import java.util.Date;

import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import lombok.Data;


/**
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "business_rejected_missed_calls")
@Data
public class BusinessRejectedMissedCalls implements Serializable {

	private static final long serialVersionUID = 1L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	@Column(name = "id")
	private Integer id;

	@Basic(optional = false)
	@NotNull
	@Column(name = "business_id")
	private int businessId;

	@Basic(optional = false)
	@NotNull
	@Column(name = "customer_id")
	private int customerId;

	@Basic(optional = false)
	@Column(name = "to_user_id")
	private int toUserId;

	@Basic(optional = false)
	@NotNull
	@Size(min = 1, max = 20)
	@Column(name = "from_number")
	private String fromNumber;

	@Basic(optional = false)
	@NotNull
	@Size(min = 1, max = 20)
	@Column(name = "to_number")
	private String toNumber;

	@Basic(optional = false)
	@Size(min = 1, max = 100)
	@Column(name = "call_sid")
	private String callSid;

	@Basic(optional = false)
	@NotNull
	@Column(name = "create_date")
	@Temporal(TemporalType.TIMESTAMP)
	private Date createDate;

	@Basic(optional = false)
	@Column(name = "sent_on")
	@Temporal(TemporalType.TIMESTAMP)
	private Date sentOn;

	@Column(name = "failure_reason")
	private String failureReason;

	@Column(name = "error_code")
	private Integer errorCode;

	@Column(name = "source")
	private Integer source = 100;

	@Basic(optional = false)
	@Size(min = 1, max = 100)
	@Column(name = "recording_sid")
	private String recordingSid;

	@Size(min = 1, max = 1000)
	@Column(name = "recording_url")
	private String recordingUrl;

	@Basic(optional = false)
	@Column(name = "transcription")
	private String transcription;

}