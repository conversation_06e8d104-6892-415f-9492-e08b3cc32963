package com.birdeye.messenger.dao.entity;

import com.birdeye.messenger.dto.ConvStateForResTimeCalc;
import com.birdeye.messenger.enums.ContactState;
import com.birdeye.messenger.enums.LeadSource;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.ReferralSource;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import jakarta.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "messenger_contact_archive")
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MessengerContactArchive implements Serializable,Cloneable{
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id", nullable = false)
    private Integer id;


    @Column(name = "business_id")
    private Integer businessId;

    @Column(name = "customer_id")
    private Integer customerId;

    @Basic(optional = false)
    @Column(name = "updated_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedAt = new Date();

    @Basic(optional = false)
    @Column(name = "created_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;

    @Basic(optional = false)
    @Column(name = "last_activity_date")//TODO Change column name
    //@Column(name = "last_msg_on")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastMsgOn;

    @Column(name = "last_message")
    private String lastMessage;

    @Column(name = "assigned_to")
    private Integer assignee;

    @Column(name = "encrypted")
    private Integer encrypted;

    @Column(name = "tag")
    private Integer tag;

    @Column(name = "facebook_id")
    private String facebookId;

    @Column(name = "last_alert_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastAlertAt; // last time email notification sent to business User for unresponded messages

    @Column(name = "last_response_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastResponseAt; // last time business user sent a response to customer

    @Column (name = "image_url")
    private String imageUrl;

    @Column(name = "current_assignee")
    private Integer currentAssignee;

    @Column(name = "current_assignee_name")
    private String currentAssigneeName;

    @Column(name = "assigned_team_id")
    private Integer teamId;

    @Column(name = "assigned_team_name")
    private String teamName;

    @Column(name = "assignment_type")
    @Enumerated(EnumType.STRING)
    private AssignmentType assignmentType;

    @Column(name = "current_assigner")
    private Integer currentAssigner;

    /**
     * @see com.birdeye.messenger.dto.LastMessageMetaData
     */
    @Column(name = "last_message_metadata")
    private String lastMessageMetaData;

    @Column(name = "current_assignee_emailId")
    private String currentAssigneeEmailId;

    @Transient
    private Boolean isNew;


    @Column(name = "import_status")
    private Byte importStatus;


    @Column(name = "viewed_by")
    private String viewedBy;

    @Column(name = "is_read")
    private Boolean isRead;

    @Column(name = "video_room_id")
    private String videoRoomId;

    @Column(name = "video_room_sid")
    private String videoRoomSid;

    @Column(name = "video_room_status")
    @Enumerated(EnumType.STRING)
    private VideoRoomStatus videoRoomStatus;

    @Column(name = "video_online_customer_id")
    private Integer videoOnlineCustomerId;

    @Column(name = "video_online_user_id")
    private Integer videoOnlineUserId;

    @Column(name = "video_room_creation_date")
    private Date videoRoomCreationDate;

    @Column(name = "video_conversation_start_date")
    private Date videoConversationStartDate;

    @Column(name = "video_recent_state_tran_date")
    private Date videoRecentStateTransitionDate;

    @Column(name = "video_initiated_by_user_id")
    private Integer videoInitiatedByUserId;

    @Column(name = "ext_ref_uid")
    private String extRefUid;

    @Column(name = "referral_source")
    @Enumerated(EnumType.STRING)
    private ReferralSource referralSource;

    @Column(name = "lead_source")
    @Enumerated(EnumType.STRING)
    private LeadSource leadSource;

    @Column(name = "lead")
    private Boolean lead;

    @Column(name = "referrer")
    private Boolean referrer;

    @Column(name = "contact_state")
    @Enumerated(EnumType.STRING)
    private ContactState contactState;

    @Column(name = "last_review_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastReviewDate;

    @Column(name = "reviewer_id")
    private Integer reviewerId;

    @Column(name = "last_survey_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastSurveyDate;

    // rtm - response time meta-data
    @Column(name = "rtm_tag_as_first_response")
    private Boolean rtmTagAsFirstResponse; // first response time to calculate

    @Column(name = "rtm_last_msg_id")
    private String rtmLastMsgId; // last message id

    @Column(name = "rtm_last_msg_type")
    private String rtmLastMsgType; // message type S - send ,R - receive, E - empty

    @Column(name = "rtm_last_msg_epoch")
    private Long rtmLastMsgEpoch;

    @Column(name = "rtm_pause_tagging")
    private Boolean rtmPauseTagging; // null,FALSE == FALSE

    @Transient
    private Integer templateId;

    @Column(name = "google_conversation_id")
    private String googleConversationId;

    @Column(name = "instagram_conversation_id")
    private String instagramConversationId;

    @Column(name = "apple_conversation_id")
    private String appleConversationId;

    @Column(name = "blocked")
    private Boolean blocked = false;

    @Transient
    private List<String> capabilityList;

    @Column(name = "payment_reference_id")
    private String paymentReferenceId;

    @Column(name = "spam")
    private Boolean spam = false;

    @Column(name = "spam_marked_by")
    private Integer spamMarkedBy = -78787;

    public boolean isCampaignOnly() {
        return tag != null && MessageTag.CAMPAIGN.getCode() == tag.intValue();
    }
    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }

    public ConvStateForResTimeCalc getCurrentStateForResTimeCalc() {
        ConvStateForResTimeCalc convStateForResTimeCalc = new ConvStateForResTimeCalc();
        convStateForResTimeCalc.setTagAsFirstResponse(rtmTagAsFirstResponse);
        convStateForResTimeCalc.setRtmLastMsgId(rtmLastMsgId);
        convStateForResTimeCalc.setRtmLastMsgType(rtmLastMsgType);
        convStateForResTimeCalc.setRtmLastMsgEpoch(rtmLastMsgEpoch);
        return convStateForResTimeCalc;
    }

    public void setCurrentStateForResTimeCalc(ConvStateForResTimeCalc state) {
        this.setRtmTagAsFirstResponse(state.getTagAsFirstResponse());
        this.setRtmLastMsgEpoch(state.getRtmLastMsgEpoch());
        this.setRtmLastMsgId(state.getRtmLastMsgId());
        this.setRtmLastMsgType(state.getRtmLastMsgType());
    }

}
