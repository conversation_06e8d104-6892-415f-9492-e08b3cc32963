package com.birdeye.messenger.dao.entity;

import java.util.Date;

import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "gpt_response_feedback_robin")
@Data
@NoArgsConstructor
public class GPTResponseFeedbackAudit {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	private Integer id;

	@Column(name = "account_id")
	private Integer accountId;

	@Column(name = "business_id")
	private Integer businessId;

	@Column(name = "m_c_id")
	private Integer mcId;

	@Column(name = "query_channel")
	private String queryChannel;
	
	@Column(name = "query")
	private String query;

	@Column(name = "response")
	private String response;

	@Column(name = "support")
	private Integer support;
	
	@Column(name = "feedback")
	private Integer feedback;
	
	@Column(name = "gpt_response_time")
	private Long gptResponseTime;

	@Basic(optional = false)
	@Column(name = "created_at")
	@Temporal(TemporalType.TIMESTAMP)
	private Date createdAt;

	@Basic(optional = false)
	@Column(name = "updated_at")
	@Temporal(TemporalType.TIMESTAMP)
	private Date updatedAt = new Date();

	@Column(name = "ai_call_payload", columnDefinition = "JSON")
	private String aiPayload;
	
	@Column(name = "response_code")
	private String responseCode;
	
	@Column(name = "error_message")
	private String errorMessage;

}
