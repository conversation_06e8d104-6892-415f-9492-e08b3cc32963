package com.birdeye.messenger.dao.entity;

import java.io.Serializable;

import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import lombok.Data;

/**
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "business_receptionist_configuration")
@Data
public class BusinessReceptionistConfiguration implements Serializable{

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	@Column(name = "id")
	private Integer id;
	
	@Column(name = "business_id")
	private Long businessId;
	
	@Column(name = "forwarding_setup_status")
    private Integer forwardingSetupStatus = 0;
	
	@Column(name = "voicemail_greeting_text")
	private String voicemailGreetingText;
	
	@Column(name = "voicemail_source_url")
	private String voicemailSourceUrl;
	
	@Column(name = "autoreply_text")
	private String autoreplyText;
	
	@Column(name = "type") //voice_call, missed_call_reply_inbox, missed_call_reply_non_inbox
	private String type = "voice_call";
	
	@Column(name = "missed_call_autoreply_text")
	private String missedCallAutoreplyText;
	
	@Column(name = "reject_missed_call")
	private Integer missedCallReject = 0;
}
