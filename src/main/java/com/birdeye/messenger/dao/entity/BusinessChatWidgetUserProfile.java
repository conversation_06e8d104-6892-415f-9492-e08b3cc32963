/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.birdeye.messenger.dao.entity;

import java.io.Serializable;
import java.util.Date;

import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "business_chat_widget_user_profile")
public class BusinessChatWidgetUserProfile implements Serializable {

	private static final long serialVersionUID = -6884516854144455524L;

	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id")
    private Integer id;

    @Column(name = "user_profile_name")
    private String userProfileName;
    
    @Column(name = "user_profile_image")
    private String userProfileImage;

    @Column(name="chat_widget_config_id")
    private Integer businessChatWidgetId;

	@JoinColumn(name = "chat_widget_config_id", referencedColumnName = "id", insertable = false, updatable = false)
	@ManyToOne(optional = false)
	private BusinessChatWidgetConfig businessChatWidget;

    @Column(name = "updated_by")
    private String updatedBy;
    
    @Column(name = "created_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;
    
    @Column(name = "updated_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedAt;

    public BusinessChatWidgetUserProfile() {
    }
    
    public BusinessChatWidgetUserProfile(String userProfileName, String userProfileImage) {
		super();
		this.userProfileName = userProfileName;
		this.userProfileImage = userProfileImage;
	}

	public BusinessChatWidgetUserProfile(String userProfileName, String userProfileImage, Integer businessChatWidgetId, BusinessChatWidgetConfig businessChatWidget, String updatedBy) {
		this.userProfileName = userProfileName;
		this.userProfileImage = userProfileImage;
		this.businessChatWidgetId = businessChatWidgetId;
		this.businessChatWidget = businessChatWidget;
		this.updatedBy = updatedBy;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getUserProfileName() {
		return userProfileName;
	}

	public void setUserProfileName(String userProfileName) {
		this.userProfileName = userProfileName;
	}

	public String getUserProfileImage() {
		return userProfileImage;
	}

	public void setUserProfileImage(String userProfileImage) {
		this.userProfileImage = userProfileImage;
	}

	public Integer getBusinessChatWidgetId() {
		return businessChatWidgetId;
	}

	public void setBusinessChatWidgetId(Integer businessChatWidgetId) {
		this.businessChatWidgetId = businessChatWidgetId;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public Date getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public BusinessChatWidgetConfig getBusinessChatWidget() {
		return businessChatWidget;
	}

	public void setBusinessChatWidget(BusinessChatWidgetConfig businessChatWidget) {
		this.businessChatWidget = businessChatWidget;
	}

	@Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        if (!(object instanceof BusinessChatWidgetUserProfile)) {
            return false;
        }
        BusinessChatWidgetUserProfile other = (BusinessChatWidgetUserProfile) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

	@Override
	public String toString() {
		return "BusinessChatWidgetUserProfile{" +
				"id=" + id +
				", userProfileName='" + userProfileName + '\'' +
				", userProfileImage='" + userProfileImage + '\'' +
				", businessChatWidgetId=" + businessChatWidgetId +
				", updatedBy='" + updatedBy + '\'' +
				", createdAt=" + createdAt +
				", updatedAt=" + updatedAt +
				'}';
	}
}
