package com.birdeye.messenger.dao.entity;

import java.util.Date;

import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.persistence.Transient;
import jakarta.xml.bind.annotation.XmlRootElement;
import lombok.Data;

/**
 *
 * <AUTHOR> Iqubal
 */
@Entity
@Table(name = "outbound_spam_message_audit")
@XmlRootElement
@Data
public class OutboundSpamMessageAudit {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	@Column(name = "id")
	private Integer id;

	@Column(name = "account_id")
	private Integer accountId;
	
	@Column(name = "business_id")
	private Integer businessId;
	
	@Column(name = "mc_id")
	private Integer mcId;
	
	@Column(name = "user_id")
	private Integer userId;
	
	@Column(name = "message_id")
	private String messageId;

	@Column(name = "channel")
	private String channel;
	
	@Column(name = "created_at")
	@Temporal(TemporalType.TIMESTAMP)
	private Date createdAt;
	
	@Column(name = "spam_content")
	private Boolean spamContent = false;
	
	@Column(name = "phishing_content")
	private Boolean phishingContent = false;

	@Column(name = "message_body")
	private String messageBody;

	public OutboundSpamMessageAudit() {
	}
	
}