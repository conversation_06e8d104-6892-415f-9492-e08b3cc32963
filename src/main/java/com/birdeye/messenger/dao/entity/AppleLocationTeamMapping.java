/**
 * 
 */
package com.birdeye.messenger.dao.entity;

import java.io.Serializable;
import java.util.Date;

import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "apple_location_team_mapping")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AppleLocationTeamMapping implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Integer id;

    @Column(name = "account_id")
    @NotNull
    private Integer accountId;

    @Column(name = "business_id", unique = true)
    @NotNull
    private Integer businessId;

    @Column(name = "team_id")
    @NotNull
    private Integer teamId;

    @Column(name = "created_at")
    @Temporal(TemporalType.TIMESTAMP)
    @Basic(optional = false)
    private Date createdAt;

    @Column(name = "updated_at")
    @Temporal(TemporalType.TIMESTAMP)
    @Basic(optional = false)
    private Date updatedAt;

}
