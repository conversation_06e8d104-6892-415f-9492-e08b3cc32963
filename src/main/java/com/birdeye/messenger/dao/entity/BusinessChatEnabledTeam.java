package com.birdeye.messenger.dao.entity;

import java.util.Date;

import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Data;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "business_chat_enabled_team")
@Data
public class BusinessChatEnabledTeam {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	@Column(name = "id")
	private Integer id;

	@Column(name = "team_id")
	private Integer teamId;

	@JoinColumn(name = "widget_config_id", referencedColumnName = "id", insertable = false, updatable = false)
	@ManyToOne(optional = false)
	private BusinessChatWidgetConfig businessChatWidget;

	@Column(name = "widget_config_id")
	private Integer widgetConfigId;
	
	@Column(name = "sort_order")
	private Integer sortOrder;

	@Column(name = "updated_by")
	private String updatedBy;

	@Column(name = "created_at")
	@Temporal(TemporalType.TIMESTAMP)
	private Date createdAt;

	@Column(name = "updated_at")
	@Temporal(TemporalType.TIMESTAMP)
	private Date updatedAt;

	public BusinessChatEnabledTeam(Integer teamId, Integer widgetConfigId, String updatedBy) {
		this.teamId = teamId;
		this.widgetConfigId = widgetConfigId;
		this.updatedBy = updatedBy;
	}
	public BusinessChatEnabledTeam() {
	}
	@Override
	public String toString() {
		return "BusinessChatEnabledTeam [id=" + id + ", teamId=" + teamId + ", businessChatWidget=" + businessChatWidget
				+ ", widgetConfigId=" + widgetConfigId + ", sortOrder=" + sortOrder + ", updatedBy=" + updatedBy
				+ ", createdAt=" + createdAt + ", updatedAt=" + updatedAt + "]";
	}
}