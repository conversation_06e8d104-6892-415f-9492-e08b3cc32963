package com.birdeye.messenger.dao.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

@Table(name="chat_transcript_config")
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ChatTranscriptAccountConfig implements Serializable  {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id")
    private Integer id;

    @Column(name = "account_id")
    private Integer accountId ;

    @Column(name = "transcript_format")
    private String transcriptFormat = "HTML";


    public ChatTranscriptAccountConfig(Integer accountId, String transcriptFormat) {
        this.accountId = accountId;
        this.transcriptFormat = transcriptFormat;
    }
}
