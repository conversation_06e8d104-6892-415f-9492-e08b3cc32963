package com.birdeye.messenger.dao.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "webchat_installation_audit")
@Data
@NoArgsConstructor
public class WebchatInstallationAudit implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    private Integer id;

    @Column(name = "widget_id")
    private Integer widgetId;

    @Column(name = "status")
    private String status;

    @Column(name = "request_type")
    private String requestType;

    @Column(name = "created")
    @Temporal(TemporalType.TIMESTAMP)
    private Date created;

    @Column(name = "updated")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updated;

    public WebchatInstallationAudit(Integer widgetId, String status, String requestType, Date created, Date updated) {
        this.widgetId = widgetId;
        this.status = status;
        this.requestType = requestType;
        this.created = created;
        this.updated = updated;
    }
}