package com.birdeye.messenger.dao.entity;

import java.util.Date;

import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Data;

@Entity
@Table(name = "business_chat_widget_config_audit")
@Data
public class BusinessChatWidgetConfigAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    private Integer id;

    @Column(name = "widget_config_id")
    private Integer widgetConfigId;

    @Column(name = "user_key")
    private String userKey;

    @Column(name = "action_taken")
    private String actionTaken;

    @Column(name = "create_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createAt;

    public BusinessChatWidgetConfigAudit() {

    }

    public BusinessChatWidgetConfigAudit(Integer widgetConfigId, String userKey, String actionTaken) {
        this.widgetConfigId = widgetConfigId;
        this.userKey = userKey;
        this.actionTaken = actionTaken;
        this.createAt = new Date();
    }
}