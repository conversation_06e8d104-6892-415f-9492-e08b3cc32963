package com.birdeye.messenger.dao.repository;

import com.birdeye.messenger.dao.entity.HtmlTemplates;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface HtmlTemplatesRepository extends JpaRepository<HtmlTemplates, Integer> {

    @Query("select h from HtmlTemplates h where h.businessId is null and h.customHtmlTemplate <> 1 and h.type = :type")
    List<HtmlTemplates> findByType(@Param("type") String type);

}
