package com.birdeye.messenger.dao.repository;

import com.birdeye.messenger.dao.entity.SmsArchive;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface ArchiveSmsRepository extends JpaRepository<SmsArchive,Integer>{

    @Modifying
    @Query("DELETE FROM SmsArchive s WHERE s.customerId IN :customerId")
    void deleteByIdIn(@Param("customerId") List<Integer> customerId);
}
