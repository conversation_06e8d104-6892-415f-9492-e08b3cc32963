package com.birdeye.messenger.dao.repository;

import com.birdeye.messenger.dao.entity.Email;
import com.birdeye.messenger.dao.entity.EmailArchive;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface ArchiveEmailRepository extends JpaRepository<EmailArchive, Integer>{

    @Modifying
    @Query("DELETE FROM EmailArchive e WHERE e.customerId IN :customerId")
    void deleteByIdIn(@Param("customerId") List<Integer> customerId);
}
