package com.birdeye.messenger.dao.repository;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.birdeye.messenger.dao.entity.WebhookSubscription;
import org.springframework.transaction.annotation.Transactional;

public interface WebhookSubscriptionRepository extends JpaRepository<WebhookSubscription, Integer> {

	@Override
	Optional<WebhookSubscription> findById(Integer integer);

	WebhookSubscription findByBusinessId(Integer businessId);

	@Query(value = "select ws.* from webhook_subscription ws, subscription_event_mapping sem, webhook_event we where ws.id = sem.subscription_id and ws.business_id = :businessId and sem.event_id = we.id and we.event_name = :name", nativeQuery = true)
	WebhookSubscription findSubscriptionByBusinessIdAndEventId(@Param("businessId") Integer businessId,
			@Param("name") String name);

	@Modifying
	@Transactional
	@Query(value = "delete from subscription_event_mapping where subscription_id = :subscriptionId and event_id = :eventId", nativeQuery = true)
	void deleteEventFromSubscription(@Param("subscriptionId") Integer subcriptionId,
															   @Param("eventId") Integer eventId);
}
