/**
 * 
 */
package com.birdeye.messenger.dao.repository;

import java.util.Optional;

import jakarta.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.birdeye.messenger.dao.entity.ConversationSummaryMessageBody;
import com.birdeye.messenger.enums.ActivityType;

/**
 * <AUTHOR>
 *
 */
public interface ConversationSummaryMessageBodyRepository
        extends JpaRepository<ConversationSummaryMessageBody, Integer> {

    @Query(value = "select csmb from ConversationSummaryMessageBody csmb where csmb.messageType=:messageType")
    Optional<ConversationSummaryMessageBody> getConversationSummaryMessageBodyByMessageType(
            @Param(value = "messageType") ActivityType messageType);

    @Query(value = "delete from ConversationSummaryMessageBody csmb where csmb.messageType=:messageType")
    @Transactional
    @Modifying
    void deleteConversationSummaryMessageBodyByMessageType(
            @Param(value = "messageType") ActivityType messageType);
}
