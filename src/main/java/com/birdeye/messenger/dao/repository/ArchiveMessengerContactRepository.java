package com.birdeye.messenger.dao.repository;

import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.MessengerContactArchive;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ArchiveMessengerContactRepository extends JpaRepository<MessengerContactArchive, Integer>{

    @Modifying
    @Query("DELETE FROM MessengerContactArchive m WHERE m.id IN :contactIds")
    void deleteByIdIn(@Param("contactIds") List<Integer> contactIds);
    
}
