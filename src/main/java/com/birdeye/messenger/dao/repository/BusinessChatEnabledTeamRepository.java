package com.birdeye.messenger.dao.repository;

import com.birdeye.messenger.dao.entity.BusinessChatEnabledTeam;
//import org.omg.CORBA.INTERNAL;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Set;

public interface BusinessChatEnabledTeamRepository extends JpaRepository<BusinessChatEnabledTeam,Integer> {

    @Modifying(clearAutomatically = true)
    @Query("Delete from  BusinessChatEnabledTeam bcet where bcet.widgetConfigId = :widgetConfigId  ")
    void deleteBusinessChatEnabledTeamByWidgetConfigId(@Param("widgetConfigId") Integer widgetConfigId);
    
    @Query("Select teamId from  BusinessChatEnabledTeam bcet where bcet.widgetConfigId = :widgetConfigId")
	List<Integer> findTeamIdByWidgetConfigId(Integer widgetConfigId);
    
    @Query("Select teamId from  BusinessChatEnabledTeam bcet where bcet.widgetConfigId = :widgetConfigId order by bcet.sortOrder asc NULLS LAST")
	List<Integer> findTeamIdByWidgetConfigIdOrdered(Integer widgetConfigId);

    @Modifying(clearAutomatically = true)
    @Query("Delete from  BusinessChatEnabledTeam bcet where bcet.teamId = :teamId")
    void deleteBusinessChatEnabledTeamByTeamId(@Param("teamId") Integer teamId);

    @Query("Select DISTINCT bcet.widgetConfigId from  BusinessChatEnabledTeam bcet where bcet.teamId = :teamId")
    List<Integer> findWidgetConfigIdByTeamId(Integer teamId);
    
    @Query("Select bcet from  BusinessChatEnabledTeam bcet where bcet.teamId in :teamIds and bcet.widgetConfigId = :widgetConfigId ")
    List<BusinessChatEnabledTeam> findByWidgetConfigIdByTeamIds(Set<Integer> teamIds,Integer widgetConfigId);

}
