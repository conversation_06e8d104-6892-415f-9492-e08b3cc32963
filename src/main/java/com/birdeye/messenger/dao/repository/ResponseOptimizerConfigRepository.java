package com.birdeye.messenger.dao.repository;

import com.birdeye.messenger.dao.entity.ResponseOptimizerConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ResponseOptimizerConfigRepository extends JpaRepository<ResponseOptimizerConfig, Long> {

    Optional<ResponseOptimizerConfig> findByAgentIdAndChannel(String agentId, Integer channel);
}
