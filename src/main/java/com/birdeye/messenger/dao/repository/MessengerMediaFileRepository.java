package com.birdeye.messenger.dao.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.birdeye.messenger.dao.entity.MessengerMediaFile;

public interface MessengerMediaFileRepository extends JpaRepository<MessengerMediaFile, Integer>{

    @Query("SELECT m FROM MessengerMediaFile m where m.messageId = :msgId order by id desc")
	List<MessengerMediaFile> findByMessageId(@Param("msgId") Integer messageId);
    
    @Query("SELECT m FROM MessengerMediaFile m where m.id  in :ids order by id desc")
   	List<MessengerMediaFile> findByIds(@Param("ids") List<Integer> ids);
}
