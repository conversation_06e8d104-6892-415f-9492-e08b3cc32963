package com.birdeye.messenger.dao.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.birdeye.messenger.dao.entity.whatsapp.WhatsAppTemplateTokens;

import jakarta.transaction.Transactional;

@Repository
public interface WhatsAppTemplateTokensRepository extends JpaRepository<WhatsAppTemplateTokens, Integer> {
	
	public List<WhatsAppTemplateTokens> findByTemplateId(Integer templateId);
	
	public List<WhatsAppTemplateTokens> findByTemplateIdIn(List<Integer> templateIds);
	
	/**
	 * Check if the template tokens are initialized for the given templateId.
	 * for a token to be initialized, it should have custom_field_type as BUSINESS, CONTACT...
	 * 
	 * @param templateId
	 * @return
	 */
	@Transactional(value = Transactional.TxType.REQUIRES_NEW)
	@Query(value = "select count(*) > 0 from whatsapp_template_tokens t where t.template_id = :templateId and t.custom_field_type IS NULL", nativeQuery = true)
	public Long areTemplateTokensNotInitialized(Integer templateId);
	
	@Modifying
	@Transactional(value = Transactional.TxType.REQUIRES_NEW)
	public void deleteByTemplateId(Integer templateId);
	
	@Override
	@Transactional(value = Transactional.TxType.REQUIRES_NEW)
	public <S extends WhatsAppTemplateTokens> List<S> saveAllAndFlush(Iterable<S> entities);

}
