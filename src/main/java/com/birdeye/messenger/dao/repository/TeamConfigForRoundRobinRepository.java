package com.birdeye.messenger.dao.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.birdeye.messenger.dao.entity.TeamConfigForRoundRobin;


@Repository
public interface TeamConfigForRoundRobinRepository extends JpaRepository<TeamConfigForRoundRobin,Integer> {

    TeamConfigForRoundRobin findByAccountId(Integer accountId);

    TeamConfigForRoundRobin findByAccountIdAndTeamId(Integer accountId,Integer teamId);

    @Transactional
    void deleteByAccountId(Integer accountId);
}
