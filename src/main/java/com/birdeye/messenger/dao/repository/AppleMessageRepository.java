package com.birdeye.messenger.dao.repository;

import jakarta.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.birdeye.messenger.dao.entity.AppleMessage;


public interface AppleMessageRepository extends JpaRepository<AppleMessage, Integer> {
    
    @Query("delete from AppleMessage am where am.customerId=:customerId")
    @Transactional
    @Modifying
    void deleteByCId(@Param(value = "customerId") Integer cId);

    @Modifying
    @Transactional
    @Query("UPDATE AppleMessage am set am.messageBody = :messageBody where am.id = :messageId")
    void updateAppleMessageBody(@Param("messageBody")String messageBody,@Param("messageId")Integer messageId);
}
