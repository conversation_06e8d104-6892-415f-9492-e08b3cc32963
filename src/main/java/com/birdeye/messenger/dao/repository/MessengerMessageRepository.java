package com.birdeye.messenger.dao.repository;

import org.springframework.data.jpa.repository.JpaRepository;

import com.birdeye.messenger.dao.entity.MessengerMessage;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

import jakarta.transaction.Transactional;

public interface MessengerMessageRepository extends JpaRepository<MessengerMessage, Integer> {

	MessengerMessage findByAccountIdAndExtRefUid(Integer accountId, String extRefUid);

	@Modifying(clearAutomatically = true)
  	List<MessengerMessage> deleteByMessengerContactId(Integer mcId);

    @Modifying(clearAutomatically = true)
//	@Query("Delete from MessengerMessage mm where mm.accountId = :accountId and mm.reviewId = :reviewId")
//    List<MessengerMessage> deleteByAccountIdAndReviewId(@Param("accountId") Integer accountId,@Param("reviewId") Integer reviewId);
    List<MessengerMessage> deleteByMessengerContactIdAndReviewId(Integer mcId,Integer reviewId);
	MessengerMessage findByAccountIdAndReviewId(Integer accountId, Integer reviewId);

	List<MessengerMessage> findByMessengerContactId(int mcId);


	@Modifying
	@Query("update MessengerMessage mm set mm.messengerContactId = :toMcId where mm.messengerContactId = :fromMcId AND mm.messageType IN ('INTERNAL_NOTES','REVIEW') ")
	void updateMessages(@Param("fromMcId") Integer fromMcId, @Param("toMcId")Integer toMCId);
	
	@Modifying
	@Query("update MessengerMessage mm set mm.messengerContactId = :primaryMessengerContactId where mm.messengerContactId in :secondaryContactIds")
	void updateMessengerContactId(@Param("primaryMessengerContactId") Integer primaryMessengerContactId, @Param("secondaryContactIds")List<Integer> secondaryContactIds);

	MessengerMessage findByMessengerContactIdAndReviewId(Integer mcId, Integer reviewId);
	
	@Modifying
	@Query("DELETE FROM MessengerMessage mm WHERE mm.messengerContactId = :conversationId AND mm.reviewId IS NULL")
	void deleteAllExceptReviewByMCId(@Param("conversationId") Integer conversationId);

	MessengerMessage findByAccountIdAndSurveyResponseId(Integer accountId, Integer surveyResponseId);

	@Modifying
	void deleteBySurveyResponseId(Integer surveyResponseId);
	
	@Modifying
	@Query("DELETE FROM MessengerMessage mm WHERE mm.id in :ids")
	void deleteByIds(@Param("ids") List<Integer> ids);
	
	@Query("SELECT mm FROM MessengerMessage mm where mm.messengerContactId in :messengerContactIds AND mm.messageType in :messageTypes")
	List<MessengerMessage> findByMcIdsAndMessageTypes(@Param("messengerContactIds")List<Integer> messengerContactIds,@Param("messageTypes")List<String> messageTypes);

    @Query(value = "select mm from MessengerMessage mm where mm.reviewId=:reviewId")
    MessengerMessage findByReviewId(@Param(value = "reviewId") Integer reviewId);
    
    @Query(value = "update MessengerMessage mm set mm.accountId=:targetAccountId where mm.accountId=:sourceAccountId and mm.messengerContactId in :mcIds")
    @Transactional
    @Modifying
    Integer updateMessengerMessageInBusinessUpgradeOrDowngrade(
            @Param(value = "targetAccountId") Integer targetAccountId,
            @Param(value = "sourceAccountId") Integer sourceAccountId, @Param("mcIds") List<Integer> mcIds);

	@Modifying
	@Transactional
	@Query(value = "INSERT INTO messenger_message_archive SELECT * FROM messenger_message WHERE messenger_contact_id IN :mcIds", nativeQuery = true)
	void moveContactsToArchive(@Param("mcIds") List<Integer> mcIds);

	@Modifying
	@Query("DELETE FROM MessengerMessage mm WHERE mm.messengerContactId IN :mcIds")
	void deleteByIdIn(@Param("mcIds") List<Integer> mcIds);

	@Modifying
	@Query("update MessengerMessage mm set mm.messengerContactId = :toMcId where mm.messengerContactId = :fromMcId")
	void updateAnonymousMessages(@Param("fromMcId") Integer fromMcId, @Param("toMcId")Integer toMCId);
	
}
