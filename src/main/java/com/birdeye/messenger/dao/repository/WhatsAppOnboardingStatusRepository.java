package com.birdeye.messenger.dao.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.birdeye.messenger.dao.entity.whatsapp.WhatsAppOnboardingStatus;
import com.birdeye.messenger.dto.whatsapp.WhatsAppTemplateDto;

import jakarta.transaction.Transactional;

@Repository
public interface WhatsAppOnboardingStatusRepository extends JpaRepository<WhatsAppOnboardingStatus, Integer> {

	public Optional<WhatsAppOnboardingStatus> findByAccountIdAndBusinessId(Integer accountId, Integer businessId);
	
	@Query("SELECT distinct w.accountId FROM WhatsAppOnboardingStatus w WHERE w.wabaId in (:wabaIds)")
	public List<Integer> findAccountIdByWabaIdIn(List<String> wabaIds);
	
	public List<WhatsAppOnboardingStatus> findByWabaIdIn(List<String> wabaIds);
	
	public List<WhatsAppOnboardingStatus> findByAccountId(Integer accountId);

	public Optional<WhatsAppOnboardingStatus> findByPhoneNumberIdAndStatus(String businessPhoneNumberId, Boolean status);

	public Optional<WhatsAppOnboardingStatus> findByBusinessIdAndStatus(Integer businessId, Boolean status);
	
	public List<WhatsAppOnboardingStatus> findByAccountIdAndBusinessIdIn(Integer accountId, List<Integer> businessId);

	@Modifying
	@Transactional(value = Transactional.TxType.REQUIRES_NEW)
	@Query("UPDATE WhatsAppOnboardingStatus w SET w.businessVerified = :businessVerified WHERE w.wabaId = :wabaId")
	public void updateBusinessVerifiedByWabaId(String wabaId, Integer businessVerified);
	
	@Query("SELECT new com.birdeye.messenger.dto.whatsapp.WhatsAppTemplateDto(w.id, w.wabaId, o.wabaName, w.templateName, w.category, w.bodyText, w.language, "
			+ "w.status, w.headerMediaUrl, w.updated, o.metaBusinessId) "
			+ "FROM WhatsAppOnboardingStatus o INNER JOIN WhatsAppTemplates w ON o.wabaId=w.wabaId WHERE (:accountId IS NULL OR w.accountId = :accountId) "
			+ "AND (:searchStr IS NULL OR LOWER(w.templateName) LIKE LOWER(CONCAT('%', :searchStr, '%'))) "
			+ "AND (:categories IS NULL OR w.category IN :categories) "
			+ "AND (:statuses IS NULL OR w.status IN :statuses) "
			+ "AND (:languages IS NULL OR w.language IN :languages) "
			+ "AND (:wabaNames IS NULL OR o.wabaName IN :wabaNames) "
			+ "AND (:businessId IS NULL OR o.businessId = :businessId) "
			+ "GROUP BY w.id")
	Page<WhatsAppTemplateDto> getWhatsappTemplatesListSortByWabaName(Integer accountId, String searchStr, List<String> categories, List<String> statuses, List<String> languages, 
			List<String> wabaNames, Integer businessId, Pageable pageable);
	
	Optional<WhatsAppOnboardingStatus> findByWabaIdAndPhoneNumberId(String wabaId, String phoneNumberId);

}
