/**
 * 
 */
package com.birdeye.messenger.dao.repository;

import jakarta.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.birdeye.messenger.dao.entity.secure.messaging.SecureMessage;

/**
 * <AUTHOR>
 *
 */
@Repository
public interface SecureMessageRepository extends JpaRepository<SecureMessage, Integer> {
    
    @Query("delete from SecureMessage sm where sm.mcId=:mcId")
    @Transactional
    @Modifying
    void deleteByMcId(@Param(value = "mcId") Integer mcId);

}
