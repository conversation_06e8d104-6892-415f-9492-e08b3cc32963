package com.birdeye.messenger.dao.repository.googleBusinessMessaging;

import com.birdeye.messenger.dao.entity.googleBusinessMessaging.GoogleChatWidgetConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Optional;

public interface GoogleChatWidgetConfigRepository extends JpaRepository<GoogleChatWidgetConfig, Integer> {

    @Query("SELECT gcw from GoogleChatWidgetConfig gcw where gcw.accountId = :accountId")
    Optional<GoogleChatWidgetConfig> findByAccountId(@Param("accountId") Integer accountId);
    
}
