package com.birdeye.messenger.dao.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.birdeye.messenger.dao.entity.BusinessMoveAudit;

public interface BusinessMoveAuditRepository extends JpaRepository<BusinessMoveAudit,Integer> {

    @Query("SELECT bma from BusinessMoveAudit bma where bma.sourceBusinessId = :sbi and bma.eventType = :eventType ")
    BusinessMoveAudit findBusinessMoveAudit(@Param("sbi") Integer sourceBusinessId, @Param("eventType") String eventType );
   
}
