package com.birdeye.messenger.dao.repository;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.birdeye.messenger.dao.entity.CustomChannel;

@Repository
public interface CustomChannelRepository extends JpaRepository<CustomChannel, Integer> {

    Optional<CustomChannel> findByAccountId(Integer accountId);
}
