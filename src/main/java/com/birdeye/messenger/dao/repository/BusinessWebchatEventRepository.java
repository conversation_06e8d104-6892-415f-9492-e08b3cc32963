package com.birdeye.messenger.dao.repository;



import java.util.List;

import jakarta.websocket.server.PathParam;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.birdeye.messenger.dao.entity.BusinessWebchatEvent;

public interface BusinessWebchatEventRepository extends JpaRepository<BusinessWebchatEvent, Integer> {

    @Query(value = "select DISTINCT bwe.website from BusinessWebchatEvent bwe where bwe.widgetId = :widgetId ")
    List<String> findWebsitesByWidgetId(@Param("widgetId") Integer widgetId);


    @Query(value = "select  bwae.widget_id,group_concat( DISTINCT bwae.website) from business_webchat_event bwae where   bwae.last_update > NOW() - INTERVAL 24  HOUR group by 1", nativeQuery = true)
    List<Object[]> findDistinctEventsInLast24Hours(Pageable pageable);

    @Query(value = "select bwae.widget_id,group_concat(DISTINCT bwae.website) from business_webchat_event bwae where  widget_id = :widgetId AND bwae.last_update > NOW() - INTERVAL 24  HOUR group by 1 ", nativeQuery = true)
    List<Object[]>  findDistinctEventsInLast24HoursByWidgetId(@PathParam("widgetId") Integer widgetId);

    @Query(value = "select  count(*) from (select  bwae.widget_id,group_concat( DISTINCT bwae.website) from business_webchat_event bwae where   bwae.last_update > NOW() - INTERVAL 24  HOUR group by 1) t", nativeQuery = true)
    Integer findDistinctEventsCountInLast24Hours();

}

