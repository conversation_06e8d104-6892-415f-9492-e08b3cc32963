package com.birdeye.messenger.dao.repository;

import com.birdeye.messenger.dao.entity.robin.RobinAutoReplyConfig;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RobinAutoReplyConfigRepository extends JpaRepository<RobinAutoReplyConfig,Integer> {

    List<RobinAutoReplyConfig> findByBusinessIdAndChannel(Integer businessId,String channel);

    @Query("SELECT rc FROM RobinAutoReplyConfig rc WHERE (rc.businessId = :businessId OR rc.businessId = :enterpriseId) AND (rc.channel = :channel OR rc.channel = 'all')")
    List<RobinAutoReplyConfig> findByBusinessIdAndEnterpriseId(@Param("businessId") Integer businessId, @Param("enterpriseId") Integer enterpriseId, @Param("channel") String channel);


    List<RobinAutoReplyConfig> findByBusinessId(Integer businessId);

    @Query("SELECT rc FROM RobinAutoReplyConfig rc WHERE rc.businessId in :businessIds and rc.channel = :channel")
    List<RobinAutoReplyConfig> findAllByBusinessIdsAndChannel(@Param("businessIds")List<Integer> businessIds, @Param("channel") String channel);

    void deleteAllByBusinessIdInAndChannel(List<Integer> businessIds, String channel);

    @Query("SELECT rc FROM RobinAutoReplyConfig rc WHERE rc.businessId IN :businessIds ORDER BY rc.channel DESC")
    List<RobinAutoReplyConfig> findAllByBusinessIdInOrderByChannelAtDesc(@Param("businessIds") List<Integer> businessIds);

}
