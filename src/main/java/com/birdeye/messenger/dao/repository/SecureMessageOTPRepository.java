/**
 * 
 */
package com.birdeye.messenger.dao.repository;

import java.util.Optional;

import jakarta.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.birdeye.messenger.dao.entity.secure.messaging.SecureMessageOTP;

/**
 * <AUTHOR>
 *
 */
@Repository
public interface SecureMessageOTPRepository extends JpaRepository<SecureMessageOTP, Integer> {

    @Query(value = "select smo from SecureMessageOTP smo where smo.secureMessagingLinkId=:secureMessagingLinkId and smo.otp=:otp")
    Optional<SecureMessageOTP> findSecureMessageOTPBySecureMessagingLinkId(
            @Param(value = "secureMessagingLinkId") Integer secureMessagingLinkId, @Param(value = "otp") String otp);

    @Query(value = "delete from SecureMessageOTP smo where smo.secureMessagingLinkId=:secureMessagingLinkId")
    @Transactional
    @Modifying
    void deleteOtpByLinkId(@Param(value = "secureMessagingLinkId") Integer secureMessagingLinkId);
}
