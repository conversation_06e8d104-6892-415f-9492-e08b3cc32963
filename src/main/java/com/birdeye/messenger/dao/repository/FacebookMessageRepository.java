package com.birdeye.messenger.dao.repository;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.birdeye.messenger.dao.entity.FacebookMessage;


public interface FacebookMessageRepository extends JpaRepository<FacebookMessage, Integer>{
	
	@Query("SELECT f FROM FacebookMessage f WHERE f.messageId = :messageId")
	FacebookMessage getMessageByMessageId(@Param("messageId") String messageId);
	
	@Query("SELECT f FROM FacebookMessage f WHERE f.customerId = :customerId")
	List<FacebookMessage> getMessageByCustomerId(@Param("customerId") Integer customerId);

	@Query("SELECT f FROM FacebookMessage f WHERE f.recipientFacebookId = :recipientFacebookId and f.customerId= :customerId")
	List<FacebookMessage> getMessageByPageIdAndBusinessId(@Param("recipientFacebookId") String recipientFacebookId,@Param("customerId") Integer customerId);


	@Modifying(clearAutomatically = true)
	@Query("UPDATE FacebookMessage f set f.status= 'read' where f.status in ('delivered','sent') and senderFacebookId =:senderFacebookId and recipientFacebookId =:recipientFacebookId and customerId =:cId and createDate<:createDate")
	void updateAllMessageWithStatusDelivered(@Param("senderFacebookId") String senderFacebookId,@Param("recipientFacebookId") String recipientFacebookId,@Param("createDate") Date createDate,@Param("cId") Integer cId);

	@Modifying(clearAutomatically = true)
    void deleteByCustomerId(Integer customerId);
	
	@Modifying
	@Query("update FacebookMessage fb set fb.customerId = :primaryMessengerContactId where fb.customerId in :secondaryContactIds")
	void updateMessengerContactId(@Param("primaryMessengerContactId") Integer primaryMessengerContactId, @Param("secondaryContactIds")List<Integer> secondaryContactIds);
	
	@Modifying
	@Query("UPDATE FacebookMessage fb set fb.messageBody = :messageBody where fb.id = :messageId")
	void updateFacebookMessageBody(@Param("messageBody")String messageBody,@Param("messageId")Integer messageId);
		

}
