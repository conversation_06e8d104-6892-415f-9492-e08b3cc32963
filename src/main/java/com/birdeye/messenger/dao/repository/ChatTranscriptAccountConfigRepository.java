package com.birdeye.messenger.dao.repository;

import com.birdeye.messenger.dao.entity.ChatTranscriptAccountConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import jakarta.transaction.Transactional;
import java.util.List;

@Repository
public interface ChatTranscriptAccountConfigRepository extends JpaRepository<ChatTranscriptAccountConfig,Integer> {

    @Transactional
    void deleteAllByAccountId(Integer accountId);

    List<ChatTranscriptAccountConfig> findAllByAccountId(Integer accountId);
}
