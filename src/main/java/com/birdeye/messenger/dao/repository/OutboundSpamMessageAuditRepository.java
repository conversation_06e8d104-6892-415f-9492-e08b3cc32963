package com.birdeye.messenger.dao.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.birdeye.messenger.dao.entity.OutboundSpamMessageAudit;

public interface OutboundSpamMessageAuditRepository extends JpaRepository<OutboundSpamMessageAudit, Integer> {

	@Query(value = "SELECT * from outbound_spam_message_audit osma where osma.created_at > NOW() - INTERVAL 24  HOUR and osma.account_id = :accountId", nativeQuery = true)
	List<OutboundSpamMessageAudit> findAllByAccountId(@Param("accountId") Integer accountId);
}
