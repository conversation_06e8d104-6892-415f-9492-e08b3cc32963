package com.birdeye.messenger.dao.repository;

import java.util.List;
import java.util.Set;

import com.birdeye.messenger.dto.BusinessChatBotDataDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.birdeye.messenger.dao.entity.BusinessChatWidgetConfig;
import com.birdeye.messenger.dto.BusinessWebchatWidgetDetails;
import com.birdeye.messenger.dto.WebChatConfigForExternalIdResponse;

public interface BusinessChatWidgetConfigRepository extends JpaRepository<BusinessChatWidgetConfig, Integer>{

    @Query("SELECT bcwc from BusinessChatWidgetConfig bcwc where bcwc.enterpriseId = :enterpriseId")
    List<BusinessChatWidgetConfig> findwidgetConfigsByEnterpriseId(@Param("enterpriseId") Integer enterpriseId);

    @Query("SELECT bcwc from BusinessChatWidgetConfig bcwc where bcwc.id = :id")
    BusinessChatWidgetConfig getDefaultConfig(@Param("id") Integer id);

    @Query("SELECT COUNT(bcwc.id) FROM BusinessChatWidgetConfig bcwc where (bcwc.enterpriseId = :enterpriseId OR bcwc.businessId = :businessId) AND (bcwc.widgetName like :name OR bcwc.widgetName like CONCAT(:name, '(', '%', ')'))")
    Integer getNameCount(@Param("name") String name,@Param("businessId") Long businessId,@Param("enterpriseId") Integer enterpriseId);

    @Query("SELECT COUNT(bcwc.id) FROM BusinessChatWidgetConfig bcwc where bcwc.id != :id and (bcwc.enterpriseId = :enterpriseId OR bcwc.businessId = :businessId) AND (bcwc.widgetName like :name OR bcwc.widgetName like CONCAT(:name, '(', '%', ')'))")
    Integer getNameCount(@Param("name") String name,@Param("businessId") Long businessId,@Param("enterpriseId") Integer enterpriseId,@Param("id") Integer id);


    @Query("SELECT new com.birdeye.messenger.dto.BusinessWebchatWidgetDetails(w.id, w.widgetName, w.enabled, w.businessId,w.externalId,w.allLocationDisabled,CASE WHEN (w.installed IS NULL OR w.installed = 0) THEN 'Not Installed'  WHEN (w.installed = 1 AND w.enabled = 1)  THEN 'Active'  WHEN (w.installed = 1 AND w.enabled = 0) THEN 'Disabled' END AS status) "
            +"FROM BusinessChatWidgetConfig w "
            +"WHERE w.businessId = :businessId")
    Page<BusinessWebchatWidgetDetails> findWidgetsByBusinessIdIncludeDefault(@Param("businessId") Long businessId, Pageable pageable);



    @Query("SELECT new com.birdeye.messenger.dto.BusinessWebchatWidgetDetails(w.id, w.widgetName, w.enabled, w.businessId,w.externalId,w.allLocationDisabled,CASE WHEN (w.installed IS NULL OR w.installed = 0) THEN 'Not Installed'  WHEN (w.installed = 1 AND w.enabled = 1)  THEN 'Active'  WHEN (w.installed = 1 AND w.enabled = 0) THEN 'Disabled' END AS status) FROM BusinessChatWidgetConfig w "
    		+"WHERE w.businessId = :businessId AND w.widgetName like CONCAT('%', :searchText, '%')")
    Page<BusinessWebchatWidgetDetails> findWidgetsByBusinessIdIn(@Param("businessId") Long businessId, @Param("searchText") String searchText, Pageable pageable);

    @Modifying(clearAutomatically = true)
    @Query("Update BusinessChatWidgetConfig bcwc set bcwc.enabled = :value where bcwc.id = :id ")
    void enableorDisableBusinessChatWidgetConfig(@Param("id") Integer id,@Param("value") Integer value);

    @Modifying(clearAutomatically = true)
    @Query("Update BusinessChatWidgetConfig bcwc set bcwc.enabled = :value where bcwc.id in :ids ")
    void enableorDisableBusinessChatWidgetConfigs(@Param("ids") List<Integer> id,@Param("value") Integer value);
    
    @Query
    List<BusinessChatWidgetConfig> findByBusinessId(@Param("businessId") Long businessId);
    
    @Query("SELECT new com.birdeye.messenger.dto.BusinessWebchatWidgetDetails(w.id, w.widgetName, w.enabled, w.businessId, w.installed) "
    		+"FROM BusinessChatWidgetConfig w "
    		+"WHERE w.businessId is null")
    BusinessWebchatWidgetDetails findDefaultWidgetDetails();

    @Query("SELECT bcwc from BusinessChatWidgetConfig bcwc where bcwc.businessId = :businessId")
    List<BusinessChatWidgetConfig> getWebChatConfigByBusinessNumber(@Param("businessId") Long businessId);

    @Query("SELECT bcwc from BusinessChatWidgetConfig bcwc where bcwc.businessId in :businessIds")
    List<BusinessChatWidgetConfig> getWebChatConfigByBusinessNumbers(@Param("businessIds") Set<Long> businessIds);

    @Query("SELECT bcwc from BusinessChatWidgetConfig bcwc where bcwc.externalId = :externalId and bcwc.enabled = 1 ")
    List<BusinessChatWidgetConfig> getWebChatConfigByExternalIdAndEnabled(@Param("externalId") Long externalId);

    @Query("SELECT new com.birdeye.messenger.dto.WebChatConfigForExternalIdResponse(bcwc.id,bcwc.businessId,bcwc.externalId) from BusinessChatWidgetConfig bcwc where bcwc.externalId = :externalId")
    List<WebChatConfigForExternalIdResponse> getWebChatConfigByExternalId(@Param("externalId") Long externalId);

    @Query("SELECT bcwc from BusinessChatWidgetConfig bcwc where bcwc.businessId = :businessId and bcwc.enabled = 1 ")
    List<BusinessChatWidgetConfig> getWebChatConfigByBusinessNumberAndEnabled(@Param("businessId") Long businessId);

    @Query("SELECT bcwc.id from BusinessChatWidgetConfig bcwc where bcwc.enabled = 1 ")
    List<Integer> getEnabledWebChatConfigId();

    @Query(value = "SELECT bcwc.id from business_chat_widget_config bcwc where bcwc.status_updated_on < NOW() - INTERVAL 24  HOUR and bcwc.enabled=1", nativeQuery = true)
    List<Integer> findWidgetIdsByStatusUpdatedOn();

    @Query("Select bcw.id from BusinessChatWidgetConfig bcw left join LiveChatWidgetConfig lcwc on bcw.id=lcwc.widgetId where lcwc.widgetId is null order by bcw.id asc")
    List<Integer> getAllWidgetIds();

    @Query("SELECT bcwc from BusinessChatWidgetConfig bcwc where bcwc.enterpriseId is not null ")
    List<BusinessChatWidgetConfig> getWebChatConfigByEnt();

    @Modifying(clearAutomatically = true)
    @Query("Update BusinessChatWidgetConfig bcwc set bcwc.enabled = 0 ,bcwc.allLocationDisabled = 1  where bcwc.id in :ids ")
    void disableBusinessChatWidgetConfigsAndLocations(@Param("ids") List<Integer> id);

    @Query("SELECT bcwc from BusinessChatWidgetConfig bcwc where bcwc.id in :widgetIds")
    List<BusinessChatWidgetConfig> getWebChatConfigByWidgetIds(@Param("widgetIds") List<Integer> widgetIds);

    @Query("SELECT  bcwc from BusinessChatWidgetConfig bcwc where bcwc.businessId in :businessIds and bcwc.enabled = :enabled and bcwc.livechatEnabled = :livechatEnabled")
    List<BusinessChatWidgetConfig> getWebChatConfigByBusinessIdForLiveChatEnabled(@Param("businessIds") List<Long> businessIds,@Param("enabled") Integer enabled,@Param("livechatEnabled") Integer livechatEnabled);

    @Query("SELECT bcwc from BusinessChatWidgetConfig bcwc where bcwc.businessId in :businessIds and bcwc.enabled = :enabled and bcwc.chatbotEnabled = :chatbotEnabled")
    List<BusinessChatWidgetConfig> getWebChatConfigByBusinessIdForChatBotEnabled(@Param("businessIds") List<Long> businessIds,@Param("enabled") Integer enabled,@Param("chatbotEnabled") Integer chatBotEnabled);

    @Query("SELECT bcwc from BusinessChatWidgetConfig bcwc where bcwc.businessId in :businessIds and bcwc.enabled = :enabled and bcwc.installed = :installed")
    List<BusinessChatWidgetConfig> getWebChatConfigByBusinessIdForInstalled (@Param("businessIds") List<Long> businessIds,@Param("enabled") Integer enabled,@Param("installed") Integer installed);
    
	@Query("SELECT new com.birdeye.messenger.dto.BusinessChatBotDataDto( b.id, b.businessId, b.chatbotEnabled) FROM BusinessChatWidgetConfig b WHERE b.id IN (SELECT DISTINCT c.businessChatWidgetId FROM BusinessChatEnabledLocation c WHERE c.businessNumber IN :businessNumbers) AND b.installed = 1 AND b.enabled = 1")
	List<BusinessChatBotDataDto> findByLocationNumberAndInstalledAndEnabled(@Param("businessNumbers") List<Long> businessNumbers);

	@Query("SELECT new com.birdeye.messenger.dto.BusinessChatBotDataDto( b.id, b.businessId, b.chatbotEnabled ) FROM BusinessChatWidgetConfig b WHERE b.businessId = :businessNumber AND b.installed = 1 AND b.enabled = 1")
	List<BusinessChatBotDataDto> findByBusinessNumberAndInstalledAndEnabled(@Param("businessNumber") Long businessNumber);
    
    
}
