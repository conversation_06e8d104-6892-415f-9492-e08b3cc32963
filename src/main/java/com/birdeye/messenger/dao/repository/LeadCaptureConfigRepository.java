package com.birdeye.messenger.dao.repository;

import com.birdeye.messenger.dao.entity.LeadCaptureConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface LeadCaptureConfigRepository extends JpaRepository<LeadCaptureConfig, Long> {

    Optional<LeadCaptureConfig> findByAgentIdAndChannel(String agentId, Integer channel);
}
