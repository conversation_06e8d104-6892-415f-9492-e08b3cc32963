package com.birdeye.messenger.dao.repository;

import com.birdeye.messenger.dao.entity.BusinessChatWidgetConfig;
import com.birdeye.messenger.dao.entity.BusinessChatWidgetUserProfile;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

public interface BusinessChatWidgetUserProfileRepository extends JpaRepository<BusinessChatWidgetUserProfile,Integer> {


    @Modifying(clearAutomatically = true)
    @Query("Delete from  BusinessChatWidgetUserProfile  bcwup where bcwup.businessChatWidgetId = :widgetConfigId  ")
    void deleteBusinessChatWidgetUserProfileByWidgetConfigID(@Param("widgetConfigId") Integer widgetConfigId);

	List<BusinessChatWidgetUserProfile> findByBusinessChatWidgetId(Integer widgetId);

}
