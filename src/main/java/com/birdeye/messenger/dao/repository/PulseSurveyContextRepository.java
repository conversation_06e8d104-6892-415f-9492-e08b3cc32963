package com.birdeye.messenger.dao.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.birdeye.messenger.dao.entity.PulseSurveyContext;

/**
 * <AUTHOR>
 *
 */
public interface PulseSurveyContextRepository extends JpaRepository<PulseSurveyContext, Integer> {

	@Override
	Optional<PulseSurveyContext> findById(Integer id);

	@Query("select psc from PulseSurveyContext psc where psc.customerId=:customerId and psc.status in :status")
	List<PulseSurveyContext> findByCustomerIdAndStatusIn(@Param(value="customerId") Integer customerId,
			@Param(value="status") List<String> status);

	 @Query(value = "select * from pulse_survey_context where customer_id=:customerId order by last_updated_date desc limit 1",nativeQuery = true)
	 Optional<PulseSurveyContext> findByCustomerIdOrderByLastUpdateDate(@Param("customerId") Integer customerId);
	

}
