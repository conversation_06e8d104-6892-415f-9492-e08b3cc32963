package com.birdeye.messenger.dao.repository;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;

import com.birdeye.messenger.dao.entity.ReceiveMsgBypassInboxAudit;

/**
 * <AUTHOR>
 *
 */
public interface ReceiveMsgBypassInboxRepository  extends JpaRepository<ReceiveMsgBypassInboxAudit, Integer> {

	@Override
	Optional<ReceiveMsgBypassInboxAudit> findById(Integer id);
	
}
