package com.birdeye.messenger.dao.repository;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.birdeye.messenger.dao.entity.VoiceCall;

public interface VoiceCallRepository extends JpaRepository<VoiceCall, Integer> {

    @Modifying(clearAutomatically = true)
    void deleteByCustomerId(Integer customerId);

    @Query("SELECT COUNT(v) FROM VoiceCall v where v.businessId in :businessIds and v.createDate >= :startDate and v.recordingUrl is not null")
    Integer countVoiceCallForBusinessIdsAndCreateDate(@Param("businessIds") List<Integer> businessIds, @Param("startDate") Date startDate);

    @Query("SELECT COUNT(v) FROM VoiceCall v where v.businessId in :businessIds and v.recordingUrl is not null")
    Integer countVoiceCallForBusinessIds(@Param("businessIds")List<Integer> businessIds);
    
    @Query("SELECT v from VoiceCall v where v.callSid = :callSid")
    List<VoiceCall> findByCallSid(@Param("callSid") String callSid);

    @Modifying
    @Query("UPDATE VoiceCall v set v.transcription = :messageBody where v.id = :messageId")
    void updateVoicecallMessageBody(String messageBody, Integer messageId);
}
