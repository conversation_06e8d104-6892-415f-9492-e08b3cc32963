package com.birdeye.messenger.dao.repository;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;

import com.birdeye.messenger.dao.entity.GPTResponseFeedbackAudit;

/**
 * <AUTHOR>
 *
 */
public interface GPTResponseFeedbackAuditRepository extends JpaRepository<GPTResponseFeedbackAudit, Integer> {

	@Override
	Optional<GPTResponseFeedbackAudit> findById(Integer id);
	
}
