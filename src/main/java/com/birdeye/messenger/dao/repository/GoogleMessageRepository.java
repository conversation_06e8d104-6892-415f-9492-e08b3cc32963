package com.birdeye.messenger.dao.repository;

import com.birdeye.messenger.dao.entity.GoogleMessage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Optional;

import jakarta.transaction.Transactional;


public interface GoogleMessageRepository extends JpaRepository<GoogleMessage, Integer> {

    @Query("SELECT g from GoogleMessage g where g.requestId = :requestId")
    Optional<GoogleMessage> findByRequestId(@Param("requestId") String requestId);
    
    @Query("delete from GoogleMessage g where g.customerId=:customerId")
    @Transactional
    @Modifying
    void deleteGoogleMessagesByCId(@Param(value = "customerId") Integer customerId);

    @Modifying
    @Query("UPDATE GoogleMessage gm set gm.messageBody = :messageBody where gm.id = :messageId")
    void updateGoogleMessageBody(@Param("messageBody")String messageBody,@Param("messageId")Integer messageId);

}
