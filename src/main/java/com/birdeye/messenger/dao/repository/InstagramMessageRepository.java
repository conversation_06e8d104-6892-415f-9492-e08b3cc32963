package com.birdeye.messenger.dao.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;

import com.birdeye.messenger.dao.entity.InstagramMessage;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Optional;


public interface InstagramMessageRepository extends JpaRepository<InstagramMessage, Integer>{
	
	@Modifying(clearAutomatically = true)
    void deleteByMessengerContactId(Integer mcId);
    
    InstagramMessage findByMessageId(String messageId);

    Optional<InstagramMessage> findById(Integer id);

    @Modifying
    @Query("UPDATE InstagramMessage im set im.messageBody = :messageBody where im.id = :messageId")
    void updateInstagramMessageBody(@Param("messageBody")String messageBody,@Param("messageId")Integer messageId);

}
