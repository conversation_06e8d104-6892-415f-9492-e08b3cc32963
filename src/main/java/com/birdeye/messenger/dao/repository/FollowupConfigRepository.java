package com.birdeye.messenger.dao.repository;

import com.birdeye.messenger.dao.entity.FollowupConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface FollowupConfigRepository extends JpaRepository<FollowupConfig, Long> {

    Optional<FollowupConfig> findByAgentIdAndChannel(String agentId, Integer channel);
}
