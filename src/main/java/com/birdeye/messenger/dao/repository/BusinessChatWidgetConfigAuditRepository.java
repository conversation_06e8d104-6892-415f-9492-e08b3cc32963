package com.birdeye.messenger.dao.repository;

import com.birdeye.messenger.dao.entity.BusinessChatWidgetConfig;
import com.birdeye.messenger.dao.entity.BusinessChatWidgetConfigAudit;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface BusinessChatWidgetConfigAuditRepository extends JpaRepository<BusinessChatWidgetConfigAudit, Integer>{



}
