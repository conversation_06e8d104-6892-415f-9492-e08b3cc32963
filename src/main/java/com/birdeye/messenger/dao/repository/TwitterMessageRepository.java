package com.birdeye.messenger.dao.repository;

import com.birdeye.messenger.dao.entity.TwitterMessage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface TwitterMessageRepository extends JpaRepository<TwitterMessage, Integer>{

    @Modifying
    @Query("UPDATE TwitterMessage t set t.messageBody = :messageBody where t.id = :messageId")
    void updateTwitterMessageBody(@Param("messageBody")String messageBody,@Param("messageId")Integer messageId);
}
