package com.birdeye.messenger.dao.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;

import com.birdeye.messenger.dao.entity.WhatsappMessage;
import org.springframework.data.jpa.repository.Query;

/**
 * <AUTHOR>
 *
 */
public interface WhatsappMessageRepository extends JpaRepository<WhatsappMessage, Integer>{


	WhatsappMessage findByMessageId(String messageId);

	@Modifying(clearAutomatically = true)
	void deleteByMessengerContactId(Integer mcId);

	@Modifying
	@Query("UPDATE WhatsappMessage w set w.messageBody = :messageBody where w.id = :messageId")
	void updateWAMessageBody(String messageBody, Integer messageId);
}
