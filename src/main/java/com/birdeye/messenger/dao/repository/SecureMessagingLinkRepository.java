/**
 * 
 */
package com.birdeye.messenger.dao.repository;

import java.util.List;
import java.util.Optional;

import jakarta.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.birdeye.messenger.dao.entity.secure.messaging.SecureMessagingLink;

/**
 * <AUTHOR>
 *
 */
@Repository
public interface SecureMessagingLinkRepository
        extends JpaRepository<SecureMessagingLink, Integer> {

    @Query(value = "select sml from SecureMessagingLink sml where sml.mcId=:mcId and sml.cId=:cId")
    Optional<SecureMessagingLink> getMessengerContactSecureMessagingLinkByMcIdAndCId(
            @Param(value = "mcId") Integer mcId, @Param(value = "cId") Integer cId);

    @Query(value = "select sml from SecureMessagingLink sml where sml.mcId in (:mcIds) and sml.cId in (:cIds)")
    List<SecureMessagingLink> getMessengerContactSecureMessagingLinkByMcIdsAndCIds(
            @Param(value = "mcIds") List<Integer> mcIds, @Param(value = "cIds") List<Integer> cIds);
    

    @Modifying(clearAutomatically = true)
    List<SecureMessagingLink> deleteBymcId(Integer mcId);

}
