package com.birdeye.messenger.dao.repository;

import com.birdeye.messenger.dao.entity.CustomFieldsWidget;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CustomFieldWidgetRepository extends JpaRepository<CustomFieldsWidget, Integer> {
    @Override
    Optional<CustomFieldsWidget> findById(Integer integer);

    @Query(value = "SELECT cf FROM CustomFieldsWidget cf where cf.widgetId = :widgetId and cf.businessId = :businessId")
    List<CustomFieldsWidget> findByWidgetIdAndBusinessId(@Param("widgetId") Integer widgetId, @Param("businessId") Long businessId);

    @Query(value = "SELECT cf FROM CustomFieldsWidget cf where cf.fieldId = :fieldId")
    List<CustomFieldsWidget> findByfieldId(@Param("fieldId") Integer fieldId);

    @Modifying(flushAutomatically = true)
    @Query(value = "DELETE FROM CustomFieldsWidget cf where cf.widgetId = :widgetId and cf.businessId = :businessId")
    void deleteByWidgetIdAndBusinessId(@Param("widgetId") Integer widgetId, @Param("businessId") Long businessId);


}
