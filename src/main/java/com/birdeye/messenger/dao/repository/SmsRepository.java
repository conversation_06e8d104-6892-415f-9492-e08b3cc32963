package com.birdeye.messenger.dao.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.birdeye.messenger.dao.entity.Sms;

import jakarta.transaction.Transactional;
import java.util.List;

public interface SmsRepository extends JpaRepository<Sms, Integer> {

    @Query("SELECT s from Sms s where s.customerId = :customerId")
    List<Sms> findByCustomerId(@Param("customerId") Integer customerId);

    @Query("SELECT s from Sms s where s.messageSid = :messageSid")
    List<Sms> findByMessageSid(@Param("messageSid") String messageSid);

    @Modifying(clearAutomatically = true)
    void deleteByCustomerId(Integer customerId);

    @Query("SELECT s from Sms s where s.reviewRequestId = :reviewRequestId")
    Sms findByReviewRequestId(@Param("reviewRequestId") Long reviewRequestId);

	Sms findByReviewRequestIdAndSmsType(Long reviewRequestId, String smsType);

    @Modifying
    @Transactional
    @Query(value = "INSERT INTO sms_archive SELECT * FROM sms WHERE customer_id IN :customerId", nativeQuery = true)
    void moveContactsToArchive(@Param("customerId") List<Integer> customerId);

    @Modifying
    @Query("DELETE FROM Sms s WHERE s.customerId IN :customerId")
    void deleteByIdIn(@Param("customerId") List<Integer> customerId);

    @Modifying
    @Query("UPDATE Sms s set s.messageBody = :messageBody where s.smsId = :messageId")
    void updateSmsMessageBody(@Param("messageBody")String messageBody,@Param("messageId")Integer messageId);

}
