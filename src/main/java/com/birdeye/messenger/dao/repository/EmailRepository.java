/**
 * 
 */
package com.birdeye.messenger.dao.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.birdeye.messenger.dao.entity.Email;

import jakarta.transaction.Transactional;

/**
 * <AUTHOR>
 *
 */
public interface EmailRepository extends JpaRepository<Email, Integer> {

	@Query("SELECT e from Email e where e.customerId = :customerId")
	List<Email> findByCustomerId(@Param("customerId") Integer customerId);

	Email findByReviewRequestId(Long reviewRequestId);

	@Modifying(clearAutomatically = true)
	void deleteByCustomerId(Integer customerId);

	@Modifying
	@Transactional
	@Query(value = "INSERT INTO email_archive SELECT * FROM email WHERE customer_id IN :customerId", nativeQuery = true)
	void moveContactsToArchive(@Param("customerId") List<Integer> customerId);

	@Modifying
	@Query("DELETE FROM Email e WHERE e.customerId IN :customerId")
	void deleteByIdIn(@Param("customerId") List<Integer> customerId);

	@Modifying
	@Query("UPDATE Email e set e.messageBody = :messageBody where e.Id = :messageId")
	void updateEmailMessageBody(@Param("messageBody")String messageBody,@Param("messageId")Integer messageId);

}
