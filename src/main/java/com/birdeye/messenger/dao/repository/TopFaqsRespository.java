/**
 * 
 */
package com.birdeye.messenger.dao.repository;

import java.util.List;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.birdeye.messenger.dao.entity.TopFAQs;

/**
 * <AUTHOR>
 *
 */
@Repository
public interface TopFaqsRespository extends JpaRepository<TopFAQs, Integer> {

    @Query(value = "select count(t) from TopFAQs t where t.accountId=:accountId")
    Integer getCountOfFAQByAccountId(@Param(value = "accountId") Integer accountId);

    @Query(value = "select t from TopFAQs t where t.id in (:ids) and t.accountId=:accountId")
    List<TopFAQs> findByIdsAndAccountId(@Param(value = "ids") List<Integer> ids,
            @Param(value = "accountId") Integer accountId);

    List<TopFAQs> findAllByAccountId(Integer accountId, Pageable pageRequest);

}
