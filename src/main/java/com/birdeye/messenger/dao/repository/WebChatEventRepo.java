package com.birdeye.messenger.dao.repository;


import com.birdeye.messenger.dao.entity.BusinessWebchatEvent;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface WebChatEventRepo extends JpaRepository<BusinessWebchatEvent, Integer> {

//    List<BusinessWebChatEvent> findByWidgetIdAndGreaterThanLastUpdate(Integer widgetId, Date lastUpdated);
}
