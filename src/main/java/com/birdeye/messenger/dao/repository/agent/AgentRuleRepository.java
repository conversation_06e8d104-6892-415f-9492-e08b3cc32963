package com.birdeye.messenger.dao.repository.agent;

import com.birdeye.messenger.dao.entity.agent.AgentRule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AgentRuleRepository extends JpaRepository<AgentRule, Long> {

    List<AgentRule> findByAgentIdOrderByIdAsc(String agentId);

    void deleteById(Long id);
} 