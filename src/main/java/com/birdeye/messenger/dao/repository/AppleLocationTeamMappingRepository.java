/**
 * 
 */
package com.birdeye.messenger.dao.repository;

import java.util.List;
import java.util.Optional;

import jakarta.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.birdeye.messenger.dao.entity.AppleLocationTeamMapping;

/**
 * <AUTHOR>
 *
 */
public interface AppleLocationTeamMappingRepository extends JpaRepository<AppleLocationTeamMapping, Integer> {

    @Query(value = "select altm from AppleLocationTeamMapping altm where altm.accountId=:accountId")
    List<AppleLocationTeamMapping> getAppleLocationTeamMappingsByAccountId(
            @Param(value = "accountId") Integer accountId);

    @Query(value = "select altm from AppleLocationTeamMapping altm where altm.accountId=:accountId and altm.businessId in :businessIds")
    List<AppleLocationTeamMapping> getAppleLocationTeamMappingsByAccountIdAndBusinessIds(
            @Param(value = "accountId") Integer accountId, @Param(value = "businessIds") List<Integer> businessIds);

    @Query(value = "delete from AppleLocationTeamMapping altm where altm.accountId=:accountId")
    @Transactional
    @Modifying
    void deleteAppleLocationTeamMappingsByAccountId(@Param(value = "accountId") Integer accountId);

    @Query(value = "delete from AppleLocationTeamMapping altm where altm.accountId=:accountId and altm.businessId in :businessIds")
    @Transactional
    @Modifying
    void deleteAppleLocationTeamMappingsByAccountIdAndBusinessIds(@Param(value = "accountId") Integer accountId,
            @Param(value = "businessIds") List<Integer> businessIds);
    
    @Query(value = "select altm from AppleLocationTeamMapping altm where altm.accountId=:accountId and altm.businessId=:businessId")
    Optional<AppleLocationTeamMapping> getAppleLocationTeamMappingByAccountIdAndBusinessId(
            @Param(value = "accountId") Integer accountId, @Param(value = "businessId") Integer businessId);


}
