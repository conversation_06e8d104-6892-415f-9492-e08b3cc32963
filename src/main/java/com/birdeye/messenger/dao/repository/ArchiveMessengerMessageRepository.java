package com.birdeye.messenger.dao.repository;

import com.birdeye.messenger.dao.entity.MessengerMessage;
import com.birdeye.messenger.dao.entity.MessengerMessageArchive;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface ArchiveMessengerMessageRepository extends JpaRepository<MessengerMessageArchive, Integer>{

    @Modifying
    @Query("DELETE FROM MessengerMessageArchive mm WHERE mm.messengerContactId IN :mcIds")
    void deleteByIdIn(@Param("mcIds") List<Integer> mcIds);
}
