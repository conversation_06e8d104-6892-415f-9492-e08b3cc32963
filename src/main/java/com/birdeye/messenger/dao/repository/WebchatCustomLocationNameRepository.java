package com.birdeye.messenger.dao.repository;



import java.util.List;
import java.util.Set;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.birdeye.messenger.dao.entity.WebchatCustomLocationName;

public interface WebchatCustomLocationNameRepository extends JpaRepository<WebchatCustomLocationName, Integer> {

	@Query("SELECT w FROM WebchatCustomLocationName w WHERE w.accountId = :accountId")
	List<WebchatCustomLocationName> getByAccountId(@Param("accountId") Integer accountId);
	
	@Query("SELECT w FROM WebchatCustomLocationName w WHERE w.businessId in :bizIds")
	List<WebchatCustomLocationName> getByBusinessIds(@Param("bizIds") Set<Integer> bizIds);
}

