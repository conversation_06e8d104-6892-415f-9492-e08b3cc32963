package com.birdeye.messenger.dao.repository;

import com.birdeye.messenger.dao.entity.ElasticQuery;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface ElasticQueryRepository extends JpaRepository<ElasticQuery, Integer> {

    @Query("SELECT q FROM ElasticQuery q where q.queryName = :name")
    List<ElasticQuery> findByName(@Param("name") String name);

}
