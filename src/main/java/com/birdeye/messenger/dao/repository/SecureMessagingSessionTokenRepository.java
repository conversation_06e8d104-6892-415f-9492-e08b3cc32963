/**
 * 
 */
package com.birdeye.messenger.dao.repository;

import java.util.List;
import java.util.Optional;

import jakarta.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.birdeye.messenger.dao.entity.secure.messaging.SecureMessagingSessionToken;

/**
 * <AUTHOR>
 *
 */
@Repository
public interface SecureMessagingSessionTokenRepository extends JpaRepository<SecureMessagingSessionToken, Integer> {

    @Query(value = "select smst from SecureMessagingSessionToken smst where smst.sessionToken=:sessionToken")
    Optional<SecureMessagingSessionToken> getSecureMessagingSessionTokenBySessionToken(
            @Param(value = "sessionToken") String sessionToken);

    @Query(value = "select smst from SecureMessagingSessionToken smst where smst.mcId=:mcId and smst.cId=:cId and smst.sessionToken!=:sessionToken and smst.expiryTime > now()")
    List<SecureMessagingSessionToken> getSecureMessagingSessionTokenByMcIdAndCId(
            @Param(value = "mcId") Integer mcId, @Param(value = "cId") Integer cId,
            @Param(value = "sessionToken") String sessionToken);

    @Query(value = "delete from SecureMessagingSessionToken smst where smst.sessionToken in (:sessionTokens)")
    @Modifying
    void deleteSessionTokens(@Param(value = "sessionTokens") List<String> sessionTokens);
    
    @Query(value = "delete from SecureMessagingSessionToken smst where smst.mcId=:mcId")
    @Transactional
    @Modifying
    void deleteByMCId(@Param(value = "mcId") Integer mcId);

}
