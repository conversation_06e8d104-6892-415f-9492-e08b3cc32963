package com.birdeye.messenger.dao.repository;

import com.birdeye.messenger.dao.entity.LiveChatWidgetConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface LiveChatMessageConfigRepository extends JpaRepository<LiveChatWidgetConfig, Integer> {


    LiveChatWidgetConfig findByWidgetId(Integer id);

    void deleteByWidgetId(Integer widgetId);

}
