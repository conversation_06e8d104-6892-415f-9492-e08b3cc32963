package com.birdeye.messenger.dao.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.birdeye.messenger.dao.entity.BusinessReceptionistConfiguration;

/**
 * <AUTHOR>
 *
 */
public interface BusinessReceptionistConfigurationRepository extends JpaRepository<BusinessReceptionistConfiguration,Integer>{

	BusinessReceptionistConfiguration findByBusinessId(Long enterpriseId);
	
	@Query("SELECT brc from BusinessReceptionistConfiguration brc where brc.id = :id")
	BusinessReceptionistConfiguration getDefaultReceptionistConfig(@Param("id") Integer id);

	//Inbox business missed call auto reply
	@Query("SELECT brc from BusinessReceptionistConfiguration brc where brc.businessId is null and type = 'missed_call_reply_inbox'")
	BusinessReceptionistConfiguration getMissedCallConfigInbox();
	
	//Non-Inbox business missed call auto reply
	@Query("SELECT brc from BusinessReceptionistConfiguration brc where brc.businessId is null and type = 'missed_call_reply_non_inbox'")
	BusinessReceptionistConfiguration getMissedCallConfigNonInbox();

}
