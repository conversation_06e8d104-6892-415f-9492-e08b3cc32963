package com.birdeye.messenger.dao.repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.birdeye.messenger.dao.entity.LiveChatSessionToken;

public interface LiveChatSessionTokenRepository extends JpaRepository<LiveChatSessionToken, Integer> {

	@Query("select lcs from LiveChatSessionToken lcs where lcs.mcid = :mcid and lcs.businessId = :businessId and lcs.status <> 'TERMINATED'")
	Optional<LiveChatSessionToken> findActiveSessionByMcidAndBusinessId(@Param("mcid") Integer mcid, @Param("businessId") Integer businessId);
	
	Optional<LiveChatSessionToken> findBySessionId(String sessionId);
	
	@Query("select lcs from LiveChatSessionToken lcs where lcs.sessionId = :sessionId and lcs.status <> 'TERMINATED'")
	Optional<LiveChatSessionToken> findActiveSessionBySessionId(@Param("sessionId") String sessionId);
	
	@Query("select lcs from LiveChatSessionToken lcs where lcs.mcid = :mcid and lcs.businessId = :businessId")
	Optional<LiveChatSessionToken> findSessionByMcidAndBusinessId(@Param("mcid") Integer mcid, @Param("businessId") Integer businessId);
	
	@Query("select lcs from LiveChatSessionToken lcs where lcs.updated < :updatedDateBefore and lcs.status <> 'TERMINATED'")
	List<LiveChatSessionToken> findStaleActiveSessions(@Param("updatedDateBefore") Date updatedDateBefore);

	@Modifying
	@Query("update LiveChatSessionToken lcs set lcs.status = 'TERMINATED',lcs.autoReplySent = NULL  where lcs.sessionId in :sessionIds")
	void updateSessionsStatusToTerminated(@Param("sessionIds")List<String> sessionIds);
}
