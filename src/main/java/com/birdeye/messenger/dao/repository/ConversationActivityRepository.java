package com.birdeye.messenger.dao.repository;

import com.birdeye.messenger.dao.entity.ConversationActivity;
import com.birdeye.messenger.enums.StatusEnum;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

import java.util.List;

public interface ConversationActivityRepository extends JpaRepository<ConversationActivity, Integer> {

    @Modifying
    @Transactional
    @Query("UPDATE ConversationActivity  ca set ca.thankYouNoteStatus = :status, ca.updatedDate = :updatedDate " +
            "where ca.referredLeadMcId = :lead and ca.referrerMcId = :referrer and ca.activityType in :activityTypes")
    void updateReferralActivity(@Param("status") StatusEnum status, @Param("referrer") Integer referrer,
                                                      @Param("lead") Integer lead, @Param("activityTypes") List<String> activityTypes,
                                                      @Param("updatedDate")Date updatedDate);

    @Query("SELECT ca from ConversationActivity ca where ca.referrerMcId = :referrer AND ca.referredLeadMcId = :lead AND ca.activityType in :activityTypes")
    List<ConversationActivity> getReferralActivity(@Param("referrer") Integer referrer, @Param("lead") Integer lead, @Param("activityTypes") List<String> activityTypes);

    void deleteByIdIn(List<Integer> messageIds);

}
