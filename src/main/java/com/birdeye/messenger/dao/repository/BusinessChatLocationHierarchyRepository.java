package com.birdeye.messenger.dao.repository;

import com.birdeye.messenger.dao.entity.BusinessChatEnabledLocation;
import com.birdeye.messenger.dao.entity.BusinessChatLocationHierarchy;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface BusinessChatLocationHierarchyRepository extends JpaRepository<BusinessChatLocationHierarchy,Integer> {

    List<BusinessChatLocationHierarchy> findBybusinessChatWidgetId(Integer id);

    @Modifying( clearAutomatically = true)
    @Query("Delete from  BusinessChatLocationHierarchy bcel where bcel.businessChatWidgetId = :widgetConfigId  ")
    void deleteBusinessChatLocationHierarchyByWidgetConfigId(@Param("widgetConfigId") Integer widgetConfigId);



}
