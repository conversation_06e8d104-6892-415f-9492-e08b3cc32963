package com.birdeye.messenger.dao.repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import jakarta.transaction.Transactional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.GetMessengerContactDTO;
import com.birdeye.messenger.dto.MessengerContactResponse;

@Repository
public interface MessengerContactRepository extends JpaRepository<MessengerContact, Integer> {

    @Override
    Optional<MessengerContact> findById(Integer integer);

    @Query(value = "SELECT * FROM messenger_contact mc where customer_id = :customerId limit 1",nativeQuery = true)
    Optional<MessengerContact> findByCustomerId(@Param("customerId") Integer customerId);
    
    @Query("SELECT mc FROM MessengerContact mc WHERE mc.facebookId = :facebookId and mc.businessId =:businessId")
	MessengerContact findByFacebookId(@Param("facebookId") String facebookId,@Param("businessId") Integer businessId);
    
    @Query("SELECT mc FROM MessengerContact mc WHERE mc.instagramConversationId = :instagramConversationId and mc.businessId =:businessId")
	MessengerContact findByInstagramConversationId(@Param("instagramConversationId") String instagramId,@Param("businessId") Integer businessId);
	
	@Query("SELECT mc.facebookId FROM MessengerContact mc WHERE mc.id = :id ")
	String findEmailById(@Param("id") Integer id);

	@Query("SELECT mc from MessengerContact mc where mc.updatedAt >= :updatedAt")
	List<MessengerContact> findAllByUpdatedAtAfter(@Param("updatedAt") Date updatedAt);

	@Query("SELECT mc from MessengerContact mc where mc.customerId in :customerIds order by mc.updatedAt desc")
	List<MessengerContact> findContactsByCustomerIds(@Param("customerIds") List<Integer> customerIds);
	
	@Query("SELECT mc from MessengerContact mc where mc.id in :Ids")
	List<MessengerContact> findContactsByIds(@Param("Ids") List<Integer> Ids);
	
	@Query(value = "Select new com.birdeye.messenger.dto.GetMessengerContactDTO (mc.id, mc.businessId, mc.tag, mc.viewedBy,"
			+ " mc.isRead, mc.assignmentType, mc.currentAssignee, mc.currentAssigneeName, mc.currentAssigneeEmailId) "
			+ "from MessengerContact mc where mc.id in :ids")
	List<GetMessengerContactDTO> findByIdIn(@Param("ids") List<Integer> mcId);
	
	@Transactional
	@Modifying
	@Query("update MessengerContact mc set mc.viewedBy = CAST(CONCAT_WS(',', mc.viewedBy, :userId) AS java.lang.String), mc.tag = :tag, mc.updatedAt = :date where mc.id in :ids")
	void updateViewedByRead(@Param("userId") Integer userId, @Param("ids") List<Integer> id, @Param("date") Date updatedAt, @Param("tag") int tag);

	@Transactional
	@Modifying
	@Query("update MessengerContact mc set mc.tag = :tag, mc.updatedAt = :date where mc.id in :ids")
	void updateConversationTag(@Param("tag") Integer tag, @Param("ids") List<Integer> conversationIds, @Param("date") Date updatedAt);
	
	@Transactional
	@Modifying
	@Query("update MessengerContact mc set mc.viewedBy = CAST(CONCAT_WS(',', mc.viewedBy, :userId) AS java.lang.String), mc.updatedAt = :date where mc.id in :ids")
	void updateViewedByUnread(@Param("userId") Integer userId, @Param("ids") List<Integer> id, @Param("date") Date updatedAt);

	@Query(value = "SELECT mc.id, mc.business_id, b.enterprise_id FROM messenger_contact mc inner join business b on b.id = mc.business_id where mc.id > ?1 order by mc.id limit ?2", nativeQuery = true)
	List<Object[]> fetchPaginatedCoversationsWithEnterprise(Integer conversationId, Integer limit);

	@Query("Select mc from MessengerContact mc where mc.businessId = :businessId")
	List<MessengerContact> findMessengerContactByBusinessId(@Param("businessId") Integer businessId);

	@Transactional
	@Modifying
	@Query("update MessengerContact mc set mc.viewedBy = :userId, mc.isRead = false, mc.updatedAt = :date where mc.businessId = :businessId")
	void updateViewedByUnreadForBusiness(@Param("businessId") Integer businessId, @Param("userId") String userId,
			@Param("date") Date updatedAt);

	@Query(value = "Select new com.birdeye.messenger.dto.GetMessengerContactDTO (mc.id, mc.viewedBy) "
			+ "from MessengerContact mc where mc.id in :ids")
	List<GetMessengerContactDTO> findViewedByByIdIn(@Param("ids") List<Integer> conversationIds);

	@Query(value = "SELECT mc.id, mc.businessId FROM MessengerContact mc where mc.id in :ids")
	List<Object[]> findBusinessIdsByMcIds(@Param("ids") Set<Integer> conversationIds);

	@Query("SELECT mc.id FROM MessengerContact mc where mc.contactState is null AND mc.lastMsgOn > :start")
	List<Integer> findByNullContactStateAndLastActivityDate(@Param("start") Date start);

	@Query(value = "SELECT mc.id, mc.facebookId FROM MessengerContact mc where mc.id in :ids")
	List<Object[]> findFacebookIdsByMcIds(@Param("ids") List<Integer> mcIds);

	List<MessengerContact> findByCustomerIdAndBusinessId(Integer customerId, Integer businessId);

	MessengerContact findByReviewerIdAndBusinessId(Integer reviewerId, Integer businessId);

	@Query("SELECT mc FROM MessengerContact mc where mc.id in :ids")
	List<MessengerContact> findByIdsIn(@Param("ids") List<Integer> ids);

	@Query("SELECT mc.id FROM MessengerContact mc where mc.rtmPauseTagging = true and (mc.createdAt between :startDate and :endDate) AND mc.tag <> 5 order by mc.createdAt asc")
	List<Integer> findContactsForMigrationCreatedBetween(@Param("startDate") Date startDate,
			@Param("endDate") Date endDate);

	@Query("SELECT mc.id FROM MessengerContact mc where mc.rtmPauseTagging = true and (mc.createdAt between :startDate and :endDate) AND mc.tag <> 5 and mc.businessId in :businessIds order by mc.createdAt asc")
	List<Integer> findContactsForMigrationCreatedBetweenForBusiness(@Param("businessIds") List<Integer> businessIds,
			@Param("startDate") Date startDate, @Param("endDate") Date endDate);

	@Query("SELECT mc.id FROM MessengerContact mc where (mc.lastMsgOn between :startDate and :endDate) AND mc.tag <> 5 AND mc.businessId in :businessIds order by mc.lastMsgOn asc")
	List<Integer> findMcForResponseTimeMigrationBasedOnLastActivityDate(@Param("businessIds") List<Integer> businessIds,
			@Param("startDate") Date startDate, @Param("endDate") Date endDate);

	@Query("SELECT mc.id FROM MessengerContact mc where (mc.lastMsgOn between :startDate and :endDate) AND mc.tag <> 5 order by mc.lastMsgOn asc")
	List<Integer> findMcForResponseTimeMigrationBasedOnLastActivityDate(@Param("startDate") Date startDate,
			@Param("endDate") Date endDate);

	@Query(value = "select mc.customer_id,mc.business_id,count(mc.customer_id) from messenger_contact mc where mc.customer_id is not null and mc.customer_id=:customerId group by customer_id having count(mc.customer_id)>1 order by mc.business_id", nativeQuery = true)
	List<Object[]> findDuplicateContactsByCustomer(@Param("customerId") Integer customerId);

	@Query(value = "select mc.customer_id,mc.business_id,count(mc.customer_id) from messenger_contact mc where mc.customer_id is not null and mc.business_id=:businessId group by customer_id having count(mc.customer_id)>1 order by mc.business_id", nativeQuery = true)
	List<Object[]> findDuplicateContactsByLocation(@Param("businessId") Integer businessId);

	@Query(value = "select mc.customer_id,mc.business_id,count(mc.customer_id) from messenger_contact mc where mc.customer_id is not null group by customer_id having count(mc.customer_id)>1 order by mc.business_id", countQuery = "select count(mc.customer_id) from messenger_contact mc where mc.customer_id is not null group by customer_id", nativeQuery = true)
	Page<Object[]> findDuplicateContacts(Pageable pageable);

	@Query(value = "select DISTINCT(mc.customer_id) from messenger_contact mc where mc.customer_id in :customerIds and mc.business_id <> :businessId", nativeQuery = true)
	List<Integer> findDuplicateContactsByCustomerIds(@Param("customerIds") List<Integer> customerIds,
			@Param("businessId") Integer businessId);

	@Modifying
	@Query("DELETE FROM MessengerContact mc WHERE mc.id in :contactIds")
	void deleteAllByIds(@Param("contactIds") List<Integer> contactIds);

	@Query("SELECT mc FROM MessengerContact mc where mc.businessId = :businessId and mc.customerId = :customerId and mc.googleConversationId = :googleConversationId")
	Optional<MessengerContact> findByGoogleConversation(@Param("googleConversationId") String googleConversationId, @Param("businessId") Integer businessId, @Param("customerId") Integer customerId);
	
	@Query("SELECT mc FROM MessengerContact mc where mc.businessId = :businessId and mc.customerId = :customerId and mc.appleConversationId = :appleConversationId")
	Optional<MessengerContact> findByAppleConversation(@Param("appleConversationId") String appleConversationId, @Param("businessId") Integer businessId, @Param("customerId") Integer customerId);
	
	@Query("SELECT mc FROM MessengerContact mc where mc.businessId = :businessId and mc.googleConversationId = :googleConversationId")
	Optional<MessengerContact> findByGoogleConversationAndSubaccount(@Param("googleConversationId") String googleConversationId, @Param("businessId") Integer businessId);
	
	@Query("SELECT mc FROM MessengerContact mc where mc.businessId = :businessId and mc.appleConversationId = :appleConversationId")
	Optional<MessengerContact> findByAppleConversationAndSubaccount(@Param("appleConversationId") String appleConversationId, @Param("businessId") Integer businessId);
	
	@Query(value = "select mc.id,mc.business_id,mc.facebook_id,mc.google_conversation_id from messenger_contact mc where (mc.google_conversation_id <> '' or mc.facebook_id is not null) and mc.business_id=:businessId order by mc.business_id",nativeQuery = true)
	List<Object[]> findContactsByLocation(@Param("businessId") Integer businessId);

	@Query(value = "select mc.id,mc.business_id,mc.facebook_id,mc.google_conversation_id from messenger_contact mc where (mc.google_conversation_id <> '' or mc.facebook_id is not null) and mc.id=:mcId order by mc.business_id", nativeQuery = true)
	List<Object[]> findContactById(@Param("mcId") Integer mcId);

	@Query(value = "select mc.id,mc.business_id,mc.facebook_id,mc.google_conversation_id from messenger_contact mc where (mc.google_conversation_id <> '' or mc.facebook_id is not null) order by mc.business_id", countQuery = "select count(mc.id) from messenger_contact mc where (mc.google_conversation_id <> '' or mc.facebook_id is not null) order by mc.business_id", nativeQuery = true)
	Page<Object[]> findContacts(Pageable pageable);

	@Query("SELECT count(mc.id) from MessengerContact mc where mc.id in :Ids and mc.customerId is not null")
	Long findCountByIds(@Param("Ids") List<Integer> Ids);

	@Query("select mc from MessengerContact mc where mc.googleConversationId=:googleConvId")
	List<MessengerContact> findByGoogleConversationId(@Param("googleConvId") String googleConvId);

	@Query("select new com.birdeye.messenger.dto.MessengerContactResponse(mc.id,mc.businessId,mc.customerId,mc.facebookId) from MessengerContact mc where mc.customerId = :customerId and mc.facebookId is not null")
	List<MessengerContactResponse> findFacebookIdsByCustomerIds(@Param("customerId") Integer customerId);

	@Query("select new com.birdeye.messenger.dto.MessengerContactResponse(mc.id,mc.businessId,mc.customerId,mc.facebookId) from MessengerContact mc where mc.customerId = :customerId")
	List<MessengerContactResponse> findMessengerContactResponseByCustomerId(@Param("customerId") Integer customerId);

    @Query("select mc from MessengerContact mc where mc.id=:id and mc.customerId=:customerId")
    Optional<MessengerContact> findByIdAndCustomerId(@Param("id") Integer id, @Param("customerId") Integer customerId);

	@Modifying
	@Transactional
	@Query(value = "INSERT INTO messenger_contact_archive SELECT * FROM messenger_contact WHERE id IN :contactIds", nativeQuery = true)
	void moveContactsToArchive(@Param("contactIds") List<Integer> contactIds);

	@Modifying
	@Query("DELETE FROM MessengerContact m WHERE m.id IN :contactIds")
	void deleteByIdIn(@Param("contactIds") List<Integer> contactIds);

	@Query("SELECT mc FROM MessengerContact mc WHERE mc.twitterConversationId = :twitterConversationId and mc.businessId =:businessId")
	MessengerContact findByTwitterConversationId(@Param("twitterConversationId") String twitterId,@Param("businessId") Integer businessId);

	@Query("SELECT mc FROM MessengerContact mc WHERE mc.whatsappConversationId = :customerWAId and mc.businessId =:businessId")
	MessengerContact findByWhatsappConversationId(String customerWAId, Integer businessId);
}
