package com.birdeye.messenger.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum RuleActionType {
    CUSTOM_MESSAGE("Custom Message"),
    AI_PROMPT("AI Prompt"), 
    KNOWLEDGE_BASE("Knowledge Base");

    private final String value;

    RuleActionType(String value) {
        this.value = value;
    }

    @JsonValue
    public String getValue() {
        return value;
    }

    @JsonCreator
    public static RuleActionType fromValue(String value) {
        if (value == null) {
            throw new IllegalArgumentException("Action type value cannot be null");
        }
        
        for (RuleActionType type : values()) {
            if (type.value.equalsIgnoreCase(value) || type.name().equalsIgnoreCase(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown action type: " + value + ". Valid values are: CustomMessageAction, AIPromptAction, KnowledgeBaseAction");
    }
}
