package com.birdeye.messenger.controller;



import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.service.WebChatStatusService;
import com.birdeye.messenger.sro.*;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.birdeye.messenger.service.WebchatService;
import com.birdeye.messenger.sro.BusinessDeactivationRequest;
import com.birdeye.messenger.sro.BusinessWebChatConfigurationRequest;
import com.birdeye.messenger.sro.CreateDefaultConfigRequest;
import com.birdeye.messenger.sro.GetLocationsResponse;
import com.birdeye.messenger.sro.SaveWebchatResponse;
import com.birdeye.messenger.sro.WebChatConfigurationGenericRequest;
import com.birdeye.messenger.sro.WebChatWidgetDefaultConfiguration;
import com.birdeye.messenger.sro.WebchatConfigFilter;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import jakarta.validation.Valid;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/messenger")
public class WebchatController {


	@Autowired
	private WebchatService webchatService;

	@Autowired
	private WebChatStatusService webChatStatusService;

	/*


    @Autowired
    private AssignmentService assignmentService;


    @Autowired
    LiveChatMessageConfigRepository liveChatMessageConfigRepository;

     *  API is triggered on sending a message from webchat widget
     */
	@RequestMapping(value = "/webchat/send", method = RequestMethod.POST)
	public ResponseEntity<Boolean> receiveMessageFromWebchatWidget(@RequestBody WebchatMessageDTO request) {
		log.debug("Webchat sms send request for widget id {} ", request.getWidgetConfigId());
		Boolean isReceivedDuringBusinessHours = webchatService.sendWebchatSMS(request);
		return new ResponseEntity<>(isReceivedDuringBusinessHours,HttpStatus.OK);
	}

	/**
	 * Widget List for Enterprise
	 * @param page
	 * @param size
	 * @param sortBy
	 * @param sortOrder
	 * @param searchText
	 * @param userLoginMessage
	 * @return
	 */
	@RequestMapping(value = "/webchat/widgets", method = RequestMethod.POST)
	public ResponseEntity<BusinessWebchatWidgetList> webchatWidgetList(@RequestParam(value = "page", required = false, defaultValue = "0") Integer page,
																	   @RequestParam(value = "size", required = false, defaultValue = "25") Integer size,
																	   @RequestParam(value = "sortBy", required = false, defaultValue = "2") Integer sortBy,
																	   @RequestParam(value = "sortOrder", required = false, defaultValue = "-1") Integer sortOrder,
																	   @RequestParam(value = "searchText", required = false) String searchText,
																	   @RequestBody UserLoginMessage userLoginMessage) {

        BusinessWebchatWidgetList response = webchatService.getWebchatWidgetList(page, size, sortBy, sortOrder, searchText, userLoginMessage);
        return new ResponseEntity<>(response, HttpStatus.OK);
    }


    /**
     * Get list of locations where widget is present
     *
     * @param enterpriseId
     * @param businessIds
     * @return
     */
    @GetMapping(value = "/get-locations/{enterpriseId}")
    public ResponseEntity<List<GetLocationsResponse>> getLocationsByEnterpriseId(@PathVariable("enterpriseId") Integer enterpriseId) {
        List<GetLocationsResponse> response = null;
        log.debug("Get locations by enterprise Id: {} ", enterpriseId);
        response = webchatService.getLocationsByEnterpriseId(enterpriseId);
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    /**
     * Get web chat widget default config - create new widget flow
     *
     * @param locationId
     * @return
     */
    @GetMapping(value = "/get-default-config/{locationId}")
    public ResponseEntity<WebChatWidgetDefaultConfiguration> getDefaultConfig(@PathVariable("locationId") Integer locationId) {
        WebChatWidgetDefaultConfiguration webChatWidgetDefaultConfiguration = webchatService.getDefaultConfig(locationId);
        return new ResponseEntity<>(webChatWidgetDefaultConfiguration, HttpStatus.OK);
    }

    /**
     * Save or Update web chat config - create, edit, clone
     *
     * @param businessWebChatConfigurationRequest
     * @return
     */
    @PostMapping(value = "/save-webchat-config")
    public ResponseEntity<SaveWebchatResponse> saveWidgetConfig(@RequestBody @Valid BusinessWebChatConfigurationRequest businessWebChatConfigurationRequest) {
        SaveWebchatResponse response = webchatService.saveWidgetConfig(businessWebChatConfigurationRequest);
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    /**
     * Web chat config for SMB
     *
     * @param configFilter
     * @return
     */
    @PostMapping(value = "/webchat/config")
    public ResponseEntity<WebChatWidgetDefaultConfiguration> getBusinessWebChatConfig(@RequestBody WebchatConfigFilter configFilter) {
        WebChatWidgetDefaultConfiguration config = webchatService.getBusinessWebChatConfig(configFilter);
        return new ResponseEntity<>(config, HttpStatus.OK);
    }


	/**
	 * Widget operations - enable, disable or delete
	 * @param request
	 * @return
	 */
	@PutMapping(value = "/webchat-operation")
	public  ResponseEntity<Boolean> webchatConfigOperation(@RequestBody WebChatConfigurationGenericRequest request){
		boolean status = webchatService.webChatConfigOperation(request);
		return new ResponseEntity<>(status,HttpStatus.OK);
	}

	/**
	 * Web chat config by widgetId : If widget is enterprise Level, added businessLocations details
	 * @param businessId
	 * @param widgetId
	 * @return
	 */
	@GetMapping(value = "/webchat/config/{businessId}/{widgetId}")
	public  ResponseEntity<WebChatWidgetDefaultConfiguration> getWebChatConfigForWidgetId(@PathVariable("businessId") Long businessId, @PathVariable("widgetId") Integer widgetId){
		WebChatWidgetDefaultConfiguration config = webchatService.getWebChatConfigForWidgetId(businessId, widgetId,null);
		return new ResponseEntity<>(config,HttpStatus.OK);
	}


	@PutMapping(value = "/webchat/disable")
	public  ResponseEntity<Void> webchatConfigAction(@RequestBody BusinessDeactivationRequest request){
		webchatService.webChatConfigAction(request);
		return new ResponseEntity<>(HttpStatus.OK);
    }


	@PostMapping(value = "/webchat/installation/status")
	public  ResponseEntity<Void> updateInstallationStatus(){
        webchatService.getAndUpdateWidgetInstallationStatus();
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping(value = "/webchat/default-config/create")
	public  ResponseEntity<Boolean> createDefaultConfig(@RequestBody CreateDefaultConfigRequest request){
		boolean status = webchatService.createDefaultConfig(request);
		return new ResponseEntity<>(status,HttpStatus.OK);
	}

	@PostMapping(value = "/webchat/event")
	public ResponseEntity<Void> consumeEvent(@RequestBody WebChatEventRequestDTO webChatEventRequestDTO) {
		log.debug("Webchat event request for widget id :{}", webChatEventRequestDTO.getWidgetId());
		webChatStatusService.consumeEvent(webChatEventRequestDTO);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	@GetMapping(value = "/webchat/get-config/{externalId}")
	public  ResponseEntity<WebChatWidgetDefaultConfiguration> getWebsiteWebchatConfig(@PathVariable("externalId") Long externalId,@RequestParam(value = "isMicrosite",required = false) boolean isMicrosite){
		WebChatWidgetDefaultConfiguration webChatWidgetDefaultConfiguration =  webchatService.getWebsiteWebchatConfig(externalId,isMicrosite);
		return new ResponseEntity<>(webChatWidgetDefaultConfiguration,HttpStatus.OK);
	}

    @GetMapping("refresh-widget/{widgetId}")
    public ResponseEntity<RefreshInstallationStatusResponse> refreshWidgetStatus(@PathVariable("widgetId") Integer widgetId) {
        log.debug("Refresh status for widget id :{}", widgetId);
        RefreshInstallationStatusResponse refreshInstallationStatusResponse = webchatService.refreshWidgetStatus(widgetId);
        return new ResponseEntity<>(refreshInstallationStatusResponse, HttpStatus.OK);
    }

	@PostMapping(value = "/save-webchat")
	public ResponseEntity<BusinessWebchatWidgetDetails> saveMultiLocationWidget(@RequestBody @Valid MultiLocationWidgetRequest multiLocationWidgetRequest) {
		BusinessWebchatWidgetDetails response = webchatService.saveWidgetConfig(multiLocationWidgetRequest);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}

	@PutMapping(value = "/update/email-mandatory")
	public  ResponseEntity<Void> updateWidgetEmailMandatory(@RequestBody UpdateEmailMandatoryRequest emailMandatoryRequest,@RequestParam(value = "emailMandatory",required = true) boolean emailMandatory){
		log.debug("update email mandatory request, widget ids :{}", emailMandatoryRequest.getWidgetIds());
		webchatService.updateWidgetEmailMandatory(emailMandatoryRequest,emailMandatory);
		return new ResponseEntity<>(HttpStatus.OK);
    }

	@PostMapping(value = "/create/webchat-custom-location-name")
	public ResponseEntity<Void> createWebchatCustomLocationName(@RequestBody WebchatCustomLocationNameRequest request) {
		webchatService.createWebchatCustomLocationName(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping(value = "webchat/update/add-custom-field")
	public ResponseEntity<Void> addWebchatCustomField(@RequestBody WidgetCustomFieldRequest request, @RequestHeader("account-id") Integer accountId) {
		log.debug("Request received : addWebchatCustomField for account id - {}", accountId);
		webchatService.addWebchatCustomFields(request);
		return new ResponseEntity(HttpStatus.OK);
	}

	@GetMapping(value = "webchat/is-custom-field-exists")
	public ResponseEntity<Boolean> isCustomFieldExist(@RequestParam("fieldId") Integer fieldId) {
		log.debug("Request received : isCustomFieldExist for field id- {}", fieldId);
		Boolean isAdded = webchatService.isCustomFieldExist(fieldId);
		return new ResponseEntity( isAdded, HttpStatus.OK);
	}

	/**
	 * @param externalId
	 * @return WebChatConfigForExternalIdResponse
	 * Used By Platform to Replace the direct DB call to BusinessChatWidgetConfig Table
	 * Provides  WebChatConfigForExternalIdResponse for Given ExternalId
	 */

	@RequestMapping(value="/webchat-config-by-external-id/{externalId}" , method = RequestMethod.GET)
	public ResponseEntity<WebChatConfigForExternalIdResponse> getBusinessChatWidgetConfigByExternalId(@PathVariable("externalId") Long externalId){
		log.debug("Webchat Widget Config for ExternalId : {}",externalId);
		WebChatConfigForExternalIdResponse response = new WebChatConfigForExternalIdResponse();
		response = webchatService.getBusinessChatWidgetConfigByExternalId(externalId);
		log.debug("Webchat Widget Config Response for ExternalId : {} is :{}",externalId,response);
		return new ResponseEntity<>(response,HttpStatus.OK);
	}

	@PostMapping(value = "/webchat-widget-teams-ordering")
	public ResponseEntity<Void> webChatWidgetTeamsOrdering(@RequestBody WebchatWidgetTeamsOrderingRequest  request) {
		log.debug("Request received to update widget team ordering for widget id :{}", request.getWidgetId());
		webchatService.webChatWidgetTeamsOrdering(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping(value = "/webchat/get-config")
	public  ResponseEntity<BusinessWebChatWidgetConfiguration> getWebsiteWebchatConfigNew(@RequestBody GetWebsiteWidgetConfigRequestDto request) throws Exception{
		BusinessWebChatWidgetConfiguration webChatWidgetDefaultConfiguration =  webchatService.getWebsiteWebchatConfigNew(request.getBusinessId(),request.getApiKey(),request.isMicroSite(),request.getActivationStatus(),request.getVersion());
		return new ResponseEntity<>(webChatWidgetDefaultConfiguration,HttpStatus.OK);
	}

	@PostMapping(value = "webchat/business-hours/{bNum}")
	public ResponseEntity<BusinessHoursInsideOutsideStatusDto> isReceivedDuringBusinessHours(@PathVariable("bNum") Long businessNumber) {
		log.debug("Request received : to check inside/outside business hours {}", businessNumber);
		Boolean isReceivedDuringBusinessHours = webchatService.isReceivedDuringBusinessHours(businessNumber);
		BusinessHoursInsideOutsideStatusDto businessHoursInsideOutsideStatusDto = new BusinessHoursInsideOutsideStatusDto(isReceivedDuringBusinessHours);
		return new ResponseEntity( businessHoursInsideOutsideStatusDto, HttpStatus.OK);
	}
	
	/**
	 * Save to business_webchat_event, throttled via topic : webchat_event
	 * @param webChatEventRequestDTO
	 * @return
	 */
	@PostMapping(value = "/webchat/event/save")
	public ResponseEntity<Void> saveWebchatEvent(@RequestBody WebChatEventRequestDTO webChatEventRequestDTO) {
		webChatStatusService.saveWebchatEvent(webChatEventRequestDTO);
		return new ResponseEntity<>(HttpStatus.OK);
	}
}
