package com.birdeye.messenger.sro;

import com.birdeye.messenger.dto.LocationHierarchy;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class MultiLocationWidgetRequest implements Serializable {

    @NotNull
    Integer accountId;
    @NotNull
    String widgetName;
    @NotNull
    List<Long> locationIds;
    LocationHierarchy locationHierarchy;
    Integer userId;
    Integer cloneId;


}
