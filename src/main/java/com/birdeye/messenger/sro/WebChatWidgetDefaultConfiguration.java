package com.birdeye.messenger.sro;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.birdeye.messenger.dto.CustomChatBotHoursDto;
import com.birdeye.messenger.dto.LocationHierarchy;
import com.birdeye.messenger.util.SecureString;
import com.birdeye.messenger.util.SecureStringUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WebChatWidgetDefaultConfiguration implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long businessId;

    private String chatTheme;

    private String chatIcon;

    private String chatIconValue;

    private String bannerColor;

    private String bannerTextColor;

    private String btnColor;

    private String btnTxtColor;

    private Integer enableReplyInBusinessHr;

    private Integer enableReplyPostBusinessHr;

    private String autoReplyTxt;

    private String replyTextPostBusinessHr;

    private String headerHeadline;

    private String headerDescription;
    
    private String thankyouMsgHeadline;

	private String thankyouMsgDescription;

    private String webChatOnlineClosingMessageHeader;

    private String webChatOnlineClosingMessageBody;

    private String apiKey;

    private String businessDomain;

    private String onlineClosingMessageBody;

    private String onlineWelcomeMessageHeader;

    private boolean smb;

    // null : disabled, 0 : instant, Other applicable values (5/10/15/20)
    private Integer popupInterval;

    // Permissible values : 0/1 : 0 to disable, 1 to enable
    private Integer microsite;

    private List<BusinessWebChatWidgetUserProfile> userProfile;

    private List<BusinessLocationInfo> businessLocations;

    private String chatBubble;

    private Integer enableChatBubble;

    private Integer enableChatBubbleSound;

    private String chatIconColor;

    private String chatIconForeColor;

    //private Integer chatConfigId;

    private List<String> supportedCountryCodes = new ArrayList<>();

    private String businessName;

    private String widgetName;

    private Integer enabled;

    private Integer widgetConfigId;

    @SecureString
    private String businessSMSPhoneNumber;

    List<Team> teams;

    // Live Chat is enabled for a business or not.
    private Boolean isLiveChatEnabled;

    // Chatbot auto-reply is enabled or not.
    private Boolean isChatbotEnabled;

    // Business'es Google Analytics settings
    private Boolean enableGoogleAnalytics;
    private String googleAnalyticsVersion;
    private String googleTrackingId;

    private String webChatOfflineClosingMessageHeader;
    private String webChatOfflineClosingMessageBody;


    private String liveChatOfflineClosingMessageHeader;
    private String liveChatOfflineClosingMessageBody;

    private String liveChatOnlineClosingMessageHeader;
    private String liveChatOnlineClosingMessageBody;

    private String liveChatOfflineWelcomeMessage;
    private String liveChatOnlineWelcomeMessage;
    private Integer installed;
    private List<String> websites;
    private Date statusUpdateOn;
    
    private String liveChatOnlineTextMessage;
    private String liveChatOfflineTextMessage;
    private LocationHierarchy locationHierarchy;
    private Long externalId;
    private Integer allLocationDisabled;
    private Boolean emailMandatory;
    private List<CustomFieldWidgetDto> customFields;
    private String disclaimer;
    private Boolean autoDetectLocationEnabled;
    private Integer disclaimerSelectionStatus;

    private List<Integer> robinInsideBusinessHours;
    private List<Integer> robinOutsideBusinessHours;
    private Integer enablePrechatForm;
    private Integer prechatFormInsideBusinessHours;
    private Integer prechatFormOutsideBusinessHours;

    private List<CustomChatBotHoursDto> chatbotHours;

    private Integer timerDisplay;
    private Integer timerMinutes;
    private Integer timerSeconds;
    
    private String mobileView;
    
    private List<String> widgetAgent;

    @Override
    public String toString() {
        return SecureStringUtil.buildSecureToString(this);
    }

}
