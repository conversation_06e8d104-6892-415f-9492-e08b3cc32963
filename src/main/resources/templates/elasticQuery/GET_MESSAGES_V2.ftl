<#if (messageFilter)??>
    {
        "from": ${messageFilter.startIndex},
        "size": ${messageFilter.count?string.computer},
        "query": {
            "bool": {
                "must": [
                    {
                        "term": {
                            "c_id": { "value": ${messageFilter.conversationId?c} }
                        }
                    }
                    <#if (messageFilter.params)??>
                    <#if (messageFilter.params.msg_type)??>,
                    {
                        "terms": {
                            "msg_type": ${messageFilter.params.msg_type}
                        }
                    }
                    </#if>
                    <#if (messageFilter.params.source)??>,                {
                    "terms": {
                            "source": ${messageFilter.params.source}
                        }
                    }
                    </#if>
                    </#if>
                    <#if (messageFilter.startDate)??>,
                    {
                        "range": {
                            "cr_date": { "gte": "${messageFilter.startDate}" }
                            }
                        }
                    </#if>
                ]
                <#if (messageFilter.params)??>,
                "must_not" : [
                    {
          				"exists": {
            				"field": "thisIsAHack"
          				}
        			}
                	<#if (messageFilter.params.activityType)??>,
                    {
                        "terms" : {
                            "activityType" : ${messageFilter.params.activityType}
                        }
                    }
                    </#if>
                    <#if (messageFilter.params.reviews?? && messageFilter.params.reviews==false)>,
                    {
                        "terms" : {
                            "messageType" : ["REVIEW"]
                        }
                    }
                    </#if>
                    <#if (messageFilter.params.surveys?? && messageFilter.params.surveys==false)>,
                    {
                        "terms" : {
                            "messageType" : ["SURVEY_RESPONSE"]
                        }
                    }
                    </#if>
                    <#if (messageFilter.params.excludeReviewIds)??>,
                    {
                        "terms" : {
                            "reviewId" : [${messageFilter.params.excludeReviewIds}]
                        }
                    }
                    </#if>
                    <#if (messageFilter.secureFaq == true)>,
                    {
                        "term" : {
                            "secureFaq" : ${messageFilter.secureFaq?c}
                        }
                    }
                    </#if>
                ]
                </#if>
            }
        },
        "sort": {
            "cr_date": {
                "order":"desc"
            }
        }
    }
</#if>
