#---------------------------- messenger db config ----------------------------------------------------------
spring.datasource.messenger.jdbcUrl=***************************************************************************************************************************************************************
spring.datasource.messenger.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.messenger.username=bfuser
spring.datasource.messenger.password=bazaar360
spring.datasource.messenger.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.messenger.connection-test-query=SELECT 1 
spring.datasource.messenger.pool-name=Messenger-Master
spring.datasource.messenger.maximumPoolSize=200
spring.datasource.messenger.minimumIdle=5
spring.datasource.messenger.maxLifetime=2000000
spring.datasource.messenger.connectionTimeout=60000
spring.datasource.messenger.idleTimeout=30000
spring.datasource.messenger.test-while-idle = true
spring.datasource.messenger.test-on-borrow = true
spring.datasource.messenger.time-between-eviction-runs-millis = 60000
spring.datasource.messenger.validation-query = SELECT 1
spring.datasource.messenger.validation-query-timeout = 3
spring.datasource.messenger.pool-prepared-statements=true
spring.datasource.messenger.max-open-prepared-statements=250
spring.datasource.messenger.max-sql-prepared-statements=2048
spring.datasource.messenger.use-server-prepared-statements=true
spring.datasource.messenger.remove-abandoned = true
spring.datasource.messenger.remove-abandoned-timeout = 120

### ---------------------------- Spring Kafka properties ------------------------ ###
# https://docs.confluent.io/current/clients/producer.html
kafka.server.default=10.21.32.246:9092
kafka.server.nexus=10.21.32.246:9092

# ----------------------------OTHER MICRO-SERVICES URL ------------------------------------------
platform.service.url=https://apiqa6.birdeye.com/resources

#Chatbot
chatbot.service.url=http://qa-paid-chatbot-api.birdeye.internal

social.messenger.url=http://qa-paid-api-social-37.birdeye.internal:8080/social/messenger
social.post.url=http://qa-paid-api-social-37.birdeye.internal:8080/social/post
social.base.url=http://qa-paid-api-social-37.birdeye.internal:8080


core.service.url=http://qa-paid-api-corebusiness-6.birdeye.internal:8080

#http://10.21.11.10:8080
nexus.url=http://qa-paid-api-nexus-1.birdeye.internal:8080
#Kontacto
#kontacto.service.url=http://qa-paid-kontacto-api-1.birdeye.internal:8080
kontacto.service.url=http://qa-paid-kontacto-api-3.birdeye.internal:8080
#NLP
nlp.service.url=http://qa-paid-api-nlp-1.birdeye.internal:8080/nlp/profane?id=1
#Campaign
campaign.service.url=http://qa-paid-campaign-api-2.birdeye.internal:8080/v1/campaign
#review
review.service.endpoint=http://qa-paid-api-review-1.birdeye.internal:8080
#Survey
survey.service.url=http://qa-paid-api-survey-1.birdeye.internal:8080
#PdfGen node
pdf.gen.service.url=https://qa-pdfgen-2.birdeye.com/v1
#Quero
quero.service.url=http://qa-paid-quero-api-1.birdeye.internal:8080/quero
#Spam detection service Url
spam.detection.service.url=http://qa-spam-detection-2.birdeye.internal:8080/api
#Conversational-AI service Url
conversational.ai.service.url=http://qa-ai-conversational-bot.birdeye.internal/api
gen.ai.service.url=http://qa-gen-ai-3.birdeye.internal/api
agentArcUrl.service.url=https://qa-birdai-agents.birdeye.com/
#Doup Service
doup.service.url=http://qa-paid-doup-api-4.birdeye.internal:8080
# ----------------------------- Redis configuration ---------------------------
spring.data.redis.host=qa-paid-redis-common.birdeye.internal
spring.data.redis.port=6379
spring.data.redis.password=foobird
spring.data.redis.database=4
spring.data.redis.timeout=3000
# Maximum number of connections that can be allocated by the pool at a given time. Use a negative value for no limit.
spring.data.redis.jedis.pool.max-active=8
# Maximum number of "idle" connections in the pool. Use a negative value to indicate an unlimited number of idle connections.
spring.data.redis.jedis.pool.max-idle=8
# Maximum amount of time a connection allocation should block before throwing an exception when the pool is exhausted. Use a negative value to block indefinitely.
spring.data.redis.jedis.pool.max-wait=100ms
spring.data.redis.jedis.pool.min-idle=0
spring.data.redis.platform.database=14
spring.data.redis.ssl.enabled=false

#------------------------------- server local port -------------
server.port=8080
good-status=OK,NOT_ACCEPTABLE,BAD_REQUEST,UNAUTHORIZED,PAYMENT_REQUIRED,FORBIDDEN,REQUEST_TIMEOUT,PAYLOAD_TOO_LARGE,URI_TOO_LONG

# ---------------------- Swagger Property -----------------------------------------------------------
enable.messenger.swagger=true
secure.dev.endpoint.password=Eg28&vS2
# ----------------------------AWS SQS URL ------------------------------------------
amazonSQS.user.typing.event.queue=https://sqs.us-west-1.amazonaws.com/************/QA_LIVE_CHAT_STATE
amazonSQS.apple.quick.reply.event.queue=https://sqs.us-west-1.amazonaws.com/************/QA_APPLE_ROBIN_FEEDBACK
amazonSQS.queue.delay.timer=5

video.host=https://qavideo.birdeye.com
# ----------------------------Email configuration ------------------------------------------
email.reply.domain=@qa.demomail.birdeye.com

#com.messenger.account.chatTranscriptEnabled=617630,617626

com.messenger.account.chatTranscriptConfig.617630=HTML
com.messenger.account.chatTranscriptConfig.624420=HTML
com.messenger.account.chatTranscriptConfig.624393=HTML
com.messenger.account.chatTranscriptConfig.617626=ADF
com.messenger.account.chatTranscriptConfig.623776=HTML
com.messenger.account.chatTranscriptConfig.461223=HTML
com.messenger.account.chatTranscriptConfig.625704=ADF
com.messenger.account.chatTranscriptConfig.617614=ADF
com.messenger.account.chatTranscriptConfig.622130=HTML
#com.messenger.account.chatTranscriptConfig.617614=HTML

# ----------------------------Other configuration ------------------------------------------
account.reject.cold.leads=123,456

#--ES Config--
aws.es.host=https://qa-es.birdeye.com
aws.es.service.name=es
aws.region=us-west-1
aws.role=arn:aws:iam::************:role/demo-qa-es-assume-role
# ---- payment base url
payment.base.url=http://qa-payments.birdeye.internal:8080/payments

#aws.redis.host.master=master.qa-redis-common.svp4hc.usw1.cache.amazonaws.com
#aws.redis.host.slave=replica.qa-redis-common.svp4hc.usw1.cache.amazonaws.com
#aws.redis.host.port=6379
#aws.redis.password=qA_Redis131bs121

#--- Actuator REST endpoints.
management.endpoints.web.exposure.include=health,metrics,loggers
management.endpoint.health.show-details=always
management.health.elasticsearch.enabled=false
spring.jackson.serialization.INDENT_OUTPUT = true

#AWS-SM
aws.secret.manager.secret.id=qa/bazaarify/Messenger
aws.secret.region=us-west-1
aws.secret.enabled=false

#ESS default businessId
ess.business.id=623048
ess.agent.id=brands/ca111918-2689-44a1-98e1-3c7994079052/agents/d0051abd-fc17-4881-9e1c-ee2d8985aa5f

#AppointmentService
appointment.service.url=http://qa-appointment-api.birdeye.internal:8080/

#secure messaging
secure.messaging.channels=1
secure.message.base.url=http://qa-secure-message-1.birdeye.internal:11000/
secure.messaging.base.firebase.bucket=secure-messaging-qa/
