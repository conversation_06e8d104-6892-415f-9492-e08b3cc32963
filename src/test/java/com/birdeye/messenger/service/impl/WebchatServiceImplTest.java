package com.birdeye.messenger.service.impl;

import static org.junit.Assert.assertNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.dao.entity.BusinessChatWidgetConfig;
import com.birdeye.messenger.dao.entity.CustomFieldsWidget;
import com.birdeye.messenger.dao.entity.LiveChatWidgetConfig;
import com.birdeye.messenger.dao.repository.LiveChatMessageConfigRepository;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.service.BusinessChatWidgetConfigService;
import com.birdeye.messenger.service.CustomFieldService;
import com.birdeye.messenger.sro.WebChatWidgetDefaultConfiguration;
import com.birdeye.messenger.util.ControllerUtil;

@ExtendWith(MockitoExtension.class)
public class WebchatServiceImplTest {

	@Mock
	BusinessService businessService;

	@Mock
	CustomFieldService customFieldService;

	@Mock
	LiveChatMessageConfigRepository liveChatMessageConfigRepository;

	@Mock
	BusinessChatWidgetConfigService businessChatWidgetConfigService;

	@InjectMocks
	WebchatServiceImpl webchatServiceImpl;
	
	private static MockedStatic<CacheManager> mockedStatic;

	@BeforeAll
	static void setUpCache() {
		mockedStatic = mockStatic(CacheManager.class);
		CacheManager mockedCacheManager = mock(CacheManager.class);
		mockedStatic.when(CacheManager::getInstance).thenReturn(mockedCacheManager);
		SystemPropertiesCache mockedCache = spy(SystemPropertiesCache.class);
		when(mockedCacheManager.getCache(SystemPropertiesCache.class)).thenReturn(mockedCache);
	}

	@AfterAll
	static void closeCache() {
		mockedStatic.close();
	}


	@Test
	public void testGetWebsiteWebchatConfig() {
		Long externalId = 145253713345512L;
		boolean isMicrosite = true;
		when(businessService.getBusinessByBusinessNumber(any())).thenReturn(mockBusinessDto());
		when(businessChatWidgetConfigService.getWebchatConfigMicrosite(any()))
				.thenReturn(mockBusinessChatWidgetConfig());
		when(customFieldService.getWebchatCustomFields(any(), any()))
				.thenReturn(List.of(mockCustomFieldsWidget(), mockCustomField2(), mockCustomField3()));
		when(businessChatWidgetConfigService.getBusinessLocationsData(any(), any())).thenReturn(new ArrayList<>());
		when(businessService.getSupportedCountryCodes(any())).thenReturn(List.of("US"));
		when(businessService.isMessengerEnabled(any())).thenReturn(0);
		when(liveChatMessageConfigRepository.findByWidgetId(any())).thenReturn(mockLiveChatWidgetConfig());
		WebChatWidgetDefaultConfiguration webChatWidgetDefaultConfiguration = webchatServiceImpl
				.getWebsiteWebchatConfig(externalId, isMicrosite);
		assertEquals(3, webChatWidgetDefaultConfiguration.getCustomFields().size());
		// 1
		assertEquals(mockCustomFieldsWidget().getFieldId(),
				webChatWidgetDefaultConfiguration.getCustomFields().get(0).getId());
		assertEquals(mockCustomFieldsWidget().getFieldName(),
				webChatWidgetDefaultConfiguration.getCustomFields().get(0).getName());
		assertEquals(mockCustomFieldsWidget().getFieldType(),
				webChatWidgetDefaultConfiguration.getCustomFields().get(0).getType());
		assertEquals(mockCustomFieldsWidget().getFieldSubType(),
				webChatWidgetDefaultConfiguration.getCustomFields().get(0).getSubType());
		assertEquals(ControllerUtil.decodeStringToList(mockCustomFieldsWidget().getSubTypeValues()),
				webChatWidgetDefaultConfiguration.getCustomFields().get(0).getSubTypeValues());
		// 2
		assertEquals(mockCustomField2().getFieldId(),
				webChatWidgetDefaultConfiguration.getCustomFields().get(1).getId());
		assertEquals(mockCustomField2().getFieldName(),
				webChatWidgetDefaultConfiguration.getCustomFields().get(1).getName());
		assertEquals(mockCustomField2().getFieldType(),
				webChatWidgetDefaultConfiguration.getCustomFields().get(1).getType());
		assertEquals(mockCustomField2().getFieldSubType(),
				webChatWidgetDefaultConfiguration.getCustomFields().get(1).getSubType());
		assertEquals(ControllerUtil.decodeStringToList(mockCustomField2().getSubTypeValues()),
				webChatWidgetDefaultConfiguration.getCustomFields().get(1).getSubTypeValues());
		// 3
		assertEquals(mockCustomField3().getFieldId(),
				webChatWidgetDefaultConfiguration.getCustomFields().get(2).getId());
		assertEquals(mockCustomField3().getFieldName(),
				webChatWidgetDefaultConfiguration.getCustomFields().get(2).getName());
		assertEquals(mockCustomField3().getFieldType(),
				webChatWidgetDefaultConfiguration.getCustomFields().get(2).getType());
		assertNull(webChatWidgetDefaultConfiguration.getCustomFields().get(2).getSubType());
		assertNull(webChatWidgetDefaultConfiguration.getCustomFields().get(2).getSubTypeValues());

	}

	private BusinessDTO mockBusinessDto() {
		BusinessDTO businessDTO = new BusinessDTO();
		businessDTO.setBusinessId(65709);
		businessDTO.setBusinessNumber(145253713345512L);
		businessDTO.setBusinessName("Swedish Motors - Marietta, PA");
		return businessDTO;
	}

	private BusinessChatWidgetConfig mockBusinessChatWidgetConfig() {
		BusinessChatWidgetConfig businessChatWidgetConfig = new BusinessChatWidgetConfig();
		businessChatWidgetConfig.setId(6621);
		businessChatWidgetConfig.setBusinessId(145253713345512L);
		businessChatWidgetConfig.setWidgetName("Default Widget");
		businessChatWidgetConfig.setChatTheme("Floating");
		businessChatWidgetConfig.setEnableReplyInBusinessHr(1);
		businessChatWidgetConfig.setEnableReplyPostBusinessHr(1);
		businessChatWidgetConfig.setLivechatEnabled(0);
		return businessChatWidgetConfig;
	}

	private CustomFieldsWidget mockCustomFieldsWidget() {
		CustomFieldsWidget customFieldsWidget = new CustomFieldsWidget();
		customFieldsWidget.setId(126411);
		customFieldsWidget.setWidgetId(6621);
		customFieldsWidget.setBusinessId(145253713345512L);
		customFieldsWidget.setFieldName("Current Insurance Plan");
		customFieldsWidget.setFieldType("text");
		customFieldsWidget.setMandatory(true);
		customFieldsWidget.setFieldSubType("dropdown");
		String subTypeValues = ControllerUtil
				.encodeToString(List.of("Personal Accident Cover", "Comprehensive Coverage", "No Current Plan"));
		customFieldsWidget.setSubTypeValues(subTypeValues);
		return customFieldsWidget;
	}

	private CustomFieldsWidget mockCustomField2() {
		CustomFieldsWidget customFieldsWidget = new CustomFieldsWidget();
		customFieldsWidget.setId(126412);
		customFieldsWidget.setWidgetId(6621);
		customFieldsWidget.setBusinessId(145253713345512L);
		customFieldsWidget.setFieldName("Vehicle");
		customFieldsWidget.setFieldType("text");
		customFieldsWidget.setMandatory(true);
		customFieldsWidget.setFieldSubType("dropdown");
		String subTypeValues = ControllerUtil.encodeToString(List.of("Two wheeler", "Four Wheeler", "Both"));
		customFieldsWidget.setSubTypeValues(subTypeValues);
		return customFieldsWidget;
	}

	private CustomFieldsWidget mockCustomField3() {
		CustomFieldsWidget customFieldsWidget = new CustomFieldsWidget();
		customFieldsWidget.setId(126413);
		customFieldsWidget.setWidgetId(6621);
		customFieldsWidget.setBusinessId(145253713345512L);
		customFieldsWidget.setFieldName("Age");
		customFieldsWidget.setFieldType("text");
		customFieldsWidget.setMandatory(true);
		return customFieldsWidget;
	}

	private LiveChatWidgetConfig mockLiveChatWidgetConfig() {
		LiveChatWidgetConfig liveChatWidgetConfig = new LiveChatWidgetConfig();
		liveChatWidgetConfig.setId(4032);
		liveChatWidgetConfig.setWidgetId(6621);
		return liveChatWidgetConfig;
	}
}
