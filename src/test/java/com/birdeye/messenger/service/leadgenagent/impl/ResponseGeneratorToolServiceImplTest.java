package com.birdeye.messenger.service.leadgenagent.impl;

import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.agent.AgentRule;
import com.birdeye.messenger.dao.entity.agent.RuleAction;
import com.birdeye.messenger.dao.entity.agent.RuleCondition;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.leadgenagent.ResponseGeneratorToolRequestDTO;
import com.birdeye.messenger.dto.leadgenagent.ResponseGeneratorToolResponseDTO;
import com.birdeye.messenger.enums.ConditionField;
import com.birdeye.messenger.enums.ResponseGenerationActionsTypeEnum;
import com.birdeye.messenger.enums.RuleActionType;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ChatbotService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.NLPService;
import com.birdeye.messenger.service.AgentRuleService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.SmsService;
import com.birdeye.messenger.service.leadgenagent.ResponseOptimiserToolService;
import com.birdeye.messenger.sro.BusinessTimingDTO;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ResponseGeneratorToolServiceImplTest {

    @Mock
    private AgentRuleService agentRuleService;

    @Mock
    private ResponseOptimiserToolService responseOptimiserToolService;

    @Mock
    private BusinessService businessService;

    @Mock
    private ContactService contactService;

    @Mock
    private MessengerContactService messengerContactService;

    @Mock
    private ChatbotService chatbotService;

    @Mock
    private NLPService nlpService;

    @Mock
    private SmsService smsService;

    @InjectMocks
    private ResponseGeneratorToolServiceImpl responseGeneratorToolService;

    private ResponseGeneratorToolRequestDTO.MetaData metaData;
    private BusinessDTO businessDTO;
    private CustomerDTO customerDTO;
    private MessengerContact messengerContact;
    private BusinessTimingDTO businessTimingDTO;
    private JsonNode configNode;

    @BeforeEach
    void setUp() {
        // Setup common test data
        metaData = ResponseGeneratorToolRequestDTO.MetaData.builder()
                .businessId(1001)
                .accountId(2001)
                .userId(3001)
                .mcId(4001)
                .msgId("msg-123")
                .requestId("req-456")
                .customerId(5001)
                .build();

        businessDTO = new BusinessDTO();
        businessDTO.setBusinessId(1001);
        businessDTO.setAccountId(2001);
        businessDTO.setEnterpriseName("Test Enterprise");
        businessDTO.setBusinessName("Test Business");

        customerDTO = new CustomerDTO();
        customerDTO.setId(5001);
        customerDTO.setFirstName("John");
        customerDTO.setLastName("Doe");

        messengerContact = new MessengerContact();
        messengerContact.setId(4001);
        messengerContact.setCustomerId(5001);

        businessTimingDTO = new BusinessTimingDTO();

        // Setup JSON config node
        ObjectMapper mapper = new ObjectMapper();
        configNode = mapper.createObjectNode().put("test", "config");

        // Setup common mocks
        when(businessService.getBusinessDTO(anyInt())).thenReturn(businessDTO);
        when(contactService.getCustomerDTO(anyInt())).thenReturn(customerDTO);
        when(messengerContactService.findById(anyInt())).thenReturn(messengerContact);
        when(responseOptimiserToolService.getResponseOptimizationChannelBased(anyString(), anyInt(), anyInt()))
                .thenReturn(configNode);
    }

    @Test
    void testGenerateResponse_BothIntentAndSentiment_ExactMatch() {
        // Arrange
        String agentId = "agent-123";
        String intent = "greeting";
        String sentiment = "positive";
        
        ResponseGeneratorToolRequestDTO request = ResponseGeneratorToolRequestDTO.builder()
                .agentId(agentId)
                .intent(intent)
                .sentiment(sentiment)
                .message_body("Hello there!")
                .channel(1)
                .metaData(metaData)
                .build();

        // Create rule that matches both intent and sentiment exactly
        AgentRule exactMatchRule = createRule(1L, "Exact Match Rule", 
                Arrays.asList(
                    createCondition(ConditionField.INTENT, "is", "greeting"),
                    createCondition(ConditionField.SENTIMENT, "is", "positive")
                ),
                "0 && 1",
                Arrays.asList(createAction(RuleActionType.CUSTOM_MESSAGE, "Welcome! How can I help you?")));

        when(agentRuleService.getRuleExpressionByAgentId(agentId))
                .thenReturn(Arrays.asList(exactMatchRule));

        // Act
        ResponseGeneratorToolResponseDTO response = responseGeneratorToolService
                .generateResponseBasedOnIntentAndSentiment(request);

        // Assert
        assertNotNull(response);
        assertEquals("Welcome! How can I help you?", response.getMessageBody());
        assertEquals(ResponseGenerationActionsTypeEnum.CUSTOM_MESSAGE.getValue(), response.getResponseType());
        assertEquals(metaData.getMcId(), response.getMcId());
        assertEquals(configNode, response.getConfig());
    }

    @Test
    void testGenerateResponse_BothIntentAndSentiment_NoExactMatch_IntentOnlyMatch() {
        // Arrange
        String agentId = "agent-123";
        String intent = "greeting";
        String sentiment = "negative";
        
        ResponseGeneratorToolRequestDTO request = ResponseGeneratorToolRequestDTO.builder()
                .agentId(agentId)
                .intent(intent)
                .sentiment(sentiment)
                .message_body("Hello there!")
                .channel(1)
                .metaData(metaData)
                .build();

        // Create rules: no exact match, but intent-only match exists
        AgentRule exactMatchRule = createRule(1L, "Exact Match Rule", 
                Arrays.asList(
                    createCondition(ConditionField.INTENT, "is", "greeting"),
                    createCondition(ConditionField.SENTIMENT, "is", "positive")
                ),
                "0 && 1",
                Arrays.asList(createAction(RuleActionType.CUSTOM_MESSAGE, "Positive greeting response")));

        AgentRule intentOnlyRule = createRule(2L, "Intent Only Rule", 
                Arrays.asList(createCondition(ConditionField.INTENT, "is", "greeting")),
                "0",
                Arrays.asList(createAction(RuleActionType.CUSTOM_MESSAGE, "General greeting response")));

        when(agentRuleService.getRuleExpressionByAgentId(agentId))
                .thenReturn(Arrays.asList(exactMatchRule, intentOnlyRule));

        // Act
        ResponseGeneratorToolResponseDTO response = responseGeneratorToolService
                .generateResponseBasedOnIntentAndSentiment(request);

        // Assert
        assertNotNull(response);
        assertEquals("General greeting response", response.getMessageBody());
        assertEquals(ResponseGenerationActionsTypeEnum.CUSTOM_MESSAGE.getValue(), response.getResponseType());
    }

    @Test
    void testGenerateResponse_BothIntentAndSentiment_NoIntentMatch_SentimentOnlyMatch() {
        // Arrange
        String agentId = "agent-123";
        String intent = "complaint";
        String sentiment = "negative";
        
        ResponseGeneratorToolRequestDTO request = ResponseGeneratorToolRequestDTO.builder()
                .agentId(agentId)
                .intent(intent)
                .sentiment(sentiment)
                .message_body("I'm not happy!")
                .channel(1)
                .metaData(metaData)
                .build();

        // Create rules: no exact match, no intent match, but sentiment-only match exists
        AgentRule exactMatchRule = createRule(1L, "Exact Match Rule", 
                Arrays.asList(
                    createCondition(ConditionField.INTENT, "is", "greeting"),
                    createCondition(ConditionField.SENTIMENT, "is", "positive")
                ),
                "0 && 1",
                Arrays.asList(createAction(RuleActionType.CUSTOM_MESSAGE, "Positive greeting response")));

        AgentRule intentOnlyRule = createRule(2L, "Intent Only Rule", 
                Arrays.asList(createCondition(ConditionField.INTENT, "is", "greeting")),
                "0",
                Arrays.asList(createAction(RuleActionType.CUSTOM_MESSAGE, "General greeting response")));

        AgentRule sentimentOnlyRule = createRule(3L, "Sentiment Only Rule", 
                Arrays.asList(createCondition(ConditionField.SENTIMENT, "is", "negative")),
                "0",
                Arrays.asList(createAction(RuleActionType.CUSTOM_MESSAGE, "I understand you're upset")));

        when(agentRuleService.getRuleExpressionByAgentId(agentId))
                .thenReturn(Arrays.asList(exactMatchRule, intentOnlyRule, sentimentOnlyRule));

        // Act
        ResponseGeneratorToolResponseDTO response = responseGeneratorToolService
                .generateResponseBasedOnIntentAndSentiment(request);

        // Assert
        assertNotNull(response);
        assertEquals("I understand you're upset", response.getMessageBody());
        assertEquals(ResponseGenerationActionsTypeEnum.CUSTOM_MESSAGE.getValue(), response.getResponseType());
    }

    @Test
    void testGenerateResponse_OnlyIntent_ExactMatch() {
        // Arrange
        String agentId = "agent-123";
        String intent = "pricing";
        
        ResponseGeneratorToolRequestDTO request = ResponseGeneratorToolRequestDTO.builder()
                .agentId(agentId)
                .intent(intent)
                .sentiment(null) // Only intent provided
                .message_body("What are your prices?")
                .channel(1)
                .metaData(metaData)
                .build();

        // Create rule that matches intent only
        AgentRule intentOnlyRule = createRule(1L, "Intent Only Rule", 
                Arrays.asList(createCondition(ConditionField.INTENT, "is", "pricing")),
                "0",
                Arrays.asList(createAction(RuleActionType.CUSTOM_MESSAGE, "Here are our pricing details")));

        when(agentRuleService.getRuleExpressionByAgentId(agentId))
                .thenReturn(Arrays.asList(intentOnlyRule));

        // Act
        ResponseGeneratorToolResponseDTO response = responseGeneratorToolService
                .generateResponseBasedOnIntentAndSentiment(request);

        // Assert
        assertNotNull(response);
        assertEquals("Here are our pricing details", response.getMessageBody());
        assertEquals(ResponseGenerationActionsTypeEnum.CUSTOM_MESSAGE.getValue(), response.getResponseType());
    }

    @Test
    void testGenerateResponse_OnlySentiment_ExactMatch() {
        // Arrange
        String agentId = "agent-123";
        String sentiment = "frustrated";
        
        ResponseGeneratorToolRequestDTO request = ResponseGeneratorToolRequestDTO.builder()
                .agentId(agentId)
                .intent(null) // Only sentiment provided
                .sentiment(sentiment)
                .message_body("This is so annoying!")
                .channel(1)
                .metaData(metaData)
                .build();

        // Create rule that matches sentiment only
        AgentRule sentimentOnlyRule = createRule(1L, "Sentiment Only Rule", 
                Arrays.asList(createCondition(ConditionField.SENTIMENT, "is", "frustrated")),
                "0",
                Arrays.asList(createAction(RuleActionType.CUSTOM_MESSAGE, "I apologize for the frustration")));

        when(agentRuleService.getRuleExpressionByAgentId(agentId))
                .thenReturn(Arrays.asList(sentimentOnlyRule));

        // Act
        ResponseGeneratorToolResponseDTO response = responseGeneratorToolService
                .generateResponseBasedOnIntentAndSentiment(request);

        // Assert
        assertNotNull(response);
        assertEquals("I apologize for the frustration", response.getMessageBody());
        assertEquals(ResponseGenerationActionsTypeEnum.CUSTOM_MESSAGE.getValue(), response.getResponseType());
    }

    // Helper methods for creating test data
    private AgentRule createRule(Long id, String description, List<RuleCondition> conditions, 
                                String conditionLogic, List<RuleAction> actions) {
        AgentRule rule = AgentRule.builder()
                .id(id)
                .agentId("agent-123")
                .accountId(2001)
                .ruleDescription(description)
                .conditionLogic(conditionLogic)
                .ruleExpression(generateMvelExpression(conditionLogic, conditions))
                .isDefaultRule(false)
                .conditions(new ArrayList<>())
                .actions(new ArrayList<>())
                .build();
        
        rule.setConditions(conditions);
        rule.setActions(actions);
        
        return rule;
    }

    private RuleCondition createCondition(ConditionField field, String operator, String value) {
        return RuleCondition.builder()
                .field(field)
                .operator(operator)
                .value(value)
                .build();
    }

    private RuleAction createAction(RuleActionType type, String message) {
        return RuleAction.builder()
                .type(type)
                .message(message)
                .build();
    }

    private String generateMvelExpression(String conditionLogic, List<RuleCondition> conditions) {
        // Simple MVEL expression generation for testing
        String mvel = conditionLogic;
        for (int i = 0; i < conditions.size(); i++) {
            RuleCondition condition = conditions.get(i);
            String field = condition.getField().getValue();
            String value = condition.getValue();
            String expression = String.format("(%s == '%s')", field, value.toLowerCase());
            mvel = mvel.replace(String.valueOf(i), expression);
        }
        return mvel;
    }
}
