package com.birdeye.messenger.service.leadgenagent.impl;

import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.agent.AgentRule;
import com.birdeye.messenger.dao.entity.agent.RuleAction;
import com.birdeye.messenger.dao.entity.agent.RuleCondition;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.leadgenagent.ResponseGeneratorToolRequestDTO;
import com.birdeye.messenger.dto.leadgenagent.ResponseGeneratorToolResponseDTO;
import com.birdeye.messenger.enums.ConditionField;
import com.birdeye.messenger.enums.ResponseGenerationActionsTypeEnum;
import com.birdeye.messenger.enums.RuleActionType;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ChatbotService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.NLPService;
import com.birdeye.messenger.service.AgentRuleService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.SmsService;
import com.birdeye.messenger.service.leadgenagent.ResponseOptimiserToolService;
import com.birdeye.messenger.sro.BusinessTimingDTO;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class ResponseGeneratorToolServiceImplTest {

    @Mock
    private AgentRuleService agentRuleService;

    @Mock
    private ResponseOptimiserToolService responseOptimiserToolService;

    @Mock
    private BusinessService businessService;

    @Mock
    private ContactService contactService;

    @Mock
    private MessengerContactService messengerContactService;

    @Mock
    private ChatbotService chatbotService;

    @Mock
    private NLPService nlpService;

    @Mock
    private SmsService smsService;

    @InjectMocks
    private ResponseGeneratorToolServiceImpl responseGeneratorToolService;

    private ResponseGeneratorToolRequestDTO.MetaData metaData;
    private BusinessDTO businessDTO;
    private CustomerDTO customerDTO;
    private MessengerContact messengerContact;
    private BusinessTimingDTO businessTimingDTO;
    private JsonNode configNode;

    @BeforeEach
    void setUp() {
        // Setup common test data
        metaData = ResponseGeneratorToolRequestDTO.MetaData.builder()
                .businessId(1001)
                .accountId(2001)
                .userId(3001)
                .mcId(4001)
                .msgId("msg-123")
                .requestId("req-456")
                .customerId(5001)
                .build();

        businessDTO = new BusinessDTO();
        businessDTO.setBusinessId(1001);
        businessDTO.setEnterpriseId(2001);
        businessDTO.setEnterpriseName("Test Enterprise");
        businessDTO.setBusinessName("Test Business");

        customerDTO = new CustomerDTO();
        customerDTO.setId(5001);
        customerDTO.setFirstName("John");
        customerDTO.setLastName("Doe");

        messengerContact = new MessengerContact();
        messengerContact.setId(4001);
        messengerContact.setCustomerId(5001);

        businessTimingDTO = new BusinessTimingDTO();

        // Setup JSON config node
        ObjectMapper mapper = new ObjectMapper();
        configNode = mapper.createObjectNode().put("test", "config");

        // Setup common mocks
        when(businessService.getBusinessDTO(anyInt())).thenReturn(businessDTO);
        when(contactService.findById(anyInt())).thenReturn(customerDTO);
        when(messengerContactService.findById(anyInt())).thenReturn(messengerContact);
        when(responseOptimiserToolService.getResponseOptimizationChannelBased(anyString(), anyInt(), anyInt()))
                .thenReturn(configNode);
    }

    @Test
    void testGenerateResponse_BothIntentAndSentiment_ExactMatch() {
        // Arrange
        String agentId = "agent-123";
        String intent = "greeting";
        String sentiment = "positive";
        
        ResponseGeneratorToolRequestDTO request = ResponseGeneratorToolRequestDTO.builder()
                .agentId(agentId)
                .intent(intent)
                .sentiment(sentiment)
                .message_body("Hello there!")
                .channel(1)
                .metaData(metaData)
                .build();

        // Create rule that matches both intent and sentiment exactly
        AgentRule exactMatchRule = createRule(1L, "Exact Match Rule", 
                Arrays.asList(
                    createCondition(ConditionField.INTENT, "is", "greeting"),
                    createCondition(ConditionField.SENTIMENT, "is", "positive")
                ),
                "0 && 1",
                Arrays.asList(createAction(RuleActionType.CUSTOM_MESSAGE, "Welcome! How can I help you?")));

        when(agentRuleService.getRuleExpressionByAgentId(agentId))
                .thenReturn(Arrays.asList(exactMatchRule));

        // Act
        ResponseGeneratorToolResponseDTO response = responseGeneratorToolService
                .generateResponseBasedOnIntentAndSentiment(request);

        // Assert
        assertNotNull(response);
        assertEquals("Welcome! How can I help you?", response.getMessageBody());
        assertEquals(ResponseGenerationActionsTypeEnum.CUSTOM_MESSAGE.getValue(), response.getResponseType());
        assertEquals(metaData.getMcId(), response.getMcId());
        assertEquals(configNode, response.getConfig());
    }

    @Test
    void testGenerateResponse_BothIntentAndSentiment_NoExactMatch_IntentOnlyMatch() {
        // Arrange
        String agentId = "agent-123";
        String intent = "greeting";
        String sentiment = "negative";
        
        ResponseGeneratorToolRequestDTO request = ResponseGeneratorToolRequestDTO.builder()
                .agentId(agentId)
                .intent(intent)
                .sentiment(sentiment)
                .message_body("Hello there!")
                .channel(1)
                .metaData(metaData)
                .build();

        // Create rules: no exact match, but intent-only match exists
        AgentRule exactMatchRule = createRule(1L, "Exact Match Rule", 
                Arrays.asList(
                    createCondition(ConditionField.INTENT, "is", "greeting"),
                    createCondition(ConditionField.SENTIMENT, "is", "positive")
                ),
                "0 && 1",
                Arrays.asList(createAction(RuleActionType.CUSTOM_MESSAGE, "Positive greeting response")));

        AgentRule intentOnlyRule = createRule(2L, "Intent Only Rule", 
                Arrays.asList(createCondition(ConditionField.INTENT, "is", "greeting")),
                "0",
                Arrays.asList(createAction(RuleActionType.CUSTOM_MESSAGE, "General greeting response")));

        when(agentRuleService.getRuleExpressionByAgentId(agentId))
                .thenReturn(Arrays.asList(exactMatchRule, intentOnlyRule));

        // Act
        ResponseGeneratorToolResponseDTO response = responseGeneratorToolService
                .generateResponseBasedOnIntentAndSentiment(request);

        // Assert
        assertNotNull(response);
        assertEquals("General greeting response", response.getMessageBody());
        assertEquals(ResponseGenerationActionsTypeEnum.CUSTOM_MESSAGE.getValue(), response.getResponseType());
    }

    @Test
    void testGenerateResponse_BothIntentAndSentiment_NoIntentMatch_SentimentOnlyMatch() {
        // Arrange
        String agentId = "agent-123";
        String intent = "complaint";
        String sentiment = "negative";
        
        ResponseGeneratorToolRequestDTO request = ResponseGeneratorToolRequestDTO.builder()
                .agentId(agentId)
                .intent(intent)
                .sentiment(sentiment)
                .message_body("I'm not happy!")
                .channel(1)
                .metaData(metaData)
                .build();

        // Create rules: no exact match, no intent match, but sentiment-only match exists
        AgentRule exactMatchRule = createRule(1L, "Exact Match Rule", 
                Arrays.asList(
                    createCondition(ConditionField.INTENT, "is", "greeting"),
                    createCondition(ConditionField.SENTIMENT, "is", "positive")
                ),
                "0 && 1",
                Arrays.asList(createAction(RuleActionType.CUSTOM_MESSAGE, "Positive greeting response")));

        AgentRule intentOnlyRule = createRule(2L, "Intent Only Rule", 
                Arrays.asList(createCondition(ConditionField.INTENT, "is", "greeting")),
                "0",
                Arrays.asList(createAction(RuleActionType.CUSTOM_MESSAGE, "General greeting response")));

        AgentRule sentimentOnlyRule = createRule(3L, "Sentiment Only Rule", 
                Arrays.asList(createCondition(ConditionField.SENTIMENT, "is", "negative")),
                "0",
                Arrays.asList(createAction(RuleActionType.CUSTOM_MESSAGE, "I understand you're upset")));

        when(agentRuleService.getRuleExpressionByAgentId(agentId))
                .thenReturn(Arrays.asList(exactMatchRule, intentOnlyRule, sentimentOnlyRule));

        // Act
        ResponseGeneratorToolResponseDTO response = responseGeneratorToolService
                .generateResponseBasedOnIntentAndSentiment(request);

        // Assert
        assertNotNull(response);
        assertEquals("I understand you're upset", response.getMessageBody());
        assertEquals(ResponseGenerationActionsTypeEnum.CUSTOM_MESSAGE.getValue(), response.getResponseType());
    }

    @Test
    void testGenerateResponse_OnlyIntent_ExactMatch() {
        // Arrange
        String agentId = "agent-123";
        String intent = "pricing";
        
        ResponseGeneratorToolRequestDTO request = ResponseGeneratorToolRequestDTO.builder()
                .agentId(agentId)
                .intent(intent)
                .sentiment(null) // Only intent provided
                .message_body("What are your prices?")
                .channel(1)
                .metaData(metaData)
                .build();

        // Create rule that matches intent only
        AgentRule intentOnlyRule = createRule(1L, "Intent Only Rule", 
                Arrays.asList(createCondition(ConditionField.INTENT, "is", "pricing")),
                "0",
                Arrays.asList(createAction(RuleActionType.CUSTOM_MESSAGE, "Here are our pricing details")));

        when(agentRuleService.getRuleExpressionByAgentId(agentId))
                .thenReturn(Arrays.asList(intentOnlyRule));

        // Act
        ResponseGeneratorToolResponseDTO response = responseGeneratorToolService
                .generateResponseBasedOnIntentAndSentiment(request);

        // Assert
        assertNotNull(response);
        assertEquals("Here are our pricing details", response.getMessageBody());
        assertEquals(ResponseGenerationActionsTypeEnum.CUSTOM_MESSAGE.getValue(), response.getResponseType());
    }

    @Test
    void testGenerateResponse_OnlySentiment_ExactMatch() {
        // Arrange
        String agentId = "agent-123";
        String sentiment = "frustrated";
        
        ResponseGeneratorToolRequestDTO request = ResponseGeneratorToolRequestDTO.builder()
                .agentId(agentId)
                .intent(null) // Only sentiment provided
                .sentiment(sentiment)
                .message_body("This is so annoying!")
                .channel(1)
                .metaData(metaData)
                .build();

        // Create rule that matches sentiment only
        AgentRule sentimentOnlyRule = createRule(1L, "Sentiment Only Rule", 
                Arrays.asList(createCondition(ConditionField.SENTIMENT, "is", "frustrated")),
                "0",
                Arrays.asList(createAction(RuleActionType.CUSTOM_MESSAGE, "I apologize for the frustration")));

        when(agentRuleService.getRuleExpressionByAgentId(agentId))
                .thenReturn(Arrays.asList(sentimentOnlyRule));

        // Act
        ResponseGeneratorToolResponseDTO response = responseGeneratorToolService
                .generateResponseBasedOnIntentAndSentiment(request);

        // Assert
        assertNotNull(response);
        assertEquals("I apologize for the frustration", response.getMessageBody());
        assertEquals(ResponseGenerationActionsTypeEnum.CUSTOM_MESSAGE.getValue(), response.getResponseType());
    }

    // Helper methods for creating test data
    private AgentRule createRule(Long id, String description, List<RuleCondition> conditions, 
                                String conditionLogic, List<RuleAction> actions) {
        AgentRule rule = AgentRule.builder()
                .id(id)
                .agentId("agent-123")
                .accountId(2001)
                .ruleDescription(description)
                .conditionLogic(conditionLogic)
                .ruleExpression(generateMvelExpression(conditionLogic, conditions))
                .isDefaultRule(false)
                .conditions(new ArrayList<>())
                .actions(new ArrayList<>())
                .build();
        
        rule.setConditions(conditions);
        rule.setActions(actions);
        
        return rule;
    }

    private RuleCondition createCondition(ConditionField field, String operator, String value) {
        return RuleCondition.builder()
                .field(field)
                .operator(operator)
                .value(value)
                .build();
    }

    private RuleAction createAction(RuleActionType type, String message) {
        return RuleAction.builder()
                .type(type)
                .message(message)
                .build();
    }

    @Test
    void testGenerateResponse_NoRulesFound_EmptyResponse() {
        // Arrange
        String agentId = "agent-123";

        ResponseGeneratorToolRequestDTO request = ResponseGeneratorToolRequestDTO.builder()
                .agentId(agentId)
                .intent("greeting")
                .sentiment("positive")
                .message_body("Hello!")
                .channel(1)
                .metaData(metaData)
                .build();

        when(agentRuleService.getRuleExpressionByAgentId(agentId))
                .thenReturn(Collections.emptyList());

        // Act
        ResponseGeneratorToolResponseDTO response = responseGeneratorToolService
                .generateResponseBasedOnIntentAndSentiment(request);

        // Assert
        assertNotNull(response);
        assertNull(response.getMessageBody());
        assertNull(response.getResponseType());
        assertNull(response.getMcId()); // Should be null when no metadata
        assertNull(response.getConfig()); // Should be null when no metadata
    }

    @Test
    void testGenerateResponse_NoMatchingRules_EmptyResponse() {
        // Arrange
        String agentId = "agent-123";

        ResponseGeneratorToolRequestDTO request = ResponseGeneratorToolRequestDTO.builder()
                .agentId(agentId)
                .intent("unknown_intent")
                .sentiment("unknown_sentiment")
                .message_body("Random message")
                .channel(1)
                .metaData(metaData)
                .build();

        // Create rule that doesn't match the request
        AgentRule nonMatchingRule = createRule(1L, "Non-matching Rule",
                Arrays.asList(createCondition(ConditionField.INTENT, "is", "greeting")),
                "0",
                Arrays.asList(createAction(RuleActionType.CUSTOM_MESSAGE, "Hello!")));

        when(agentRuleService.getRuleExpressionByAgentId(agentId))
                .thenReturn(Arrays.asList(nonMatchingRule));

        // Act
        ResponseGeneratorToolResponseDTO response = responseGeneratorToolService
                .generateResponseBasedOnIntentAndSentiment(request);

        // Assert
        assertNotNull(response);
        assertNull(response.getMessageBody());
        assertNull(response.getResponseType());
    }

    @Test
    void testGenerateResponse_NullIntentAndSentiment_EmptyResponse() {
        // Arrange
        String agentId = "agent-123";

        ResponseGeneratorToolRequestDTO request = ResponseGeneratorToolRequestDTO.builder()
                .agentId(agentId)
                .intent(null)
                .sentiment(null)
                .message_body("Message without intent or sentiment")
                .channel(1)
                .metaData(metaData)
                .build();

        AgentRule rule = createRule(1L, "Some Rule",
                Arrays.asList(createCondition(ConditionField.INTENT, "is", "greeting")),
                "0",
                Arrays.asList(createAction(RuleActionType.CUSTOM_MESSAGE, "Hello!")));

        when(agentRuleService.getRuleExpressionByAgentId(agentId))
                .thenReturn(Arrays.asList(rule));

        // Act
        ResponseGeneratorToolResponseDTO response = responseGeneratorToolService
                .generateResponseBasedOnIntentAndSentiment(request);

        // Assert
        assertNotNull(response);
        assertNull(response.getMessageBody());
        assertNull(response.getResponseType());
    }

    @Test
    void testGenerateResponse_EmptyIntentAndSentiment_EmptyResponse() {
        // Arrange
        String agentId = "agent-123";

        ResponseGeneratorToolRequestDTO request = ResponseGeneratorToolRequestDTO.builder()
                .agentId(agentId)
                .intent("")
                .sentiment("")
                .message_body("Message with empty intent and sentiment")
                .channel(1)
                .metaData(metaData)
                .build();

        AgentRule rule = createRule(1L, "Some Rule",
                Arrays.asList(createCondition(ConditionField.INTENT, "is", "greeting")),
                "0",
                Arrays.asList(createAction(RuleActionType.CUSTOM_MESSAGE, "Hello!")));

        when(agentRuleService.getRuleExpressionByAgentId(agentId))
                .thenReturn(Arrays.asList(rule));

        // Act
        ResponseGeneratorToolResponseDTO response = responseGeneratorToolService
                .generateResponseBasedOnIntentAndSentiment(request);

        // Assert
        assertNotNull(response);
        assertNull(response.getMessageBody());
        assertNull(response.getResponseType());
    }

    @Test
    void testGenerateResponse_AIPromptAction() {
        // Arrange
        String agentId = "agent-123";
        String intent = "complex_query";

        ResponseGeneratorToolRequestDTO request = ResponseGeneratorToolRequestDTO.builder()
                .agentId(agentId)
                .intent(intent)
                .sentiment("neutral")
                .message_body("Can you explain quantum physics?")
                .channel(1)
                .metaData(metaData)
                .build();

        AgentRule aiPromptRule = createRule(1L, "AI Prompt Rule",
                Arrays.asList(createCondition(ConditionField.INTENT, "is", "complex_query")),
                "0",
                Arrays.asList(createAction(RuleActionType.AI_PROMPT, "Use AI to answer complex questions")));

        when(agentRuleService.getRuleExpressionByAgentId(agentId))
                .thenReturn(Arrays.asList(aiPromptRule));
        when(nlpService.getAIReplyUsingPrompt(any())).thenReturn("AI generated response about quantum physics");

        // Act
        ResponseGeneratorToolResponseDTO response = responseGeneratorToolService
                .generateResponseBasedOnIntentAndSentiment(request);

        // Assert
        assertNotNull(response);
        assertEquals("AI generated response about quantum physics", response.getMessageBody());
        assertEquals(ResponseGenerationActionsTypeEnum.AI_PROMPT.getValue(), response.getResponseType());
        verify(nlpService).getAIReplyUsingPrompt(any());
    }

    @Test
    void testGenerateResponse_KnowledgeBaseAction() {
        // Arrange
        String agentId = "agent-123";
        String intent = "faq";

        ResponseGeneratorToolRequestDTO request = ResponseGeneratorToolRequestDTO.builder()
                .agentId(agentId)
                .intent(intent)
                .sentiment("neutral")
                .message_body("What are your business hours?")
                .channel(1)
                .metaData(metaData)
                .build();

        AgentRule knowledgeBaseRule = createRule(1L, "Knowledge Base Rule",
                Arrays.asList(createCondition(ConditionField.INTENT, "is", "faq")),
                "0",
                Arrays.asList(createAction(RuleActionType.KNOWLEDGE_BASE, "Search knowledge base")));

        when(agentRuleService.getRuleExpressionByAgentId(agentId))
                .thenReturn(Arrays.asList(knowledgeBaseRule));
        when(chatbotService.getAIReplyUsingKnowledgeBase(anyInt(), anyInt(), anyString(), any(), anyInt(), anyInt(), any(), any()))
                .thenReturn("Our business hours are 9 AM to 5 PM");

        // Act
        ResponseGeneratorToolResponseDTO response = responseGeneratorToolService
                .generateResponseBasedOnIntentAndSentiment(request);

        // Assert
        assertNotNull(response);
        assertEquals("Our business hours are 9 AM to 5 PM", response.getMessageBody());
        assertEquals(ResponseGenerationActionsTypeEnum.AI_ANSWER_USING_KNOWLEDGE_BASE.getValue(), response.getResponseType());
        verify(chatbotService).getAIReplyUsingKnowledgeBase(anyInt(), anyInt(), anyString(), any(), anyInt(), anyInt(), any(), any());
    }

    @Test
    void testGenerateResponse_FirstMatchingRuleIsUsed() {
        // Arrange
        String agentId = "agent-123";
        String intent = "greeting";

        ResponseGeneratorToolRequestDTO request = ResponseGeneratorToolRequestDTO.builder()
                .agentId(agentId)
                .intent(intent)
                .sentiment("positive")
                .message_body("Hello!")
                .channel(1)
                .metaData(metaData)
                .build();

        // Create multiple rules that could match - first one should be used
        AgentRule firstRule = createRule(1L, "First Rule",
                Arrays.asList(createCondition(ConditionField.INTENT, "is", "greeting")),
                "0",
                Arrays.asList(createAction(RuleActionType.CUSTOM_MESSAGE, "First response")));

        AgentRule secondRule = createRule(2L, "Second Rule",
                Arrays.asList(createCondition(ConditionField.INTENT, "is", "greeting")),
                "0",
                Arrays.asList(createAction(RuleActionType.CUSTOM_MESSAGE, "Second response")));

        when(agentRuleService.getRuleExpressionByAgentId(agentId))
                .thenReturn(Arrays.asList(firstRule, secondRule));

        // Act
        ResponseGeneratorToolResponseDTO response = responseGeneratorToolService
                .generateResponseBasedOnIntentAndSentiment(request);

        // Assert
        assertNotNull(response);
        assertEquals("First response", response.getMessageBody());
        assertEquals(ResponseGenerationActionsTypeEnum.CUSTOM_MESSAGE.getValue(), response.getResponseType());
    }

    @Test
    void testGenerateResponse_CaseInsensitiveMatching() {
        // Arrange
        String agentId = "agent-123";
        String intent = "GREETING"; // Uppercase intent
        String sentiment = "POSITIVE"; // Uppercase sentiment

        ResponseGeneratorToolRequestDTO request = ResponseGeneratorToolRequestDTO.builder()
                .agentId(agentId)
                .intent(intent)
                .sentiment(sentiment)
                .message_body("HELLO!")
                .channel(1)
                .metaData(metaData)
                .build();

        // Create rule with lowercase values
        AgentRule rule = createRule(1L, "Case Insensitive Rule",
                Arrays.asList(
                    createCondition(ConditionField.INTENT, "is", "greeting"),
                    createCondition(ConditionField.SENTIMENT, "is", "positive")
                ),
                "0 && 1",
                Arrays.asList(createAction(RuleActionType.CUSTOM_MESSAGE, "Case insensitive match!")));

        when(agentRuleService.getRuleExpressionByAgentId(agentId))
                .thenReturn(Arrays.asList(rule));

        // Act
        ResponseGeneratorToolResponseDTO response = responseGeneratorToolService
                .generateResponseBasedOnIntentAndSentiment(request);

        // Assert
        assertNotNull(response);
        assertEquals("Case insensitive match!", response.getMessageBody());
        assertEquals(ResponseGenerationActionsTypeEnum.CUSTOM_MESSAGE.getValue(), response.getResponseType());
    }

    @Test
    void testGenerateResponse_MultipleActions_OnlyFirstActionExecuted() {
        // Arrange
        String agentId = "agent-123";
        String intent = "greeting";

        ResponseGeneratorToolRequestDTO request = ResponseGeneratorToolRequestDTO.builder()
                .agentId(agentId)
                .intent(intent)
                .sentiment("positive")
                .message_body("Hello!")
                .channel(1)
                .metaData(metaData)
                .build();

        // Create rule with multiple actions
        AgentRule rule = createRule(1L, "Multiple Actions Rule",
                Arrays.asList(createCondition(ConditionField.INTENT, "is", "greeting")),
                "0",
                Arrays.asList(
                    createAction(RuleActionType.CUSTOM_MESSAGE, "First action"),
                    createAction(RuleActionType.CUSTOM_MESSAGE, "Second action")
                ));

        when(agentRuleService.getRuleExpressionByAgentId(agentId))
                .thenReturn(Arrays.asList(rule));

        // Act
        ResponseGeneratorToolResponseDTO response = responseGeneratorToolService
                .generateResponseBasedOnIntentAndSentiment(request);

        // Assert
        assertNotNull(response);
        // Note: Current implementation processes all actions, but the last one overwrites the response
        assertEquals("Second action", response.getMessageBody());
        assertEquals(ResponseGenerationActionsTypeEnum.CUSTOM_MESSAGE.getValue(), response.getResponseType());
    }

    @Test
    void testGenerateResponse_NullMetaData_HandlesGracefully() {
        // Arrange
        String agentId = "agent-123";
        String intent = "greeting";

        ResponseGeneratorToolRequestDTO request = ResponseGeneratorToolRequestDTO.builder()
                .agentId(agentId)
                .intent(intent)
                .sentiment("positive")
                .message_body("Hello!")
                .channel(1)
                .metaData(null) // Null metadata
                .build();

        AgentRule rule = createRule(1L, "Simple Rule",
                Arrays.asList(createCondition(ConditionField.INTENT, "is", "greeting")),
                "0",
                Arrays.asList(createAction(RuleActionType.CUSTOM_MESSAGE, "Hello response")));

        when(agentRuleService.getRuleExpressionByAgentId(agentId))
                .thenReturn(Arrays.asList(rule));

        // Act
        ResponseGeneratorToolResponseDTO response = responseGeneratorToolService
                .generateResponseBasedOnIntentAndSentiment(request);

        // Assert
        assertNotNull(response);
        assertEquals("Hello response", response.getMessageBody());
        assertEquals(ResponseGenerationActionsTypeEnum.CUSTOM_MESSAGE.getValue(), response.getResponseType());
        assertNull(response.getMcId());
        assertNull(response.getConfig());
    }

    @Test
    void testCustomGreetingMessageFromResponseGeneratorConfig() {
        // Arrange
        String agentId = "agent-123";

        AgentRule conversationStarterRule = createRule(1L, "Conversation Starter Rule",
                Arrays.asList(createCondition(ConditionField.INTENT, "is", "conversation starters")),
                "0",
                Arrays.asList(createAction(RuleActionType.CUSTOM_MESSAGE, "Welcome! How can I assist you today?")));

        when(agentRuleService.getRuleExpressionByAgentId(agentId))
                .thenReturn(Arrays.asList(conversationStarterRule));

        // Act
        String greetingMessage = responseGeneratorToolService.customGreetingMessageFromResponseGeneratorConfig(agentId);

        // Assert
        assertEquals("Welcome! How can I assist you today?", greetingMessage);
    }

    @Test
    void testCustomGreetingMessageFromResponseGeneratorConfig_NoMatchingRule_ReturnsNull() {
        // Arrange
        String agentId = "agent-123";

        AgentRule otherRule = createRule(1L, "Other Rule",
                Arrays.asList(createCondition(ConditionField.INTENT, "is", "pricing")),
                "0",
                Arrays.asList(createAction(RuleActionType.CUSTOM_MESSAGE, "Pricing info")));

        when(agentRuleService.getRuleExpressionByAgentId(agentId))
                .thenReturn(Arrays.asList(otherRule));

        // Act
        String greetingMessage = responseGeneratorToolService.customGreetingMessageFromResponseGeneratorConfig(agentId);

        // Assert
        assertNull(greetingMessage);
    }

    private String generateMvelExpression(String conditionLogic, List<RuleCondition> conditions) {
        // Simple MVEL expression generation for testing
        String mvel = conditionLogic;
        for (int i = 0; i < conditions.size(); i++) {
            RuleCondition condition = conditions.get(i);
            String field = condition.getField().getValue();
            String value = condition.getValue();
            String expression = String.format("(%s == '%s')", field, value.toLowerCase());
            mvel = mvel.replace(String.valueOf(i), expression);
        }
        return mvel;
    }
}
